import { execSync } from 'child_process'
import path from 'path'
import { message } from '../bin/common/utils'

const scriptName = path.basename(__filename)

/**
 * IBCを設定する
 */
export function setIBCApp(network: string, ibcApp: string, ibcAppName: string) {
  if (!network || !ibcApp || !ibcAppName) {
    message('err', `Missing arguments. network:${network} , ibcApp:${ibcApp} , ibcName:${ibcAppName}`)
    process.exit(1)
  }

  // Hardhat コマンド実行
  const command = `npx hardhat setIBCApp --ibcapp "${ibcApp}" --ibcapp-name "${ibcAppName}" --network "${network}"`

  try {
    execSync(command, { stdio: 'inherit' })
  } catch (err) {
    message('err', `Failed to execute Hardhat command: ${(err as Error).message}`)
    process.exit(1)
  }
}

// 直接実行用
if (require.main === module) {
  // 引数チェック
  const network = process.argv[2]
  const ibcApp = process.argv[3]
  const ibcAppName = process.argv[4]

  if (!network || !ibcApp || !ibcAppName) {
    message('err', 'Please specify the NETWORK as the first argument.')
    console.log(`npx ts-node tools/${scriptName} [NETWORK name] [IBCApp] [IBCApp name]`)
    process.exit(1)
  }

  setIBCApp(network, ibcApp, ibcAppName)
}
