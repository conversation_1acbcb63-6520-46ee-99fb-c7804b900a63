#!/bin/bash

if [ $# -ne 5 ]; then
  echo "usage:"
  echo "    bash tools/partialForceBurn.sh [issuerId] [accountId] [burnedAmount] [burnedBalance] [issuerKey]"
  exit
fi
if [ -z "${NETWORK}" ]; then
  echo "need var NETWORK"
  exit
fi

ISSUER_ID="$1"
ACCOUNT_ID="$2"
BURNED_AMOUNT="$3"
BURNED_BALANCE="$4"
ISSUER_KEY="$5"

npx hardhat partialForceBurnToken \
  --network ${NETWORK} \
  --issuer-id "${ISSUER_ID}" \
  --account-id "${ACCOUNT_ID}" \
  --burned-amount "${BURNED_AMOUNT}" \
  --burned-balance "${BURNED_BALANCE}" \
  --issuer-key "${ISSUER_KEY}"
