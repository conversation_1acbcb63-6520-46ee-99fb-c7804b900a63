/**
 * Hardhat用コンフィグファイル
 **/
import '@nomicfoundation/hardhat-ethers'
import '@nomicfoundation/hardhat-toolbox'
import '@nomiclabs/hardhat-truffle5'
import * as dotenv from 'dotenv'
import 'hardhat-contract-sizer'
import 'hardhat-deploy'
import 'hardhat-deploy-ethers'
import { HardhatUserConfig, HttpNetworkUserConfig } from 'hardhat/types'
import 'solidity-coverage'
import 'solidity-docgen'
import 'tsconfig-paths/register'
import { envVers } from './envVers'
import {} from './types/hardhat'

// task配下のカスタムタスクを設定
import * as PrivateKey from './privateKey'
import './tasks/index.ts'

dotenv.config()

// AWS_PROFILE 環境変数を取得
const awsProfile = envVers.aws.profile

// AWS_PROFILE 環境変数が設定されている場合は、値に応じたenvフォルダ下の環境変数を読み込む
if (awsProfile) {
  dotenv.config({ path: `./bin/main/env/.${awsProfile}` })
} else if (process.env.PROJECT_ENV) {
  dotenv.config({ path: `./bin/main/env/.${process.env.PROJECT_ENV}` })
}

dotenv.config({ path: '.kms' })

const getNetworkConfig = (func: string, privateKey: any): HttpNetworkUserConfig => {
  const baseConfig = {
    url: 'http://localhost:8451',
    accounts: privateKey.key,
    chainId: 5151,
    gasPrice: 0,
    timeout: *********, // (ms)= 100000s = 1666m = 27h
  }

  switch (func) {
    case 'env':
      return {
        ...baseConfig,
        url: envVers.provider.url || 'http://127.0.0.1:8451',
        accounts: [envVers.keyAdmin, privateKey.key[9]],
        chainId: parseInt(envVers.network.id),
      }
    case 'default':
      return {
        ...baseConfig,
      }
    case 'defaultFin':
      return {
        ...baseConfig,
        url: `http://localhost:${envVers.network.localFin.port}`,
        chainId: parseInt(envVers.network.localFin.networkId),
      }
    case 'defaultBiz':
      return {
        ...baseConfig,
        url: `http://localhost:${envVers.network.localBiz.port}`,
        chainId: parseInt(envVers.network.localBiz.networkId),
      }
    default:
      throw new Error(`Unknown function: ${func}`)
  }
}

const config: HardhatUserConfig = {
  solidity: {
    version: '0.8.12',
    settings: {
      optimizer: {
        enabled: true,
        runs: 200,
        details: {
          yul: true,
          yulDetails: {
            stackAllocation: true,
            optimizerSteps: 'dhfoDgvulfnTUtnIf',
          },
        },
      },
    },
  },
  networks: {
    mainFin: getNetworkConfig('env', PrivateKey),
    mainBiz: getNetworkConfig('env', PrivateKey),
    local: getNetworkConfig('default', PrivateKey),
    localFin: getNetworkConfig('defaultFin', PrivateKey),
    localBiz: getNetworkConfig('defaultBiz', PrivateKey),
    kms: {
      url: envVers.provider.url,
      chainId: 5151,
    },
  },
  namedAccounts: {
    deployer: 0,
    signer1: 1,
  },
  paths: {
    sources: 'contracts',
    tests: './test',
    artifacts: './build',
  },
  mocha: {
    timeout: 20000,
    reporter: 'mochawesome',
    reporterOptions: {
      reportDir: './report',
      reportFilename: 'index',
      quiet: true,
      json: false,
      html: true,
    },
  },
  typechain: {
    outDir: 'types',
    target: 'ethers-v6',
  },
}

config['docgen'] = {
  pages: 'files',
  outputDir: './docs',
  exclude: ['interfaces', 'mocks', 'renewableEnergyToken/interfaces'],
  // templates: './docs/templates',
}

BigInt.prototype['toJSON'] = function () {
  return this.toString()
}

export default config
