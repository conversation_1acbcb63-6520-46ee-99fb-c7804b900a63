import { expect } from 'chai'
import { Network, networkConfig } from './helpers/constants'
import './register_data.test'
import { checkSyncAccountUnauthorizedIssuer, synchronous } from './scenarios/12_synchronous'
import { industry } from './scenarios/13_industry'
import { charge } from './scenarios/14_charge'
import { syncTransfer } from './scenarios/15_sync_transfer'
import { discharge } from './scenarios/16_discharge'
import { dischargeFromFinScenario } from './scenarios/16_discharge_from_fin'
import { frozenForceBurn, partialForceBurn } from './scenarios/17_frozen_force_burn'
import { bizTerminating, checkTerminatingAccountBalanceNotZero } from './scenarios/18_biz_terminating'
import { bizTerminated } from './scenarios/19_biz_terminated'
import { deleteBizZoneToIssuer } from './scenarios/20_delete_bizzone_to_issuer'
import { emitAfterBalanceScenario } from './scenarios/23_emit_after_balance'
import { ERROR } from './scenarios/utils'

describe('Synchronous', function () {
  this.timeout(0)
  it('Check sync account linked to an unauthorized issuer', async function () {
    const output = await checkSyncAccountUnauthorizedIssuer(Network.LocalFin)
    expect(output).to.not.include(ERROR)
  })

  it('Sync account linked to an authorized issuer', async function () {
    const output = await synchronous(Network.LocalBiz)
    expect(output).to.not.include(ERROR)
  })
})

describe('Start account/industry scenario for localFin', function () {
  this.timeout(0)
  it('Start account/industry scenario run successfully', async function () {
    const output = await industry(Network.LocalFin)
    expect(output).to.not.include(ERROR)
  })
})

describe('Start token/charge scenario', function () {
  this.timeout(0)
  it('token/charge scenario run successfully', async function () {
    const output = await charge(Network.LocalFin, networkConfig[Network.LocalFin].ACCOUNT_ID_3)
    expect(output).to.not.include(ERROR)
  })
})

describe('Start token transfer in BizZone scenario', function () {
  this.timeout(0)
  it('token transfer in BizZone scenario run successfully', async function () {
    const output = await syncTransfer(
      Network.LocalBiz,
      networkConfig[Network.LocalBiz].ACCOUNT_ID_3,
      networkConfig[Network.LocalBiz].ACCOUNT_ID_4,
    )
    expect(output).to.not.include(ERROR)
  })
})

describe('Start token/discharge from FinZone scenario', function () {
  this.timeout(0)
  it('token/discharge from FinZone scenario run successfully', async function () {
    const output = await dischargeFromFinScenario()
    expect(output).to.not.include(ERROR)
  })
})

describe('Start token/discharge scenario', function () {
  this.timeout(0)
  it('Start token/discharge scenario run successfully', async function () {
    const output = await discharge(Network.LocalBiz)
    expect(output).to.not.include(ERROR)
  })
})

describe('Start frozen/forceBurn scenario', function () {
  this.timeout(0)
  it('frozen/forceBurn scenario run successfully', async function () {
    const frozenForceBurnForAccount3 = await frozenForceBurn(networkConfig[Network.LocalFin].ACCOUNT_ID_3)
    expect(frozenForceBurnForAccount3).to.not.include(ERROR)
    const frozenForceBurnForAccount4 = await frozenForceBurn(networkConfig[Network.LocalFin].ACCOUNT_ID_4)
    expect(frozenForceBurnForAccount4).to.not.include(ERROR)
  })
})

describe('Synchronous', function () {
  this.timeout(0)
  it('Start account/synchronous scenario', async function () {
    const output = await synchronous(Network.LocalBiz)
    expect(output).to.not.include(ERROR)
  })
})

describe('Start account/industry scenario for localFin', function () {
  this.timeout(0)
  it('Start account/industry scenario run successfully', async function () {
    const output = await industry(Network.LocalFin)
    expect(output).to.not.include(ERROR)
  })
})

describe('Start partial force burn scenario', function () {
  this.timeout(0)
  it('Partial force burn scenario run successfully', async function () {
    const output = await partialForceBurn()
    expect(output).to.not.include(ERROR)
  })
})

describe('Start biz/terminating scenario', function () {
  this.timeout(0)

  it('Check terminating Biz zone account balance not zero', async function () {
    const output = await checkTerminatingAccountBalanceNotZero(Network.LocalFin)
    expect(output).to.not.include(ERROR)
  })

  it('Biz Terminating Scenario run successfully', async function () {
    const output = await bizTerminating(Network.LocalBiz)
    expect(output).to.not.include(ERROR)
  })
})

describe('Start biz/terminated scenario', function () {
  this.timeout(0)
  it('Biz Terminated Scenario run successfully', async function () {
    const output = await bizTerminated(Network.LocalFin)
    expect(output).to.not.include(ERROR)
  })
})

describe('Start delete BizZone to issuer', function () {
  this.timeout(0)
  it('Delete BizZone to Issuer for localBiz run successfully', async function () {
    const output = await deleteBizZoneToIssuer()
    expect(output).to.not.include(ERROR)
  })
})

describe('Start emitAfterBalance scenario', function () {
  this.timeout(0)
  it('emitAfterBalance scenario run successfully', async function () {
    const output = await emitAfterBalanceScenario(Network.LocalFin)
    expect(output).to.not.include(ERROR)
  })
})
