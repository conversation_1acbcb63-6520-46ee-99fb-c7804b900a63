import { expect } from 'chai'
import { Network } from './helpers/constants'
import {
  fetchMax1000Accounts,
  fetch999Accounts,
  fetch1001stAccountOnSecondPage,
  registerMassAccount,
} from './scenarios/21_batch_handling'
import { ERROR } from './scenarios/utils'

describe('Paginated Account List - Batch Handling', function () {
  this.timeout(0)
  before(async () => {
    await registerMassAccount(Network.LocalFin, 1001)
  })

  it('Should retrieve max 1000 accounts per page', async function () {
    const output = await fetchMax1000Accounts(Network.LocalFin)
    expect(output).to.not.include(ERROR)
  })

  it('Should retrieve account 1001th on second page', async function () {
    const output = await fetch1001stAccountOnSecondPage(Network.LocalFin)
    expect(output).to.not.include(ERROR)
  })

  it('Should retrieve 999 accounts if limit is 999', async function () {
    const output = await fetch999Accounts(Network.LocalFin)
    expect(output).to.not.include(ERROR)
  })
})
