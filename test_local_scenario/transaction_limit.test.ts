import { expect } from 'chai'
import { commonConfig, Network, networkConfig } from './helpers/constants'
import { addBizZoneToIssuer } from './scenarios/11_reg_bizzone'
import { syncAccount } from './scenarios/12_synchronous'
import {
  addAccountWithLimit,
  addAccountWithoutLimit,
  checkExchangeExceedCumulativeChargeLimit,
  checkExchangeExceedCumulativeDisChargeLimit,
  checkExchangeNotExceedLimit,
  checkExchangeNotSetLimit,
  checkSync,
  checkTransactionExceedDailyLimit,
  checkTransactionFromAndToAccountIssuerAreDifferent,
  checkTransactionLimit,
  checkTransactionNotExceedDailyLimit,
  checkTransactionNotSetLimit,
  getAccountAll,
  getAccountInfo,
  getAccountLimit,
  modTokenLimit,
} from './scenarios/22_account_transaction_limit'
import { ERROR, extractAccountInfo, showAccountsStatus } from './scenarios/utils'
import { SetActiveBusinessAccountWithZoneTask } from './tasks/SetActiveBusinessAccountWithZoneTask'

describe('Start transaction limit scenario', function () {
  this.timeout(0)
  before(async () => {
    await addBizZoneToIssuer(networkConfig[Network.LocalFin].ISSUER_ID, networkConfig[Network.LocalBiz].ZONE_ID)

    const receiveAccountId = networkConfig[Network.LocalFin].ACCOUNT_ID_2
    const receiveAccountName = networkConfig[Network.LocalFin].ACCOUNT_NAME_2
    await syncAccount(Network.LocalBiz, receiveAccountId, receiveAccountName)
    await new Promise((resolve) => setTimeout(resolve, 15000))
    const syncAccountResult = extractAccountInfo(await showAccountsStatus(receiveAccountId))
    if (
      syncAccountResult.finZone.status !== commonConfig.STATUS_ACTIVE ||
      syncAccountResult.finZone.bizZoneAccountStatus !== commonConfig.STATUS_APPLYING ||
      syncAccountResult.bizZone.status !== commonConfig.STATUS_ACTIVE
    )
      throw new Error('Sync account failed')
    await new SetActiveBusinessAccountWithZoneTask(Network.LocalFin).execute({ accountId: receiveAccountId })
    const activeAccount = extractAccountInfo(await showAccountsStatus(receiveAccountId))
    if (
      activeAccount.finZone.status !== commonConfig.STATUS_ACTIVE ||
      activeAccount.finZone.bizZoneAccountStatus !== commonConfig.STATUS_ACTIVE ||
      activeAccount.bizZone.status !== commonConfig.STATUS_ACTIVE
    )
      throw new Error('Activate account failed')
  })

  it('Start register account without limit setting', async function () {
    const accountId = '333333'
    const output = await addAccountWithoutLimit(Network.LocalFin, accountId)
    expect(output).to.not.include(ERROR)
  })

  it('Start register account with limit setting', async function () {
    const accountId = '333334'
    const output = await addAccountWithLimit(Network.LocalFin, accountId)
    expect(output).to.not.include(ERROR)
  })

  it('Start modify token limit', async function () {
    const accountId = '333333'
    const output = await modTokenLimit(Network.LocalFin, accountId)
    expect(output).to.not.include(ERROR)
  })

  it('Get account all should return limit values', async function () {
    const accountId = '333333'
    const output = await getAccountAll(Network.LocalFin, accountId)
    expect(output).to.not.include(ERROR)
  })

  it('Get account should return limit values', async function () {
    const accountId = '333333'
    const output = await getAccountInfo(Network.LocalFin, accountId)
    expect(output).to.not.include(ERROR)
  })

  it('Get account limit should return limit values', async function () {
    const accountId = '333333'
    const output = await getAccountLimit(Network.LocalFin, accountId)
    expect(output).to.not.include(ERROR)
  })

  it('Start check transaction not exceed daily transfer limit', async function () {
    const accountId = '333335'
    const output1 = await checkTransactionNotExceedDailyLimit(Network.LocalFin, accountId)
    expect(output1).to.not.include(ERROR)
    const accountId2 = '333336'
    const output2 = await checkTransactionNotExceedDailyLimit(Network.LocalBiz, accountId2)
    expect(output2).to.not.include(ERROR)
  })

  it('Start check transaction account not set limit', async function () {
    const accountId = '333337'
    const output1 = await checkTransactionNotSetLimit(Network.LocalFin, accountId)
    expect(output1).to.not.include(ERROR)
    const accountId2 = '333338'
    const output2 = await checkTransactionNotSetLimit(Network.LocalBiz, accountId2)
    expect(output2).to.not.include(ERROR)
  })

  it('Start check transaction exceed daily limit', async function () {
    const accountId = '333339'
    const output1 = await checkTransactionExceedDailyLimit(Network.LocalFin, accountId)
    expect(output1).to.not.include(ERROR)
    const accountId2 = '333340'
    const output2 = await checkTransactionExceedDailyLimit(Network.LocalBiz, accountId2)
    expect(output2).to.not.include(ERROR)
  })

  it('Start check transaction fromAccount and toAccount have different issuer', async function () {
    const output = await checkTransactionFromAndToAccountIssuerAreDifferent(
      networkConfig[Network.LocalFin].ACCOUNT_ID_3,
    )
    expect(output).to.not.include(ERROR)
  })

  it('Start check syncMint, syncBurn, syncCharge, syncDischarge, syncTransfer, resetCumulative', async function () {
    const accountId = '333341'
    const output = await checkSync(accountId)
    expect(output).to.not.include(ERROR)
  })

  it('Start check mint, burn, charge, discharge, transfer limit', async function () {
    const accountId = '333342'
    const output = await checkTransactionLimit(accountId, Network.LocalBiz)
    expect(output).to.not.include(ERROR)
  })

  it('Start check exchange not exceed daily limit', async function () {
    const accountId = '333343'
    const output = await checkExchangeNotExceedLimit(Network.LocalBiz, accountId)
    expect(output).to.not.include(ERROR)
  })

  it('Start check exchange not set limit', async function () {
    const accountId = '333344'
    const output = await checkExchangeNotSetLimit(Network.LocalBiz, accountId)
    expect(output).to.not.include(ERROR)
  })

  it('Start check exchange exceed limit', async function () {
    const accountId = '333345'
    const output1 = await checkExchangeExceedCumulativeChargeLimit(Network.LocalBiz, accountId)
    expect(output1).to.not.include(ERROR)
    const accountId2 = '333346'
    const output2 = await checkExchangeExceedCumulativeDisChargeLimit(Network.LocalBiz, accountId2)
    expect(output2).to.not.include(ERROR)
  })
})
