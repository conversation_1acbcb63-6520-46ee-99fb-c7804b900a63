import { commonConfig, Network, networkConfig } from '../helpers/constants'
import { BurnTokenTask } from '../tasks/BurnTokenTask'
import { CheckBurnTask } from '../tasks/CheckBurnTask'
import { CheckExchangeTask } from '../tasks/CheckExchangeTask'
import { CheckMintTask } from '../tasks/CheckMintTask'
import { CheckSyncAccountTask } from '../tasks/CheckSyncAccountTask'
import { CheckTransactionTask } from '../tasks/CheckTransactionTask'
import { ForceBurnTokenTask } from '../tasks/ForceBurnTokenTask'
import { GetBizZoneAccountStatus } from '../tasks/GetBizZoneAccountStatusTask'
import { MintTokenTask } from '../tasks/MintTokenTask'
import { PartialForceBurnTokenTask } from '../tasks/PartialForceBurnTokenTask'
import { RegisterAccTask } from '../tasks/RegisterAccTask'
import { RetrieveForceBurnEventTask } from '../tasks/RetrieveForceBurnEventTask'
import { SetAccountStatusTask } from '../tasks/SetAccountStatusTask'
import { SyncAccountTask } from '../tasks/SyncAccountTask'
import { TransferSingleTask } from '../tasks/TransferSingleTask'
import { TransferTask } from '../tasks/TransferTask'
import { ERROR, SUCCESS, delay, extractAccountInfo, showAccountsStatus } from './utils'

// テスト用の定数定義
const TEST_AMOUNTS = {
  MINT_AMOUNT: '5000',
  BURN_AMOUNT: '1000',
  TRANSFER_AMOUNT: '500',
  CHARGE_AMOUNT: '1000',
  PARTIAL_BURN_AMOUNT: '3000',
  PARTIAL_BURN_BALANCE: '7000',
  LARGE_MINT_AMOUNT: '10000',
  INITIAL_MINT_AMOUNT: '3000',
} as const

// 期待される残高定義
const EXPECTED_BALANCES = {
  AFTER_MINT: '5000',
  AFTER_BURN: '4000', // 5000 - 1000
  AFTER_TRANSFER_FROM: '3500', // 4000 - 500
  AFTER_TRANSFER_TO: '3500', // 3000 + 500
  AFTER_CHARGE_FIN: '2500', // 3500 - 1000
  AFTER_CHARGE_BIZ: '1000',
  AFTER_PARTIAL_BURN: '7000', // 10000 - 3000
  FORCE_BURN_RESULT: '0',
} as const

/**
 * アカウントの包括的な状態検証を行うヘルパー関数
 */
async function verifyAccountState(
  network: Network,
  accountId: string,
  expectedFinBalance: string,
  expectedStatus: string,
  operationName: string,
): Promise<boolean> {
  try {
    const accountInfo = extractAccountInfo(await showAccountsStatus(accountId))

    // FinZone残高の確認
    if (accountInfo.finZone.balance !== expectedFinBalance) {
      console.error(
        `${operationName}: FinZone balance mismatch. Expected: ${expectedFinBalance}, Got: ${accountInfo.finZone.balance}`,
      )
      return false
    }

    // ステータスの確認
    if (accountInfo.finZone.status !== expectedStatus) {
      console.error(
        `${operationName}: Account status mismatch. Expected: ${expectedStatus}, Got: ${accountInfo.finZone.status}`,
      )
      return false
    }

    console.info(
      `${operationName}: Account state verified - Balance: ${accountInfo.finZone.balance}, Status: ${accountInfo.finZone.status}`,
    )
    return true
  } catch (error) {
    console.error(`Error verifying account state for ${operationName}:`, error)
    return false
  }
}

/**
 * ビジネスゾーンアカウントの状態検証を行うヘルパー関数
 */
async function verifyBizZoneAccountState(
  network: Network,
  accountId: string,
  expectedBalance: string,
  expectedStatus: string,
  operationName: string,
): Promise<boolean> {
  try {
    const bizZoneStatus = await new GetBizZoneAccountStatus(network).execute({
      accountId,
      zoneId: networkConfig[network].ZONE_ID,
    })

    if (!bizZoneStatus.includes(expectedStatus)) {
      console.error(`${operationName}: BizZone status mismatch. Expected: ${expectedStatus}, Got: ${bizZoneStatus}`)
      return false
    }

    if (!bizZoneStatus.includes(expectedBalance)) {
      console.error(`${operationName}: BizZone balance mismatch. Expected: ${expectedBalance}, Got: ${bizZoneStatus}`)
      return false
    }

    console.info(
      `${operationName}: BizZone account state verified - Balance: ${expectedBalance}, Status: ${expectedStatus}`,
    )
    return true
  } catch (error) {
    console.error(`Error verifying BizZone account state for ${operationName}:`, error)
    return false
  }
}

/**
 * AfterBalanceイベントの総合検証を行うヘルパー関数
 */
async function verifyAfterBalanceEvent(
  network: Network,
  accountId: string,
  expectedBalance: string,
  operationName: string,
): Promise<boolean> {
  console.info(`Starting comprehensive AfterBalance verification for ${operationName}`)

  // アカウント状態の検証
  const accountVerified = await verifyAccountState(
    network,
    accountId,
    expectedBalance,
    commonConfig.STATUS_ACTIVE,
    operationName,
  )

  if (!accountVerified) {
    console.error(`${operationName}: Account state verification failed`)
    return false
  }

  console.info(`${operationName}: AfterBalance event verification passed`)
  return true
}

/**
 * emitAfterBalance機能のシナリオテスト
 * 各種取引操作後にAfterBalanceイベントが正しく発行されることを確認
 */
export async function emitAfterBalanceScenario(network: Network) {
  console.info('=== Start emitAfterBalance Scenario ===')

  // テスト用アカウントの準備
  const testAccountId1 = 'EMIT_TEST_ACC_1'
  const testAccountId2 = 'EMIT_TEST_ACC_2'
  const testAccountId3 = 'EMIT_TEST_ACC_3'

  try {
    // 1. テスト用アカウントの登録
    console.info('1. Register test accounts for emitAfterBalance scenario')
    await registerTestAccounts(network, [testAccountId1, testAccountId2, testAccountId3])
    await delay(5000)

    // 2. Mint操作でのAfterBalanceイベント確認
    console.info('2. Test AfterBalance event on mint operation')
    const mintResult = await testMintAfterBalance(network, testAccountId1)
    if (mintResult !== SUCCESS) {
      console.error('Mint AfterBalance test failed')
      return ERROR
    }
    await delay(5000)

    // 3. Burn操作でのAfterBalanceイベント確認
    console.info('3. Test AfterBalance event on burn operation')
    const burnResult = await testBurnAfterBalance(network, testAccountId1)
    if (burnResult !== SUCCESS) {
      console.error('Burn AfterBalance test failed')
      return ERROR
    }
    await delay(5000)

    // 4. Transfer操作でのAfterBalanceイベント確認
    console.info('4. Test AfterBalance event on transfer operation')
    // まずtestAccountId2にもmint
    await new MintTokenTask(network).execute({
      accountId: testAccountId2,
      amount: TEST_AMOUNTS.INITIAL_MINT_AMOUNT,
    })
    await delay(5000)

    const transferResult = await testTransferAfterBalance(network, testAccountId1, testAccountId2)
    if (transferResult !== SUCCESS) {
      console.error('Transfer AfterBalance test failed')
      return ERROR
    }
    await delay(5000)

    // 5. BizZone間でのCharge/Discharge操作でのAfterBalanceイベント確認
    console.info('5. Test AfterBalance event on charge/discharge operations')

    // BizZoneへの同期を実行
    const syncResult = await setupBizZoneAccount(network, testAccountId1)
    if (syncResult !== SUCCESS) {
      console.error('BizZone account sync failed - skipping charge test')
      console.info('Note: BizZone tests require proper environment setup')
      // BizZoneテストをスキップしても、他のテストを続行
    } else {
      await delay(15000) // 同期待ち

      // BizZoneへのCharge
      const chargeResult = await testChargeAfterBalance(network, testAccountId1)
      if (chargeResult !== SUCCESS) {
        console.error('Charge AfterBalance test failed')
        return ERROR
      }
      await delay(5000)
    }

    // 6. ForceBurn操作でのAfterBalanceイベント確認
    console.info('6. Test AfterBalance event on forceBurn operation')
    // testAccountId3に残高を設定
    await new MintTokenTask(network).execute({
      accountId: testAccountId3,
      amount: TEST_AMOUNTS.MINT_AMOUNT,
    })
    await delay(5000)

    const forceBurnResult = await testForceBurnAfterBalance(network, testAccountId3)
    if (forceBurnResult !== SUCCESS) {
      console.error('ForceBurn AfterBalance test failed')
      return ERROR
    }
    await delay(5000)

    // 7. PartialForceBurn操作でのAfterBalanceイベント確認
    console.info('7. Test AfterBalance event on partialForceBurn operation')
    // 新しいアカウントで部分強制償却テスト
    const testAccountId4 = 'EMIT_TEST_ACC_4'
    await registerTestAccounts(network, [testAccountId4])
    await new MintTokenTask(network).execute({
      accountId: testAccountId4,
      amount: TEST_AMOUNTS.LARGE_MINT_AMOUNT,
    })
    await delay(5000)

    const partialForceBurnResult = await testPartialForceBurnAfterBalance(network, testAccountId4)
    if (partialForceBurnResult !== SUCCESS) {
      console.error('PartialForceBurn AfterBalance test failed')
      return ERROR
    }

    console.info('=== emitAfterBalance Scenario Completed Successfully ===')
    return SUCCESS
  } catch (error) {
    console.error('Error in emitAfterBalance scenario:', error)
    return ERROR
  }
}

/**
 * テスト用アカウントの登録
 */
async function registerTestAccounts(network: Network, accountIds: string[]) {
  const config = networkConfig[network]

  for (const accountId of accountIds) {
    await new RegisterAccTask(network).execute({
      accountId,
      accountName: `Test Account ${accountId}`,
      validId: config.VALID_ID,
    })
  }
}

/**
 * Mint操作でのAfterBalanceイベント確認
 */
async function testMintAfterBalance(network: Network, accountId: string) {
  console.info(`Testing mint AfterBalance for account: ${accountId}`)

  // 事前チェック: Mintが可能な状態であることを確認
  console.info('Performing pre-mint checks...')
  const checkMintOutput = await new CheckMintTask(network).execute({
    accountId,
    amount: TEST_AMOUNTS.MINT_AMOUNT,
  })

  if (!checkMintOutput.includes('result | ok')) {
    console.error('Pre-mint check failed')
    return ERROR
  }

  // Mint実行
  const mintOutput = await new MintTokenTask(network).execute({
    accountId,
    amount: TEST_AMOUNTS.MINT_AMOUNT,
  })

  if (!mintOutput.includes(SUCCESS)) {
    console.error('Mint operation failed')
    return ERROR
  }

  // AfterBalanceイベントの総合検証
  const eventVerified = await verifyAfterBalanceEvent(network, accountId, EXPECTED_BALANCES.AFTER_MINT, 'mint')

  if (!eventVerified) {
    console.error('Mint AfterBalance event verification failed')
    return ERROR
  }

  console.info('Mint AfterBalance test passed')
  return SUCCESS
}

/**
 * Burn操作でのAfterBalanceイベント確認
 */
async function testBurnAfterBalance(network: Network, accountId: string) {
  console.info(`Testing burn AfterBalance for account: ${accountId}`)

  // 事前チェック: Burnが可能な状態であることを確認
  console.info('Performing pre-burn checks...')
  const checkBurnOutput = await new CheckBurnTask(network).execute({
    accountId,
    amount: TEST_AMOUNTS.BURN_AMOUNT,
  })

  if (!checkBurnOutput.includes('result | ok')) {
    console.error('Pre-burn check failed')
    return ERROR
  }

  // Burn実行
  const burnOutput = await new BurnTokenTask(network).execute({
    accountId,
    amount: TEST_AMOUNTS.BURN_AMOUNT,
  })

  if (!burnOutput.includes(SUCCESS)) {
    console.error('Burn operation failed')
    return ERROR
  }

  // AfterBalanceイベントの総合検証
  const eventVerified = await verifyAfterBalanceEvent(network, accountId, EXPECTED_BALANCES.AFTER_BURN, 'burn')

  if (!eventVerified) {
    console.error('Burn AfterBalance event verification failed')
    return ERROR
  }

  console.info('Burn AfterBalance test passed')
  return SUCCESS
}

/**
 * Transfer操作でのAfterBalanceイベント確認
 */
async function testTransferAfterBalance(network: Network, fromAccountId: string, toAccountId: string) {
  console.info(`Testing transfer AfterBalance from ${fromAccountId} to ${toAccountId}`)

  // 事前チェック: Transferが可能な状態であることを確認
  console.info('Performing pre-transfer checks...')
  const checkTransferOutput = await new CheckTransactionTask(network).execute({
    sendAccountId: fromAccountId,
    fromAccountId: fromAccountId,
    toAccountId: toAccountId,
    amount: TEST_AMOUNTS.TRANSFER_AMOUNT,
  })

  if (!checkTransferOutput.includes('result | ok')) {
    console.error('Pre-transfer check failed')
    return ERROR
  }

  // Transfer実行
  const transferOutput = await new TransferSingleTask(network).execute({
    sendAccountId: fromAccountId,
    fromAccountId: fromAccountId,
    toAccountId: toAccountId,
    amount: TEST_AMOUNTS.TRANSFER_AMOUNT,
    miscValue1: '',
    miscValue2: '',
    memo: 'Test transfer for AfterBalance',
    traceId: `TRACE_${Date.now()}`,
  })

  if (!transferOutput.includes(SUCCESS)) {
    console.error('Transfer operation failed')
    return ERROR
  }

  await delay(5000)

  // 送金元アカウントのAfterBalanceイベント検証
  const fromEventVerified = await verifyAfterBalanceEvent(
    network,
    fromAccountId,
    EXPECTED_BALANCES.AFTER_TRANSFER_FROM,
    'transfer (from)',
  )

  if (!fromEventVerified) {
    console.error('Transfer from account AfterBalance event verification failed')
    return ERROR
  }

  // 送金先アカウントのAfterBalanceイベント検証
  const toEventVerified = await verifyAfterBalanceEvent(
    network,
    toAccountId,
    EXPECTED_BALANCES.AFTER_TRANSFER_TO,
    'transfer (to)',
  )

  if (!toEventVerified) {
    console.error('Transfer to account AfterBalance event verification failed')
    return ERROR
  }

  console.info('Transfer AfterBalance test passed')
  return SUCCESS
}

/**
 * BizZoneアカウントのセットアップ
 */
async function setupBizZoneAccount(network: Network, accountId: string) {
  console.info(`Setting up BizZone sync for account: ${accountId}`)

  const finConfig = networkConfig[Network.LocalFin]
  const bizConfig = networkConfig[Network.LocalBiz]

  // まずLocalBizネットワークに口座を登録（既に登録されている場合はスキップ）
  console.info(`Registering account ${accountId} to LocalBiz network...`)
  try {
    await new RegisterAccTask(Network.LocalBiz).execute({
      accountId,
      accountName: `Test Account ${accountId}`,
      validId: bizConfig.VALID_ID,
    })
    console.info(`Account ${accountId} registered successfully in LocalBiz`)
  } catch (error) {
    // アカウントが既に登録されている場合は無視
    if (error && error.toString().includes('already exists')) {
      console.info(`Account ${accountId} already exists in LocalBiz - continuing...`)
    } else {
      console.error(`Failed to register account ${accountId} in LocalBiz:`, error)
      return ERROR
    }
  }

  // LocalFinでの同期確認をチェック
  const checkResult = await new CheckSyncAccountTask(Network.LocalFin).execute({
    accountId,
    validId: finConfig.VALID_ID,
    zoneId: bizConfig.ZONE_ID,
  })

  if (checkResult && checkResult.includes('result | ok')) {
    console.info(`Starting syncAccount for account ${accountId}...`)

    const syncAccountTask = new SyncAccountTask(Network.LocalBiz)
    const result = await syncAccountTask.execute({
      accountId,
      accountName: `Test Account ${accountId}`,
    })

    if (!result.includes(SUCCESS)) {
      console.error(`Failed to sync account ${accountId}`)
      return ERROR
    }

    console.info(`syncAccount for account ${accountId} is successful.`)
    return SUCCESS
  } else {
    console.error(`Failed checkSyncAccount for account ${accountId}.`)
    return ERROR
  }
}

/**
 * Charge操作でのAfterBalanceイベント確認
 */
async function testChargeAfterBalance(network: Network, accountId: string) {
  console.info(`Testing charge AfterBalance for account: ${accountId}`)

  // CheckExchangeを実行
  const checkExOutput = await new CheckExchangeTask(network).execute({
    accountId,
    toZoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
  })
  if (!checkExOutput.includes('result | ok')) {
    console.error('CheckExchange failed')
    return ERROR
  }

  // BizZoneへのCharge実行
  console.info('Starting charge operation...')
  const chargeOutput = await new TransferTask(network).execute({
    accountId,
    toZoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
    amount: TEST_AMOUNTS.CHARGE_AMOUNT,
  })

  if (!chargeOutput.includes(SUCCESS)) {
    console.error('Charge operation failed')
    return ERROR
  }

  await delay(15000) // BizZone同期待ち

  // FinZoneのAfterBalanceイベント検証
  const finZoneEventVerified = await verifyAfterBalanceEvent(
    network,
    accountId,
    EXPECTED_BALANCES.AFTER_CHARGE_FIN,
    'charge (FinZone)',
  )

  if (!finZoneEventVerified) {
    console.error('Charge FinZone AfterBalance event verification failed')
    return ERROR
  }

  // BizZoneアカウントの状態検証
  const bizZoneVerified = await verifyBizZoneAccountState(
    network,
    accountId,
    EXPECTED_BALANCES.AFTER_CHARGE_BIZ,
    commonConfig.STATUS_ACTIVE,
    'charge (BizZone)',
  )

  if (!bizZoneVerified) {
    console.error('Charge BizZone state verification failed')
    return ERROR
  }

  console.info('Charge AfterBalance test passed')
  return SUCCESS
}

/**
 * ForceBurn操作でのAfterBalanceイベント確認
 */
async function testForceBurnAfterBalance(network: Network, accountId: string) {
  console.info(`Testing forceBurn AfterBalance for account: ${accountId}`)

  // アカウントを凍結状態に変更（forceBurnの前提条件）
  console.info(`Freezing account: ${accountId}`)
  const freezeOutput = await new SetAccountStatusTask(network).execute({
    accountId,
    accountStatus: commonConfig.STATUS_FROZEN,
    issuerId: networkConfig[Network.LocalFin].ISSUER_ID,
  })

  if (!freezeOutput.includes(SUCCESS)) {
    console.error('Failed to freeze account')
    return ERROR
  }

  // 凍結状態の確認
  const frozenAccountInfo = extractAccountInfo(await showAccountsStatus(accountId))
  if (frozenAccountInfo.finZone.status !== commonConfig.STATUS_FROZEN) {
    console.error(`Expected account status to be frozen, but got ${frozenAccountInfo.finZone.status}`)
    return ERROR
  }

  // ForceBurn実行
  const forceBurnOutput = await new ForceBurnTokenTask(network).execute({
    accountId,
    issuerId: networkConfig[Network.LocalFin].ISSUER_ID,
  })

  if (!forceBurnOutput.includes(SUCCESS)) {
    console.error('ForceBurn operation failed')
    return ERROR
  }

  // ForceBurnイベントの検証
  console.info('Retrieving ForceBurn event...')
  const forceBurnEvent = await new RetrieveForceBurnEventTask(network).execute({})
  if (!forceBurnEvent.includes(accountId)) {
    console.error('ForceBurn event not found or does not contain expected accountId')
    return ERROR
  }

  // AfterBalanceイベントの包括的検証
  const eventVerified = await verifyAccountState(
    network,
    accountId,
    EXPECTED_BALANCES.FORCE_BURN_RESULT,
    commonConfig.STATUS_FORCE_BURNED,
    'forceBurn',
  )

  if (!eventVerified) {
    console.error('ForceBurn AfterBalance event verification failed')
    return ERROR
  }

  console.info('ForceBurn AfterBalance test passed')
  return SUCCESS
}

/**
 * PartialForceBurn操作でのAfterBalanceイベント確認
 */
async function testPartialForceBurnAfterBalance(network: Network, accountId: string) {
  console.info(`Testing partialForceBurn AfterBalance for account: ${accountId}`)

  // アカウントを凍結状態に変更（partialForceBurnの前提条件）
  console.info(`Freezing account: ${accountId}`)
  const freezeOutput = await new SetAccountStatusTask(network).execute({
    accountId,
    accountStatus: commonConfig.STATUS_FROZEN,
    issuerId: networkConfig[Network.LocalFin].ISSUER_ID,
  })

  if (!freezeOutput.includes(SUCCESS)) {
    console.error('Failed to freeze account')
    return ERROR
  }

  // 凍結状態の確認
  const frozenAccountInfo = extractAccountInfo(await showAccountsStatus(accountId))
  if (frozenAccountInfo.finZone.status !== commonConfig.STATUS_FROZEN) {
    console.error(`Expected account status to be frozen, but got ${frozenAccountInfo.finZone.status}`)
    return ERROR
  }

  // PartialForceBurn実行（部分的に償却）
  const partialForceBurnOutput = await new PartialForceBurnTokenTask(network).execute({
    accountId,
    issuerId: networkConfig[Network.LocalFin].ISSUER_ID,
    burnedAmount: TEST_AMOUNTS.PARTIAL_BURN_AMOUNT,
    burnedBalance: TEST_AMOUNTS.PARTIAL_BURN_BALANCE, // 残高10000から3000を償却して7000
  })

  if (!partialForceBurnOutput.includes(SUCCESS)) {
    console.error('PartialForceBurn operation failed')
    return ERROR
  }

  // AfterBalanceイベントの包括的検証
  const eventVerified = await verifyAccountState(
    network,
    accountId,
    EXPECTED_BALANCES.AFTER_PARTIAL_BURN,
    commonConfig.STATUS_FROZEN,
    'partialForceBurn',
  )

  if (!eventVerified) {
    console.error('PartialForceBurn AfterBalance event verification failed')
    return ERROR
  }

  console.info('PartialForceBurn AfterBalance test passed')
  return SUCCESS
}
