import { Network } from '../helpers/constants'
import { RegisterIssuerArguments } from '../helpers/task-arguments'
import { RegisterIssuerTask } from '../tasks/RegisterIssuerTask'

async function registerIssuer(network: Network, params: Partial<RegisterIssuerArguments> = {}) {
  const registerIssuerTask = new RegisterIssuerTask(network)
  return await registerIssuerTask.execute(params)
}

export { registerIssuer }
