import { commonConfig, Network, networkConfig } from '../helpers/constants'
import { CheckTransactionTask } from '../tasks/CheckTransactionTask'
import { TransferSingleTask } from '../tasks/TransferSingleTask'
import { ERROR, extractAccountInfo, showAccountsStatus, SUCCESS } from './utils'

export async function syncTransfer(network: Network, fromAccountId: string, toAccountId: string) {
  const checkOutput = await new CheckTransactionTask(Network.LocalFin).execute({
    sendAccountId: fromAccountId,
    fromAccountId,
    toAccountId,
    zoneId: networkConfig[network].ZONE_ID,
    validId: networkConfig[network].VALID_ID,
  })
  if (!checkOutput.includes('result | ok')) {
    throw new Error('failed checkTransaction.')
  }
  const transferOutput = await new TransferSingleTask(network).execute({
    sendAccountId: fromAccountId,
    fromAccountId,
    toAccountId,
  })
  if (!transferOutput.includes(SUCCESS)) {
    return ERROR
  }
  await new Promise((resolve) => setTimeout(resolve, 15000))

  const fromAccountInfo = extractAccountInfo(await showAccountsStatus(fromAccountId))
  if (
    fromAccountInfo.finZone.bizZoneAccountBalance !== '0' ||
    fromAccountInfo.finZone.bizZoneAccountStatus !== commonConfig.STATUS_ACTIVE ||
    fromAccountInfo.bizZone.balance !== '0' ||
    fromAccountInfo.bizZone.status !== commonConfig.STATUS_ACTIVE
  ) {
    return ERROR
  }
  const toAccountInfo = extractAccountInfo(await showAccountsStatus(toAccountId))

  if (
    toAccountInfo.finZone.bizZoneAccountBalance !== '1000' ||
    toAccountInfo.finZone.bizZoneAccountStatus !== commonConfig.STATUS_ACTIVE ||
    toAccountInfo.bizZone.balance !== '1000' ||
    toAccountInfo.bizZone.status !== commonConfig.STATUS_ACTIVE
  ) {
    return ERROR
  }

  return SUCCESS
}
