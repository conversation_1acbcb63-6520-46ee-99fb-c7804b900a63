import { expect } from 'chai'
import { Network, networkConfig } from '../helpers/constants'
import { DeleteBizZoneToIssuer } from '../tasks/DeleteBizZoneToIssuer'
import { GetIssuerWithZone } from '../tasks/GetIssuerWithZone'
import { ERROR, SUCCESS } from './utils'

export async function deleteBizZoneToIssuer() {
  const issuerId = networkConfig[Network.LocalFin].ISSUER_ID
  const zoneId = networkConfig[Network.LocalBiz].ZONE_ID

  const beforeDeleteIssuer = await new GetIssuerWithZone(Network.LocalFin).execute({ zoneId: zoneId })
  expect(beforeDeleteIssuer).to.include(issuerId)

  await new DeleteBizZoneToIssuer(Network.LocalFin).execute({
    issuerId: issuerId,
    zoneId: zoneId,
  })

  const afterDeleteIssuer = await new GetIssuerWithZone(Network.LocalFin).execute({ zoneId: zoneId })
  expect(afterDeleteIssuer).to.not.include(issuerId)
  if (!afterDeleteIssuer.includes(issuerId)) {
    return SUCCESS
  }
  return ERROR
}
