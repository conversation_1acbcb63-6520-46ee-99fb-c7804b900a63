import { Network } from '../helpers/constants'
import { AddBizZoneToIssuer } from '../tasks/AddBizZoneToIssuer'
import { GetIssuerWithZone } from '../tasks/GetIssuerWithZone'
import { RegisterBizZoneTask } from '../tasks/RegisterBizZoneTask'

export async function regBizZone(zoneId: string, zoneName: string) {
  return await new RegisterBizZoneTask(Network.LocalFin).execute({
    zoneId: zoneId,
    zoneName: zoneName,
  })
}

export async function addBizZoneToIssuer(issuerId: string, zoneId: string) {
  return await new AddBizZoneToIssuer(Network.LocalFin).execute({
    issuerId: issuerId,
    zoneId: zoneId,
  })
}

export async function getIssuerWithZone(zoneId: string) {
  return await new GetIssuerWithZone(Network.LocalFin).execute({
    zoneId: zoneId,
  })
}
