import { commonConfig, Network, networkConfig } from '../helpers/constants'
import { GetAccountAllTask } from '../tasks/GetAccountAllTask'
import { SetActiveBusinessAccountWithZoneTask } from '../tasks/SetActiveBusinessAccountWithZoneTask'
import { ERROR, extractAccountInfo, showAccountsStatus, SUCCESS } from './utils'

async function processIndustryAccount(network: Network, accountId: string, networkConfig: any) {
  const getAccountAllTask = new GetAccountAllTask(network)
  const output = await getAccountAllTask.execute({
    accountId,
  })

  if (!output.includes('Not linked from Business Zone.')) {
    console.info(`start to make account ${accountId} active.`)

    const setActiveTask = new SetActiveBusinessAccountWithZoneTask(network)
    return await setActiveTask.execute({
      accountId,
    })
  } else {
    throw new Error(`BZ account ${accountId} is not linked.`)
  }
}

async function industry(network: Network) {
  const config = networkConfig[network]

  await processIndustryAccount(network, config.ACCOUNT_ID_3, config)
  await processIndustryAccount(network, config.ACCOUNT_ID_4, config)

  const account3Status = extractAccountInfo(await showAccountsStatus(config.ACCOUNT_ID_3))
  if (
    account3Status.finZone.status !== commonConfig.STATUS_ACTIVE ||
    account3Status.finZone.bizZoneAccountStatus !== commonConfig.STATUS_ACTIVE
  ) {
    return ERROR
  }

  const account4Status = extractAccountInfo(await showAccountsStatus(config.ACCOUNT_ID_4))
  if (
    account4Status.finZone.status !== commonConfig.STATUS_ACTIVE ||
    account4Status.finZone.bizZoneAccountStatus !== commonConfig.STATUS_ACTIVE
  ) {
    return ERROR
  }
  return SUCCESS
}

export { industry }
