import { commonConfig, Network, networkConfig } from '../helpers/constants'
import { CheckSyncAccountTask } from '../tasks/CheckSyncAccountTask'
import { SyncAccountTask } from '../tasks/SyncAccountTask'
import { ERROR, extractAccountInfo, showAccountsStatus, SUCCESS, ERROR_CODE } from './utils'

export async function syncAccount(network: Network, accountId: string, accountName: string) {
  const checkResult = await new CheckSyncAccountTask(Network.LocalFin).execute({
    accountId,
    validId: networkConfig[network].VALID_ID,
    zoneId: networkConfig[network].ZONE_ID,
  })

  if (checkResult && checkResult.includes('result | ok')) {
    console.info(`start syncAccount for account ${accountId}.`)

    const syncAccountTask = new SyncAccountTask(network)
    const result = await syncAccountTask.execute({
      accountId,
      accountName,
    })

    console.info(`syncAccount for account ${accountId} is successful.`)
    return result
  } else {
    throw new Error(`failed checkSyncAccount for account ${accountId}.`)
  }
}

async function synchronous(network: Network) {
  const configData = networkConfig[network]

  await syncAccount(network, configData.ACCOUNT_ID_3, configData.ACCOUNT_NAME_3)
  await syncAccount(network, configData.ACCOUNT_ID_4, configData.ACCOUNT_NAME_4)

  await new Promise((resolve) => setTimeout(resolve, 15000))

  const syncAccount3 = extractAccountInfo(await showAccountsStatus(configData.ACCOUNT_ID_3))
  if (
    syncAccount3.finZone.status !== commonConfig.STATUS_ACTIVE ||
    syncAccount3.finZone.bizZoneAccountStatus !== commonConfig.STATUS_APPLYING ||
    syncAccount3.bizZone.status !== commonConfig.STATUS_ACTIVE
  )
    return ERROR
  const syncAccount4 = extractAccountInfo(await showAccountsStatus(configData.ACCOUNT_ID_4))
  if (
    syncAccount4.finZone.status !== commonConfig.STATUS_ACTIVE ||
    syncAccount4.finZone.bizZoneAccountStatus !== commonConfig.STATUS_APPLYING ||
    syncAccount4.bizZone.status !== commonConfig.STATUS_ACTIVE
  )
    return ERROR
  return SUCCESS
}

async function checkSyncAccountUnauthorizedIssuer(network: Network) {
  const accountId = networkConfig[Network.LocalFin].UNLINKED_ISSUER_ACCOUNT_ID
  const validId = networkConfig[Network.LocalFin].UNLINKED_ISSUER_VALIDATOR_ID
  const zoneId = networkConfig[Network.LocalBiz].ZONE_ID
  const checkSyncAccountResult = await new CheckSyncAccountTask(network).execute({
    accountId: accountId,
    validId: validId,
    zoneId: zoneId,
  })

  if (checkSyncAccountResult.includes(ERROR_CODE.ACCOUNT_NOT_EXISTS)) {
    return SUCCESS
  }
  return ERROR
}

export { checkSyncAccountAuthorizedIssuer, checkSyncAccountUnauthorizedIssuer, synchronous }
