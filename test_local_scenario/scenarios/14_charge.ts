import { commonConfig, Network, networkConfig } from '../helpers/constants'
import { CheckExchangeTask } from '../tasks/CheckExchangeTask'
import { GetTokenInfoTask } from '../tasks/GetTokenInfoTask'
import { MintTokenTask } from '../tasks/MintTokenTask'
import { TransferTask } from '../tasks/TransferTask'
import { ERROR, extractAccountInfo, showAccountsStatus, SUCCESS, extractTotalSupply } from './utils'

export async function charge(network: Network, accountId: string) {
  await new MintTokenTask(network).execute({
    accountId,
  })
  const checkExOutput = await new CheckExchangeTask(network).execute({
    accountId,
    toZoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
  })
  if (!checkExOutput.includes('result | ok')) {
    throw new Error('failed checkExchange.')
  }
  console.info('start charge to account3')

  // チャージ前のtotalSupplyを取得(FinZone)
  const finTokenInfoBefore = await new GetTokenInfoTask(Network.LocalFin).execute({
    providerId: networkConfig[Network.LocalFin].PROV_ID,
  })
  const finTotalSupplyBefore = extractTotalSupply(finTokenInfoBefore)

  // チャージ前のtotalSupplyを取得(BizZone)
  const bizTokenInfoBefore = await new GetTokenInfoTask(Network.LocalBiz).execute({
    providerId: networkConfig[Network.LocalBiz].PROV_ID,
  })
  const bizTotalSupplyBefore = extractTotalSupply(bizTokenInfoBefore)

  const transferOutput = await new TransferTask(network).execute({
    accountId,
    toZoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
  })
  if (!transferOutput.includes(SUCCESS)) {
    return ERROR
  }
  await new Promise((resolve) => setTimeout(resolve, 15000))

  const accountInfo = extractAccountInfo(await showAccountsStatus(accountId))
  if (
    accountInfo.finZone.balance !== '4000' ||
    accountInfo.finZone.status !== commonConfig.STATUS_ACTIVE ||
    accountInfo.finZone.bizZoneAccountStatus !== commonConfig.STATUS_ACTIVE ||
    accountInfo.finZone.bizZoneAccountBalance !== '1000' ||
    accountInfo.bizZone.status !== commonConfig.STATUS_ACTIVE ||
    accountInfo.bizZone.balance !== '1000'
  ) {
    return ERROR
  }

  // チャージ後のtotalSupplyを取得(FinZone)
  const finTokenInfoAfter = await new GetTokenInfoTask(Network.LocalFin).execute({
    providerId: networkConfig[Network.LocalFin].PROV_ID,
  })
  const finTotalSupplyAfter = extractTotalSupply(finTokenInfoAfter)

  // チャージ後のtotalSupplyを取得(BizZone)
  const bizTokenInfoAfter = await new GetTokenInfoTask(Network.LocalBiz).execute({
    providerId: networkConfig[Network.LocalBiz].PROV_ID,
  })
  const bizTotalSupplyAfter = extractTotalSupply(bizTokenInfoAfter)

  if (
    finTotalSupplyBefore.totalSupply == null ||
    finTotalSupplyAfter.totalSupply == null ||
    bizTotalSupplyBefore.totalSupply == null ||
    bizTotalSupplyAfter.totalSupply == null
  ) {
    return ERROR
  }

  // FinZoneのtotalSupplyが変わっていないこと
  if (finTotalSupplyBefore.totalSupply - finTotalSupplyAfter.totalSupply !== 0) {
    return ERROR
  }

  // BizZoneのtotalSupplyがチャージ額分だけ増額されていること
  if (bizTotalSupplyAfter.totalSupply - bizTotalSupplyBefore.totalSupply !== Number(commonConfig.CHARGE_AMOUNT)) {
    return ERROR
  }

  return SUCCESS
}
