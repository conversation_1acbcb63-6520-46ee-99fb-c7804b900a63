import { commonConfig, Network, networkConfig } from '../helpers/constants'
import { CheckExchangeTask } from '../tasks/CheckExchangeTask'
import { GetTokenInfoTask } from '../tasks/GetTokenInfoTask'
import { TransferTask } from '../tasks/TransferTask'
import { ERROR, extractAccountInfo, showAccountsStatus, SUCCESS, extractTotalSupply } from './utils'

async function processDischarge(network: Network, accountId: string) {
  const checkExchangeTask = new CheckExchangeTask(Network.LocalFin)
  const checkResult = await checkExchangeTask.execute({
    accountId,
    toZoneId: networkConfig[Network.LocalBiz].FIN_ZONE_ID,
    fromZoneId: networkConfig[Network.LocalBiz].ZONE_ID,
  })

  if (checkResult && checkResult.includes('result | ok')) {
    const transferTask = new TransferTask(network)
    return await transferTask.execute({
      accountId,
      toZoneId: networkConfig[Network.LocalBiz].FIN_ZONE_ID,
      amount: commonConfig.DISCHARGE_AMOUNT,
    })
  } else {
    throw new Error(`failed exchange.`)
  }
}

async function discharge(network: Network) {
  const config = networkConfig[network]

  // ディスチャージ前のtotalSupplyを取得(FinZone)
  const finTokenInfoBefore = await new GetTokenInfoTask(Network.LocalFin).execute({
    providerId: networkConfig[Network.LocalFin].PROV_ID,
  })
  const finTotalSupplyBefore = extractTotalSupply(finTokenInfoBefore)

  // ディスチャージ前のtotalSupplyを取得(BizZone)
  const bizTokenInfoBefore = await new GetTokenInfoTask(Network.LocalBiz).execute({
    providerId: networkConfig[Network.LocalBiz].PROV_ID,
  })
  const bizTotalSupplyBefore = extractTotalSupply(bizTokenInfoBefore)

  const result = await processDischarge(network, config.ACCOUNT_ID_4)
  if (!result.includes(SUCCESS)) return ERROR

  await new Promise((resolve) => setTimeout(resolve, 15000))

  const account4Status = extractAccountInfo(await showAccountsStatus(config.ACCOUNT_ID_4))
  if (
    account4Status.finZone.balance !== '500' ||
    account4Status.finZone.status !== commonConfig.STATUS_ACTIVE ||
    account4Status.finZone.bizZoneAccountStatus !== commonConfig.STATUS_ACTIVE ||
    account4Status.finZone.bizZoneAccountBalance !== '500' ||
    account4Status.bizZone.status !== commonConfig.STATUS_ACTIVE ||
    account4Status.bizZone.balance !== '500'
  )
    return ERROR

  // ディスチャージ後のtotalSupplyを取得(FinZone)
  const finTokenInfoAfter = await new GetTokenInfoTask(Network.LocalFin).execute({
    providerId: networkConfig[Network.LocalFin].PROV_ID,
  })
  const finTotalSupplyAfter = extractTotalSupply(finTokenInfoAfter)

  // ディスチャージ後のtotalSupplyを取得(BizZone)
  const bizTokenInfoAfter = await new GetTokenInfoTask(Network.LocalBiz).execute({
    providerId: networkConfig[Network.LocalBiz].PROV_ID,
  })
  const bizTotalSupplyAfter = extractTotalSupply(bizTokenInfoAfter)

  if (
    finTotalSupplyBefore.totalSupply == null ||
    finTotalSupplyAfter.totalSupply == null ||
    bizTotalSupplyBefore.totalSupply == null ||
    bizTotalSupplyAfter.totalSupply == null
  ) {
    return ERROR
  }

  // FinZoneのtotalSupplyが変わっていないこと
  if (finTotalSupplyBefore.totalSupply - finTotalSupplyAfter.totalSupply !== 0) {
    return ERROR
  }

  // BizZoneのtotalSupplyがディスチャージ額分だけ減額されていること
  if (bizTotalSupplyBefore.totalSupply - bizTotalSupplyAfter.totalSupply !== Number(commonConfig.DISCHARGE_AMOUNT)) {
    return ERROR
  }

  return SUCCESS
}

export { discharge }
