import { Network, networkConfig } from '../helpers/constants'
import { RegisterMassAccountsArguments } from '../helpers/task-arguments'
import { RegisterAccTask } from '../tasks/RegisterAccTask'
import { RegisterEscrowAccTask } from '../tasks/RegisterEscrowAccTask'
import { RegisterMassAccountsTask } from '../tasks/RegisterMassAccountsTask'
import { ERROR, SUCCESS, checkAccountExist } from './utils'

async function registerAcc(network: Network) {
  const registerAccTask = new RegisterAccTask(network)
  if (network === Network.LocalFin) {
    await registerAccTask.execute({
      accountId: networkConfig[Network.LocalFin].ACCOUNT_ID_1,
      accountName: networkConfig[Network.LocalFin].ACCOUNT_NAME_1,
    })
    if ((await checkAccountExist(network, networkConfig[Network.LocalFin].ACCOUNT_ID_1)) === ERROR) return ERROR
    await registerAccTask.execute({
      accountId: networkConfig[Network.LocalFin].ACCOUNT_ID_2,
      accountName: networkConfig[Network.LocalFin].ACCOUNT_NAME_2,
    })
    if ((await checkAccountExist(network, networkConfig[Network.LocalFin].ACCOUNT_ID_2)) === ERROR) return ERROR
    await registerAccTask.execute({
      accountId: networkConfig[Network.LocalFin].ACCOUNT_ID_3,
      accountName: networkConfig[Network.LocalFin].ACCOUNT_NAME_3,
    })
    if ((await checkAccountExist(network, networkConfig[Network.LocalFin].ACCOUNT_ID_3)) === ERROR) return ERROR
    await registerAccTask.execute({
      accountId: networkConfig[Network.LocalFin].ACCOUNT_ID_4,
      accountName: networkConfig[Network.LocalFin].ACCOUNT_NAME_4,
    })
    if ((await checkAccountExist(network, networkConfig[Network.LocalFin].ACCOUNT_ID_4)) === ERROR) return ERROR
    await registerAccTask.execute({
      accountId: networkConfig[Network.LocalFin].UNLINKED_ISSUER_ACCOUNT_ID,
      accountName: networkConfig[Network.LocalFin].UNLINKED_ISSUER_ACCOUNT_NAME,
      issuerId: networkConfig[Network.LocalFin].UNLINKED_ISSUER_ID,
      validId: networkConfig[Network.LocalFin].UNLINKED_ISSUER_VALIDATOR_ID,
    })
    if (
      (await checkAccountExist(
        network,
        networkConfig[Network.LocalFin].UNLINKED_ISSUER_ACCOUNT_ID,
        networkConfig[Network.LocalFin].UNLINKED_ISSUER_VALIDATOR_ID,
      )) === ERROR
    )
      return ERROR
    return SUCCESS
  } else {
    await registerAccTask.execute({
      accountId: networkConfig[Network.LocalBiz].ACCOUNT_ID_1,
      accountName: networkConfig[Network.LocalBiz].ACCOUNT_NAME_1,
    })
    if ((await checkAccountExist(network, networkConfig[Network.LocalBiz].ACCOUNT_ID_1)) === ERROR) return ERROR
    await registerAccTask.execute({
      accountId: networkConfig[Network.LocalBiz].ACCOUNT_ID_2,
      accountName: networkConfig[Network.LocalBiz].ACCOUNT_NAME_2,
    })
    if ((await checkAccountExist(network, networkConfig[Network.LocalBiz].ACCOUNT_ID_2)) === ERROR) return ERROR
    return SUCCESS
  }
}

async function registerEscrowAccount(network: Network) {
  const registerAccTask = new RegisterEscrowAccTask(network)
  return await registerAccTask.execute({
    dstZoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
    escrowAccount: networkConfig[Network.LocalFin].ACCOUNT_ID_1,
  })
}

async function registerMassAccounts(network: Network, params: Partial<RegisterMassAccountsArguments> = {}) {
  const registerMassAccountsTask = new RegisterMassAccountsTask(network)
  return await registerMassAccountsTask.execute(params)
}

export { registerAcc, registerEscrowAccount, registerMassAccounts }
