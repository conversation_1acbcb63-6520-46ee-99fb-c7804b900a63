import { Network } from '../helpers/constants'
import { RegisterValidArguments } from '../helpers/task-arguments'
import { RegisterValidTask } from '../tasks/RegisterValidTask'

async function registerValid(network: Network, params: Partial<RegisterValidArguments> = {}) {
  const registerValidTask = new RegisterValidTask(network)
  return await registerValidTask.execute(params)
}

export { registerValid }
