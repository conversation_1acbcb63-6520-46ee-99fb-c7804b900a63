import { expect } from 'chai'
import { Network, networkConfig } from './helpers/constants'
import { dvpRenewable } from './scenarios/11_dvp_renewable'
import { addBizZoneToIssuer, regBizZone, getIssuerWithZone } from './scenarios/11_reg_bizzone'
import { getRenewable } from './scenarios/12_get_renewable'
import { registerProv } from './scenarios/2_register_prov'
import { registerIssuer } from './scenarios/3_register_issuer'
import { registerValid } from './scenarios/4_register_valid'
import { registerToken } from './scenarios/5_register_token'
import { registerAcc, registerEscrowAccount } from './scenarios/6_register_account'
import { approve } from './scenarios/7_approve'
import { burnToken } from './scenarios/7_burn_token'
import { mintToken } from './scenarios/7_mint_token'
import { mintRenewable } from './scenarios/9_mint_renewable'
import { ERROR } from './scenarios/utils'

describe('Registering Data for localFin', function () {
  this.timeout(0)
  it('Registering Provider for localFin', async function () {
    const output = await registerProv(Network.LocalFin)
    expect(output).to.not.include(ERROR)
  })
  it('Registering Issuer for localFin', async function () {
    // linked to Biz zone issuer
    const registerIssuer1Result = await registerIssuer(Network.LocalFin, {
      issuerId: networkConfig[Network.LocalFin].ISSUER_ID,
      bankCode: networkConfig[Network.LocalFin].BANK_CODE,
      issuerName: networkConfig[Network.LocalFin].ISSUER_NAME,
    })
    expect(registerIssuer1Result).to.not.include(ERROR)
    // unlinked to biz zone issuer
    const registerIssuer2Result = await registerIssuer(Network.LocalFin, {
      issuerId: networkConfig[Network.LocalFin].UNLINKED_ISSUER_ID,
      bankCode: networkConfig[Network.LocalFin].UNLINKED_ISSUER_BANK_CODE,
      issuerName: networkConfig[Network.LocalFin].UNLINKED_ISSUER_ISSUER_NAME,
    })
    expect(registerIssuer2Result).to.not.include(ERROR)
  })

  it('Setting valid ID for localFin', async function () {
    // linked to Biz zone issuer's validator
    const registerValidator1Result = await registerValid(Network.LocalFin, {
      validId: networkConfig[Network.LocalFin].VALID_ID,
      issuerId: networkConfig[Network.LocalFin].ISSUER_ID,
      validName: networkConfig[Network.LocalFin].VALID_NAME,
    })
    expect(registerValidator1Result).to.not.include(ERROR)
    // unlinked to biz zone issuer's validator
    const registerValidator2Result = await registerValid(Network.LocalFin, {
      validId: networkConfig[Network.LocalFin].UNLINKED_ISSUER_VALIDATOR_ID,
      issuerId: networkConfig[Network.LocalFin].UNLINKED_ISSUER_ID,
      validName: networkConfig[Network.LocalFin].UNLINKED_ISSUER_VALIDATOR_NAME,
    })
    expect(registerValidator2Result).to.not.include(ERROR)
  })
  it('Registering Token for localFin', async function () {
    const output = await registerToken(Network.LocalFin)
    expect(output).to.not.include(ERROR)
  })
  it('Setting Account ID for localFin', async function () {
    const output = await registerAcc(Network.LocalFin)
    expect(output).to.not.include(ERROR)
  })
  it('Setting Escrow account for localFin', async function () {
    const output = await registerEscrowAccount(Network.LocalFin)
    expect(output).to.not.include('Error')
  })
  it('Minting token for localFin', async function () {
    const output = await mintToken(Network.LocalFin)
    expect(output).to.not.include(ERROR)
  })
  it('Burning token for localFin', async function () {
    const output = await burnToken(Network.LocalFin)
    expect(output).to.not.include(ERROR)
  })
  it('Registering BizZone for localFin', async function () {
    const output = await regBizZone(networkConfig[Network.LocalBiz].ZONE_ID, networkConfig[Network.LocalBiz].ZONE_NAME)
    expect(output).to.not.include(ERROR)
  })
  it('Add BizZone to Issuer for localBiz', async function () {
    const addBizZoneResult = await addBizZoneToIssuer(
      networkConfig[Network.LocalFin].ISSUER_ID,
      networkConfig[Network.LocalBiz].ZONE_ID,
    )
    expect(addBizZoneResult).to.not.include(ERROR)
    const getIssuerWithZoneResult = await getIssuerWithZone(networkConfig[Network.LocalBiz].ZONE_ID)
    expect(getIssuerWithZoneResult).to.include(networkConfig[Network.LocalFin].ISSUER_ID)
  })
})

describe('Registering Data for localBiz', function () {
  this.timeout(0)
  it('Registering Provider for localBiz', async function () {
    const output = await registerProv(Network.LocalBiz)
    expect(output).to.not.include(ERROR)
  })
  it('Registering Issuer for localBiz', async function () {
    const output = await registerIssuer(Network.LocalBiz)
    expect(output).to.not.include(ERROR)
  })
  it('Setting valid ID for localBiz', async function () {
    const output = await registerValid(Network.LocalBiz)
    expect(output).to.not.include(ERROR)
  })
  it('Registering Token for localBiz', async function () {
    const output = await registerToken(Network.LocalBiz)
    expect(output).to.not.include(ERROR)
  })
  it('Setting Account ID for localBiz', async function () {
    const output = await registerAcc(Network.LocalBiz)
    expect(output).to.not.include(ERROR)
  })
  it('Minting token for localBiz', async function () {
    const output = await mintToken(Network.LocalBiz)
    expect(output).to.not.include(ERROR)
  })
  it('Burning token for localBiz', async function () {
    const output = await burnToken(Network.LocalBiz)
    expect(output).to.not.include(ERROR)
  })
  it('Approve for localBiz', async function () {
    const output = await approve(Network.LocalBiz)
    expect(output).to.not.include(ERROR)
  })
  it('Minting Renewable Tokens for localBiz', async function () {
    const output = await mintRenewable()
    expect(output).to.not.include(ERROR)
  })
  it('Getting renewable for localBiz', async function () {
    const output = await getRenewable(Network.LocalBiz)
    expect(output).to.not.include(ERROR)
  })
  it('DvP Renewable Tokens for localBiz', async function () {
    const output = await dvpRenewable()
    expect(output).to.not.include(ERROR)
  })
  it('Getting renewable for localBiz', async function () {
    const output = await getRenewable(Network.LocalBiz)
    expect(output).to.not.include(ERROR)
  })
})
