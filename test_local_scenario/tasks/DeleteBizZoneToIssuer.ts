import { Network } from '../helpers/constants'
import { AddBizZoneToIssuerArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class DeleteBizZoneToIssuer extends BaseTask<AddBizZoneToIssuerArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'deleteBizZoneToIssuer')
  }

  protected getDefaultArguments(): Partial<AddBizZoneToIssuerArguments> {
    return {}
  }
}
