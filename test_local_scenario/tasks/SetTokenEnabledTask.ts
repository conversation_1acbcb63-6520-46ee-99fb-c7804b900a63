import { Network } from '../helpers/constants'
import { SetTokenEnabledArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class SetTokenEnabledTask extends BaseTask<SetTokenEnabledArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'setTokenEnabled')
  }

  protected getDefaultArguments(): Partial<SetTokenEnabledArguments> {
    const networkConfig = this.getNetworkConfig()
    const commonConfig = this.getCommonConfig()
    return {
      provKey: commonConfig.KEY_PROV,
      provId: networkConfig.PROV_ID,
      tokenId: networkConfig.TOKEN_ID,
      enabled: 'true',
    }
  }
}
