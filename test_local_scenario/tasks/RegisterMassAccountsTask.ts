import { Network } from '../helpers/constants'
import { RegisterMassAccountsArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'
export class RegisterMassAccountsTask extends BaseTask<RegisterMassAccountsArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'registerMassAccounts')
  }
  protected getDefaultArguments(): RegisterMassAccountsArguments {
    const networkConfig = this.getNetworkConfig()

    return {
      numberOfAccounts: '1000',
      accountId: networkConfig.ACCOUNT_ID_1,
      accountName: networkConfig.ACCOUNT_NAME_1,
      validId: networkConfig.VALID_ID,
    }
  }
}
