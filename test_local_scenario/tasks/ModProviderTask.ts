import { Network } from '../helpers/constants'
import { ModProviderArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class ModProviderTask extends BaseTask<ModProviderArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'modifyProvider')
  }

  protected getDefaultArguments(): Partial<ModProviderArguments> {
    const networkConfig = this.getNetworkConfig()
    return {
      provId: networkConfig.PROV_ID,
      provName: networkConfig.PROV_NAME,
    }
  }
}
