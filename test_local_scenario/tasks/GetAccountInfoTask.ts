import { Network } from '../helpers/constants'
import { GetAccountAllArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class GetAccountInfoTask extends BaseTask<GetAccountAllArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'getAccountInfo')
  }

  protected getDefaultArguments(): Partial<GetAccountAllArguments> {
    return {
      validId: this.getNetworkConfig().VALID_ID,
    }
  }
}
