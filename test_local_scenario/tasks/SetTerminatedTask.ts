import { Network } from '../helpers/constants'
import { SetTerminatedArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class SetTerminatedTask extends BaseTask<SetTerminatedArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'setTerminated')
  }

  protected getDefaultArguments(): Partial<SetTerminatedArguments> {
    const commonConfig = this.getCommonConfig()
    const networkConfig = this.getNetworkConfig()
    return {
      validatorId: networkConfig.VALID_ID,
      reasonCode: commonConfig.REASON_CODE,
    }
  }
}
