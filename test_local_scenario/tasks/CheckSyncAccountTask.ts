import { Network } from '../helpers/constants'
import { CheckSyncAccountArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class CheckSyncAccountTask extends BaseTask<CheckSyncAccountArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'checkSyncAccount')
  }

  protected getDefaultArguments(): CheckSyncAccountArguments {
    const networkConfig = this.getNetworkConfig()
    return {
      accountId: networkConfig.ACCOUNT_ID_1,
      validId: networkConfig.VALID_ID,
      zoneId: networkConfig.ZONE_ID,
      accountStatus: this.getCommonConfig().STATUS_APPLYING,
    }
  }
}
