import { Network } from '../helpers/constants'
import { IndustryAccountArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class SetActiveBusinessAccountWithZoneTask extends BaseTask<IndustryAccountArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'setActiveBusinessAccountWithZone')
  }

  protected getDefaultArguments(): Partial<IndustryAccountArguments> {
    const networkConfig = this.getNetworkConfig()
    return {
      validatorId: networkConfig.VALID_ID,
      accountId: networkConfig.ACCOUNT_ID_1,
      zoneId: (networkConfig as any).BIZ_ZONE_ID,
    }
  }
}
