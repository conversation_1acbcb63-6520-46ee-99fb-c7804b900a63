import { Network } from '../helpers/constants'
import { ForceBurnTokenArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class CumulativeResetTask extends BaseTask<ForceBurnTokenArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'cumulativeReset')
  }

  protected getDefaultArguments(): Partial<ForceBurnTokenArguments> {
    const networkConfig = this.getNetworkConfig()
    const commonConfig = this.getCommonConfig()
    return {
      issuerId: networkConfig.ISSUER_ID,
      issuerKey: commonConfig.KEY_ISSUER,
    }
  }
}
