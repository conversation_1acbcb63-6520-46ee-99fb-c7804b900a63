import { Network } from '../helpers/constants'
import { RetrieveForceBurnEventArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class RetrieveForceBurnEventTask extends BaseTask<RetrieveForceBurnEventArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'retrieveForceBurnEvent')
  }

  protected getDefaultArguments(): Partial<RetrieveForceBurnEventArguments> {
    return {}
  }
}
