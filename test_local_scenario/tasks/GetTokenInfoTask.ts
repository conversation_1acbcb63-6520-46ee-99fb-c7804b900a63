import { Network } from '../helpers/constants'
import { GetTokenInfoArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class GetTokenInfoTask extends BaseTask<GetTokenInfoArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'getTokenInfo')
  }

  protected getDefaultArguments(): GetTokenInfoArguments {
    const networkConfig = this.getNetworkConfig()
    return {
      providerId: networkConfig.PROV_ID,
    }
  }
}
