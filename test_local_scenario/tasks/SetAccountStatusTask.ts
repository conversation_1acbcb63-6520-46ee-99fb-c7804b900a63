import { Network } from '../helpers/constants'
import { SetAccountStatusArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class SetAccountStatusTask extends BaseTask<SetAccountStatusArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'setAccountStatus')
  }

  protected getDefaultArguments(): Partial<SetAccountStatusArguments> {
    const commonConfig = this.getCommonConfig()
    const networkConfig = this.getNetworkConfig()
    return {
      issuerId: networkConfig.ISSUER_ID,
      reasonCode: commonConfig.REASON_CODE,
      issuerKey: commonConfig.KEY_ISSUER,
    }
  }
}
