import { Network } from '../helpers/constants'
import { MintRenewableArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class MintRenewableArgumentsTask extends BaseTask<MintRenewableArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'mintRenewable')
  }

  protected getDefaultArguments(): Partial<MintRenewableArguments> {
    const networkConfig = this.getNetworkConfig()
    return {
      mintAccountId: networkConfig.ACCOUNT_ID_2,
      ownerAccountId: networkConfig.ACCOUNT_ID_2,
      isLocked: 'false',
    }
  }
}
