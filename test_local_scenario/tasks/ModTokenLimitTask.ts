import { Network } from '../helpers/constants'
import { ModTokenLimitArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'
export class ModTokenLimitTask extends BaseTask<ModTokenLimitArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'modTokenLimit')
  }

  protected getDefaultArguments(): SyncAccountArguments {
    const networkConfig = this.getNetworkConfig()
    const commonConfig = this.getCommonConfig()
    return {
      issuerId: networkConfig.ISSUER_ID,
      issuerKey: commonConfig.KEY_ISSUER,
      limitUpdates: JSON.stringify(commonConfig.LIMIT_UPDATE_ALL),
    }
  }
}
