# ./test_local_scenario

## 概要

このディレクトリには、スマートコントラクトのテストシナリオを実行するためのテストケースが配置されています。
ローカル環境や指定の開発環境で、実際の業務フローに近い形で検証を行うことを想定しています。

## 構成

#### ディレクトリ構成

* `tasks/`: テストで使用される個別のタスクを実行するためのクラスファイル
  - 各テストシナリオで必要な処理をTaskクラスとして実装
  - 基本クラス：
    - `base-task.ts`: 全てのタスククラスの基底クラス。共通機能を提供

  - アカウント関連：
    - `RegisterAccTask.ts`: アカウント登録処理
    - `RegisterEscrowAccTask.ts`: エスクローアカウント登録処理
    - `GetAccountAllTask.ts`: アカウント情報取得処理
    - `CheckFinAccountStatusTask.ts`: アカウントステータス確認処理
    - `CheckSyncAccountTask.ts`: アカウント同期確認処理
    - `GetBizZoneAccountStatusTask.ts`: BizZoneアカウントステータス取得処理
    - `SetAccountStatusTask.ts`: アカウントステータス設定処理
    - `SetActiveBusinessAccountWithZoneTask.ts`: アクティブなビジネスアカウントとゾーンの設定処理
    - `SyncAccountTask.ts`: アカウント同期処理
    - `ModTokenLimitTask.ts`: アカウントの各取引の累積限度額の変更
    - `GetAccountInfoTask.ts`: アカウント情報取得
    - `GetAccountLimitDataTask.ts`: アカウントの各取引の累積限度額の取得
    - `RegisterMassAccountsTask.ts`: アカウント一括登録(scenario: batch processing)
    - `GetAccountAllListTask.ts`: アカウント一覧の取得(scenario: batch processing)

  - トークン関連：
    - `RegisterTokenTask.ts`: トークン登録処理
    - `GetTokenTask.ts`: トークン情報取得処理
    - `MintTokenTask.ts`: トークンミント処理
    - `CheckMintTask.ts`: トークンミント確認処理
    - `BurnTokenTask.ts`: トークン焼却処理
    - `CheckBurnTask.ts`: トークン焼却確認処理
    - `ForceBurnTokenTask.ts`: 強制トークン焼却処理
    - `MintRenewableArgumentsTask.ts`: 再生可能エネルギートークンのミント処理
    - `PartialForceBurnTokenTask.ts`: 一部強制償却処理

  - 事業者関連：
    - `RegisterProvTask.ts`: プロバイダー登録処理
    - `RegisterIssuerTask.ts`: イシュアー登録処理
    - `RegisterValidTask.ts`: バリデーター登録処理

  - 取引関連：
    - `DvpTask.ts`: DVP取引処理
    - `DvpMultiTask.ts`: 複数DVP取引処理
    - `CheckTransactionTask.ts`: 取引確認処理
    - `CheckTransactionRenewable.ts`: 再生可能エネルギー取引確認処理
    - `ApproveTask.ts`: 承認処理
    - `CheckApproveTask.ts`: 承認確認処理
    - `CheckExchangeTask.ts`: 交換確認処理
    - `TransferTask.ts`: 転送処理
    - `TransferSingleTask.ts`: 単一転送処理

  - BizZone関連：
    - `RegisterBizZoneTask.ts`: BizZone登録処理
    - `BizTerminatingTask.ts`: BizZone解約申請処理
    - `SetBizZoneTerminatedTask.ts`: BizZone解約完了設定処理
    - `AddBizZoneToIssuer.ts`: イシュアにBiz Zoneを追加
    - `DeleteBizZoneToIssuer.ts`: イシュアからBiz Zoneを削除
    - `GetIssuerWithZone.ts`: BizZoneに紐付けられたイシュアの一覧を取得

* `scenarios/`: テストシナリオファイル
  - ユーティリティ：
    - `utils.ts`: シナリオテスト用の共通ユーティリティ関数
      - `showAccountsStatus`: FINゾーンとBIZゾーンのアカウント状態を表示

  - 初期データ登録 (localFin)：
    - `2_register_prov.ts`: プロバイダー登録
    - `3_register_issuer.ts`: イシュアー登録
    - `4_register_valid.ts`: バリデーター登録
    - `5_register_token.ts`: トークン登録
    - `6_register_account.ts`: アカウント登録
    - `7_mint_token.ts`: トークンミント
    - `7_burn_token.ts`: トークン焼却
    - `11_reg_bizzone.ts`: BizZone情報登録

  - 初期データ登録 (localBiz)：
    - `2_register_prov.ts`: プロバイダー登録
    - `3_register_issuer.ts`: イシュアー登録
    - `4_register_valid.ts`: バリデーター登録
    - `5_register_token.ts`: トークン登録
    - `6_register_account.ts`: アカウント登録
    - `7_mint_token.ts`: トークンミント
    - `7_burn_token.ts`: トークン焼却
    - `7_approve.ts`: 承認設定
    - `9_mint_renewable.ts`: 再生可能エネルギートークンのミント
    - `11_dvp_renewable.ts`: DVP取引実行
    - `12_get_renewable.ts`: 再生可能エネルギートークン情報取得

  - BizZone連携：
    - `12_synchronous.ts`: BizZone利用申し込み処理
    - `13_industry.ts`: BizZone利用確定処理
    - `14_charge.ts`: BizZoneへのチャージ処理
    - `15_sync_transfer.ts`: BizZone内トークン転送
    - `16_discharge.ts`: BizZoneからのディスチャージ処理
    - `17_frozen_forceBurn.ts`: トークンの凍結→強制償却→凍結解除処理
    - `18_biz_terminating.ts`: BizZone解約申請処理
    - `19_biz_terminated.ts`: FinZone管理のBizAccount解約確定処理
    - `22_account_transaction_limit.ts`: アカウントの取引制限に関する処理

* `helpers/`: テストをサポートするユーティリティ関数や定数
  - 共通定数：
    - `constants.ts`: システム全体で使用される定数定義
      - Network列挙型（LocalFin、LocalBiz）
      - 共通設定（アカウントキー、ステータス、金額など）
      - ネットワーク別設定（プロバイダー、ゾーン、トークン情報など）

  - ユーティリティ関数：
    - `capture_output.ts`: コンソール出力をキャプチャするヘルパー関数
      - `captureConsoleOutputAsync`: 非同期関数の実行中のコンソール出力を捕捉

    - `local_task_runner.ts`: タスク実行用ヘルパー関数
      - `runTask`: 指定されたネットワークでタスクを実行
      - `runTaskAndCaptureOutput`: タスク実行と出力のキャプチャを組み合わせ

    - `multi_hardhat.ts`: Hardhat環境管理用ヘルパー関数
      - `getHardhatRuntimeEnvironment`: ネットワーク別のHardhat実行環境を取得

    - `task-arguments.ts`: タスク引数の型定義
      - 各種タスク用の引数インターフェース定義   

* `utils/`: テスト実行に必要な共通のユーティリティ機能
  - レポーター関連：
    - `reporter.ts`: テスト結果のカスタムレポーター
      - テスト実行結果の整形
      - エラー情報の詳細表示
      - 実行時間の計測と表示
  - `register_data.test.ts`: 初期データ登録
  - `main_functions.test.ts`: メインな機能を確認するためのシナリオ
  - `transaction_limit.test.ts`: アカウントの各取引ごとの累積限度額に関して確認するためのシナリオ
  - `batch_handling.test.ts`: 大量のアカウント情報の取得バッチ処理を確認するためのシナリオ
## テストシナリオの実行順序
### 1. **必須**
* **main_functions** (必須): メインな機能を確認する
### 2. **オプション** (main_functionsの後に実行)
* **transaction_limits**: アカウントの各取引ごとの累積限度額に関して確認する
* **batch_handling**: 大量のアカウント情報の取得バッチ処理を確認する

**注意:** main_functionsのシナリオを実行してからオプションのシナリオを実行してください
## テスト実行方法

### 個別のテストを実行
以下のコマンドを使用：
* **main_functions**: `npm run test-local-scenario:main_functions`
* **batch_handling**: `npm run test-local-scenario:batch_handling`
* **transaction_limits**: `npm run test-local-scenario:transaction_limits`

コマンドの説明：
このコマンドは、以下のコマンドのショートカットです：
```bash
TS_NODE_TRANSPILE_ONLY=true npx mocha --require ts-node/register ./test_local_scenario/{SCENARIO_FILE_HERE}  --reporter ./test_local_scenario/utils/reporter.ts
```
- `TS_NODE_TRANSPILE_ONLY=true`: TypeScriptのトランスパイル最適化フラグ。型チェックをスキップして実行速度を向上
- `--require ts-node/register`: TypeScriptファイルを直接実行するためのts-nodeレジストリを有効化
- `--reporter ./test_local_scenario/utils/reporter.ts`: テスト結果の出力形式をカスタマイズするレポーター設定
- `./test_local_scenario/{SCENARIO_FILE_HERE}`: 実行対象のテストファイルを指定