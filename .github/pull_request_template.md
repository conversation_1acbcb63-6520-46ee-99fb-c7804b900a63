## 概要
このPRで実装/修正した内容の簡潔な説明

## 背景と目的（Why）
- なぜこの変更が必要か
- 解決しようとしている問題は何か
- 関連Issue: #XXX

## 変更内容（What）
- [ ] 実装した機能や修正内容のリスト
- [ ] 影響を受けるコンポーネント
- [ ] 追加/変更されたファイルの概要

## 実装詳細（How）
- 採用したアプローチとその理由
- 考慮した代替案
- 技術的な設計判断

## テスト
- [ ] ユニットテストを追加/更新
- [ ] 統合テストを実行
- [ ] 手動テストの手順：
  1. 手順1
  2. 手順2

## 影響範囲とリスク
- [ ] Breaking changeの有無
- [ ] パフォーマンスへの影響
- [ ] セキュリティへの影響

## レビューポイント
- 特に注意して見てほしい箇所
- 懸念事項や相談したい点

## スクリーンショット（UIの変更がある場合）
変更前：
変更後：

## チェックリスト
- [ ] コードが自己文書化されている
- [ ] テストが追加されている
- [ ] ドキュメントが更新されている
- [ ] リンターエラーがない