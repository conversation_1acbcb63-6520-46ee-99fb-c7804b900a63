name: "[Contract] Continuous Deployment"
run-name: "[${{ github.event.inputs.environment }}][${{github.ref_name}}] Continuous Deployment"
on:
  workflow_dispatch:
    inputs:
      environment:
        type: environment
      number_of_bizzone:
        type: number
 
env:
  SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL }}

permissions:
  id-token: write
  contents: read

jobs:
  contract-migration:
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}
    steps:
      - name: Debug input values
        run: |
          echo "Environment ${{ github.event.inputs.environment }}"
          echo "Number of Bizzone ${{ github.event.inputs.number_of_bizzone }}"
      - name: "Configure AWS Credentials [${{ github.event.inputs.environment }}]"
        uses: aws-actions/configure-aws-credentials@v4.0.2
        with:
          role-to-assume: ${{ secrets.AWS_ASSUME_ROLE_ARN }}
          aws-region: ap-northeast-1
      - name: Run CodeBuild
        uses: aws-actions/aws-codebuild-run-build@v1
        with:
          project-name: ${{ github.event.inputs.environment }}-tokyo-main-dlt-contract-migration
          source-version-override: ${{ github.head_ref || github.ref_name }}
          env-vars-for-codebuild: |
              PROJECT_ENV,
              NUMBER_OF_BIZZONE
        env:
          PROJECT_ENV: ${{ github.event.inputs.environment }}
          NUMBER_OF_BIZZONE: ${{ github.event.inputs.number_of_bizzone }}
      - name: Slack Notification on Success
        if: success()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_CHANNEL: C0285NR7MPC
          SLACK_TITLE: "[${{ github.event.inputs.environment }}][Contract] migration job Success"
          SLACK_COLOR: good
          SLACK_MESSAGE: <!channel> Successfully executed the migration job.

      - name: Slack Notification on Failure
        uses: rtCamp/action-slack-notify@v2
        if: failure()
        env:
          SLACK_CHANNEL: C0285NR7MPC
          SLACK_TITLE: "[${{ github.event.inputs.environment }}][Contract] migration job Failure"
          SLACK_COLOR: danger
          SLACK_MESSAGE: <!channel> The migration job failed to execute.