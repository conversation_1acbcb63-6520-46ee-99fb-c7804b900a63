{
  "compilerOptions": {
    "target": "ES2021",
    "module": "commonjs",
    "lib": ["ES2021", "DOM"],
    "sourceMap": true,
    "strict": true,
    "noImplicitAny": false,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "noUnusedParameters": true,
    "noUnusedLocals": true,
    "types": [
      "@types/node",
      "@types/chai",
      "@types/mocha"
    ],
    "baseUrl": "./",
    "paths": {
      "@deploy/*": ["./deploy/*"],
      "@test/*": ["./test/*"],
      "@tasks/*": ["./tasks/*"],
      "@tools/*": ["./tools/*"],
      "@scripts/*": ["./scripts/*"],
      "@contracts/*": ["./types/contracts/*"],
      "@testLocalScenario/*": ["./test_local_scenario/*"],
      "@/*": ["*"],
    }
  },
  "include": ["**/*.{ts,tsx}", "test", "deploy", "tasks", "tools", "scripts", "testLocalScenario",
  "types/**/*", "test_local_scenario/**/*","bin/**/*"],
  "exclude": ["node_modules"],
  "files": ["./hardhat.config.ts"]
}