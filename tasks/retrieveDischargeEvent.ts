import path from 'path'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printSection, printTable } from '@tasks/common/tools'

wrappedTask('retrieveDischargeEvent', 'check if DischargeRequested event is emitted', {
  filePath: path.basename(__filename),
}).setAction(async (_, hre) => {
  const { network, getNamedAccounts, ethers } = hre

  printSection({ title: `Retrieve DischargeRequestedEvent on ${network.name} network: ${path.basename(__filename)}` })

  const ibcTokenContractDeployed = await hre.deployments.get('IBCToken')
  const ibcTokenContract = await ethers.getContractAt('IBCToken', ibcTokenContractDeployed.address)
  const { signer1 } = await getNamedAccounts()
  const signer1Hardhat = await ethers.getSigner(signer1)
  ibcTokenContract.connect(signer1Hardhat)

  const dischargeRequestedEvent = await ibcTokenContract.queryFilter(ibcTokenContract.filters.DischargeRequested())
  console.log(`** DischargeRequested Event Information **\n`)

  if (dischargeRequestedEvent.length > 0) {
    const formattedArgs = dischargeRequestedEvent.map((log) => ({
      validatorId: log.args[0],
      accountId: log.args[1],
      fromZoneId: log.args[2],
      amount: log.args[3].toString(),
      traceId: log.args[4],
    }))

    formattedArgs.forEach((args) => printTable({ data: args }))
  } else {
    console.log('No DischargeRequested events emitted.')
  }
})
