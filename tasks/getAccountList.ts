import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable } from './common/tools'
import { Validator } from '@/types/contracts/Validator'

wrappedTask('getAccountList', 'Get all Account information associated with Validator.', {
  filePath: path.basename(__filename),
})
  .addParam('validId', 'validator id')
  .setAction(async (taskArguments, hre) => {
    const { ethers } = hre

    const validatorId = convertToHex({ hre, value: taskArguments.validId || '' })

    const { contract } = await getContractWithSigner<Validator>({ hre, contractName: 'Validator' })

    const offset = 0
    const limit = 100
    const sort_order = 'asc'

    const receipt = await contract.getAccountList(validatorId, offset, limit, sort_order)

    console.log('*** Account List ***')
    receipt[0].forEach((account, index) => {
      const accountInfo = {
        accountId: account.accountId,
        balance: account.balance,
        accountStatus: ethers.toUtf8String(account.accountStatus.toString()),
      }

      console.log(`--- Account ${index + 1} ---`)
      printTable({ data: accountInfo })
    })

    console.log(`Total Count: ${receipt.totalCount}`)
  })
