import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'
import { Account } from '@/types/contracts/Account'

wrappedTask('isTerminated', 'is Terminated', { filePath: path.basename(__filename) })
  .addParam('accountId', 'account Id')
  .setAction(async (taskArguments, hre) => {
    try {
      const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })

      console.log(`** isTerminated Parameters **\n`)
      const params = {
        accountId,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner<Account>({ hre, contractName: 'Account' })

      const receipt = await contract.isTerminated(accountId)

      console.log(`** isTerminated Receipt Information **\n`)
      printTable({ data: receipt })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
