import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { executeReceipt } from './common/executeReceipt'
import { printTable, showErrorDetails } from './common/tools'

wrappedTask('addAccount', 'add Account', { filePath: path.basename(__filename) })
  .addParam('accountId', 'account Id')
  .addParam('validatorId', 'validator Id')
  .addParam('accountName', 'account Name')
  .setAction(async (taskArguments, hre) => {
    try {
      const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })
      const validatorId = convertToHex({ hre, value: taskArguments.validatorId || '' })
      const accountName = convertToHex({ hre, value: taskArguments.accountName || '' })
      const traceId = convertToHex({ hre, value: 'trace1' })

      console.log(`** addAccount Parameters **\n`)
      const params = {
        accountId,
        validatorId,
        accountName,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner({ hre, contractName: 'Validator' })

      await executeReceipt(
        contract.addAccount(
          validatorId,
          accountId,
          accountName,
          [50000, 50000, 50000, 50000, 50000, [50000, 50000, 50000, 50000, 50000, 50000]],
          traceId,
        ),
      )
    } catch (error) {
      showErrorDetails({ error })
    }
  })
