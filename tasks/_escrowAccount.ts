import path from 'path'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import web3 from 'web3'
import { JPYTokenTransferBridge } from '@/types/contracts/JPYTokenTransferBridge'

wrappedTask('_escrowAcc', 'escrow account', { filePath: path.basename(__filename) })
  .addParam('regionId', 'region id')
  .addParam('dstRegionId', 'dst region id')
  .setAction(async (taskArguments, hre) => {
    const regionId = Number(taskArguments.regionId || '')
    const dstRegionId = Number(taskArguments.dstRegionId || '')

    const toUint256HexPadded = (x) => web3.utils.padLeft(web3.utils.toHex(x), 64)

    const { contract } = await getContractWithSigner<JPYTokenTransferBridge>({
      hre,
      contractName: 'JPYTokenTransferBridge',
    })

    const escrowAccount = toUint256HexPadded(await contract.escrowAccount(regionId, dstRegionId))
    console.log(escrowAccount)
  })
