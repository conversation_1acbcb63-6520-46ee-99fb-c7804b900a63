import path from 'path'
import { wrappedTask } from '@/tasks/common/taskMiddleware'

wrappedTask('transactionReceipt', 'check transaction receipt', {
  filePath: path.basename(__filename),
})
  .addParam('transactionHash', 'transaction hash')
  .addParam('method', 'check method')
  .setAction(async (taskArguments, hre) => {
    const { ethers } = hre

    const { transactionHash = '', method = '' } = taskArguments

    const result = await ethers.provider.getTransactionReceipt(transactionHash)
    if (!result) {
      console.log('ERR: no transactionReceipt')
    }
    switch (method) {
      case 'receipt': {
        console.log(JSON.stringify(result))
        break
      }
      case 'logs': {
        if (!result.logs) {
          console.log('ERR: no logs')
        }
        console.log(JSON.stringify(result.logs))
        break
      }
      case 'reason': {
        const tx = await ethers.provider.getTransaction(transactionHash)
        const response = await ethers.provider.call(
          {
            to: tx.to,
            from: tx.from,
            nonce: tx.nonce,
            gasLimit: tx.gasLimit,
            gasPrice: tx.gasPrice,
            data: tx.data,
            value: tx.value,
            chainId: tx.chainId,
            type: tx.type ?? undefined,
            accessList: tx.accessList,
          },
          tx.blockNumber,
        )
        if (!response.substring(138)) {
          console.log('ERR: no revertReason')
          break
        }
        if (response.length >= 202) {
          const reason = ethers.toUtf8String('0x' + response.substring(138))
          console.log(`revertReason: ${reason}`)
        } else {
          console.log(JSON.stringify(response))
        }
        break
      }
    }
  })
