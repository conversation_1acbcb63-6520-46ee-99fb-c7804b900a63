import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'
import { FinancialCheck } from '@/types/contracts/FinancialCheck'

wrappedTask('checkExchange', 'Check if an account can charge.', { filePath: path.basename(__filename) })
  .addParam('accountId', 'account id')
  .addParam('fromZoneId', 'from zone id')
  .addParam('toZoneId', 'to zone id')
  .addParam('amount', 'amount')
  .setAction(async (taskArguments, hre) => {
    const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })
    const fromZoneId = Number(taskArguments.fromZoneId || '')
    const toZoneId = Number(taskArguments.toZoneId || '')
    const amount = Number(taskArguments.amount || '')

    console.log(`** checkExchange Parameters **\n`)
    const params = {
      accountId,
      fromZoneId,
      toZoneId,
      amount,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner<FinancialCheck>({ hre, contractName: 'FinancialCheck' })

      const { success, err } = await contract.checkExchange(accountId, fromZoneId, toZoneId, amount)

      const formattedReceipt = {
        result: success ? 'ok' : 'failed',
        reason: err,
      }

      console.log(`** checkExchange receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
