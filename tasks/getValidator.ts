import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'
import { Validator } from '@/types/contracts/Validator'

wrappedTask('getValidator', 'get Validator', { filePath: path.basename(__filename) })
  .addParam('validatorId', 'validator Id')
  .setAction(async (taskArguments, hre) => {
    try {
      const validatorId = convertToHex({ hre, value: taskArguments.validatorId || '' })

      console.log(`** getValidator Parameters **\n`)
      const params = {
        validatorId,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner<Validator>({ hre, contractName: 'Validator' })

      const { issuerId, name, err } = await contract.getValidator(validatorId)

      const validatorInfo = {
        issuerId,
        name,
        error: err,
      }

      console.log(`** Validator Information **\n`)
      printTable({ data: validatorInfo })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
