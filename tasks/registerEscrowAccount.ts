import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { executeReceipt } from './common/executeReceipt'

wrappedTask('registerEscrowAccount', 'register escrow account', {
  filePath: path.basename(__filename),
})
  .addParam('validId', 'validator id')
  .addParam('accountId', 'account id')
  .addParam('accountName', 'account name')
  .setAction(async (taskArguments, hre) => {
    const limitAmounts = [
      ************,
      ************,
      ************,
      ************,
      ************,
      [************, ************, ************, ************, ************, ************],
    ]

    const validatorId = convertToHex({ hre, value: taskArguments.validId || '' })
    const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })
    const accountName = convertToHex({ hre, value: taskArguments.accountName || '' })
    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner({ hre, contractName: 'Validator' })

    await executeReceipt(contract.addAccount(validatorId, accountId, accountName, limitAmounts, traceId))
  })
