import path from 'path'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'
import { Provider } from '@/types/contracts/Provider'

wrappedTask('getZone', 'get Zone', { filePath: path.basename(__filename) }).setAction(async (_, hre) => {
  try {
    console.log(`** getZone Parameters **\n`)
    console.log('No parameters required')

    const { contract } = await getContractWithSigner<Provider>({ hre, contractName: 'Provider' })

    const { zoneId, zoneName, err } = await contract.getZone()

    const zoneInfo = {
      zoneId,
      zoneName,
      error: err,
    }

    console.log(`** Zone Information **\n`)
    printTable({ data: zoneInfo })
  } catch (error) {
    showErrorDetails({ error })
  }
})
