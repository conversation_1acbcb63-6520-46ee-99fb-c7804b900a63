import * as Tools from '@tasks/common/tools'
import { AwsKmsSigner, EthersAwsKmsSignerConfig } from '@tools/common/awsKmsSigner'
import { HardhatRuntimeEnvironment } from 'hardhat/types'
import { envVers } from '@/envVers'

type GetBackupSignatureParams = {
  hre: HardhatRuntimeEnvironment
  salt: string
}

export const getBackupSignature = async ({ hre, salt }: GetBackupSignatureParams) => {
  const provider = new hre.ethers.JsonRpcProvider(envVers.provider.url)
  const kmsConfig: EthersAwsKmsSignerConfig = {
    credentials: {
      accessKeyId: envVers.aws.credentials.accessKeyId,
      secretAccessKey: envVers.aws.credentials.secretAccessKey,
      ...(hre.network.name.includes('main') && {
        sessionToken: envVers.aws.credentials.sessionToken,
      }),
    },
    region: envVers.aws.region,
    keyId: hre.network.name.includes('Fin') ? envVers.kms.keys.fin : envVers.kms.keys.biz,
    ...(hre.network.name.includes('local') && {
      endpoint: envVers.kms.endpoint,
    }),
  }
  const kmsSigner = new AwsKmsSigner(kmsConfig, provider)

  const deadline = await Tools.getTime()
  const sig = await kmsSigner.sign(['string', 'uint256'], [salt, deadline])
  return { deadline, sig }
}
