import path from 'path'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { convertToHex } from './convertToHex'
import * as Tools from './tools'

wrappedTask('getKmsSig', 'Get Kms Sig', { filePath: path.basename(__filename) })
  .addParam('types', 'types') // 例: 'bytes32,uint16,string,uint256'
  .addParam('data', 'data') // 例: '0x00,9999,abc,87125345'
  .setAction(async (taskArguments, hre) => {
    try {
      const types: string[] = taskArguments.types.split(',')
      const rawData: string[] = taskArguments.data.split(',')

      if (types.length !== rawData.length) {
        throw new Error('types と data の要素数が一致しません')
      }

      // paramsの組み立て
      const params = types.map((type, index) => {
        const value = rawData[index]
        switch (type) {
          case 'bytes32':
            return convertToHex({ hre, value: value || '' })
          case 'uint16':
          case 'uint256':
            return Number(value)
          case 'string':
            return value
          default:
            throw new Error(`未対応のtype: ${type}`)
        }
      })

      const kmsSigner = kmsSignerProvider({ hre })
      const kmsSig = await kmsSigner.sign(types, params)

      console.log(`kmsSig:${kmsSig}`)
    } catch (error) {
      Tools.showErrorDetails({ error })
    }
  })
