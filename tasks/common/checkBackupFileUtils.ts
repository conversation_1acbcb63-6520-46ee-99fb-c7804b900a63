import { AccountAllType, IssuerAllType, ValidatorAllType } from './types'
import { checkArraysEqual, getItemArray, sortArrays } from './utils'

type assertEqualsValidatorIdIssuerIdAccIdParams = {
  validators: ValidatorAllType[]
  issuers: IssuerAllType[]
  accountIds: string[]
}

export const assertEqualsValidatorIdIssuerIdAccId = ({
  validators,
  issuers,
  accountIds,
}: assertEqualsValidatorIdIssuerIdAccIdParams) => {
  // sort validators and issuers all by issuerId
  const params = [
    { arr: validators, key: 2 },
    { arr: issuers, key: 0 },
  ]
  const [validatorsByOrder, issuersByOrder] = sortArrays(params)

  const accountsByValidator: unknown[] = []

  console.log(`Check1-1: Checking validator's accountIds and issuer's accountIds...`)
  if (validatorsByOrder.length != issuersByOrder.length) {
    return `Error1-1: The number of issuers and validators are not equel`
  } else {
    for (let i = 0; i < validatorsByOrder.length; i++) {
      if (validatorsByOrder[i][2].toString() != issuersByOrder[i][0].toString()) {
        return `Error1-2: validator's issuerId check false`
      }
      if (!checkArraysEqual(validatorsByOrder[i][9], issuersByOrder[i][6])) {
        return `Error1-3: validator${i + 1} and issuer${i + 1}'s accountId list check false`
      }

      // collect account ids for following check
      if (validatorsByOrder[i][9].length != 0) {
        accountsByValidator.push(...validatorsByOrder[i][9])
      }
    }
    console.log(`Check1-1: Success`)
    // sort all account ids
    const accountsIdsByValidator = getItemArray(accountsByValidator, 0).sort()

    console.log(`Check1-2: Checking total accounts with accounts linked by validator(issuers)...`)
    if (accountsIdsByValidator.length != accountIds.length) {
      return 'Error1-4: The number of accounts and validators linked accounts are not equel'
    } else {
      if (!checkArraysEqual(accountsIdsByValidator, accountIds)) {
        return `Error1-5: validator's accounts check false`
      } else {
        console.log(`Check1-2: Success`)
        return `Success`
      }
    }
  }
}

type assertEqualsAccountBalanceParams = {
  balances: number[]
  token: unknown[]
}

export const assertEqualsAccountBalance = ({ balances, token }: assertEqualsAccountBalanceParams) => {
  console.log(`Check2: Checking total balance of all accounts is equal to  with total supply...`)
  const totalBalance = balances.reduce((a, b) => a + b)
  const totalSupply = token[3]
  if (totalBalance != totalSupply) {
    return `Error2: Total balance doesn't match with total supply`
  } else {
    return 'Success'
  }
}

type assertEqualsAccountsFinAccountsParams = {
  finAccountIds: string[]
  accountIds: string[]
}

export const assertEqualsAccountsFinAccounts = ({
  finAccountIds,
  accountIds,
}: assertEqualsAccountsFinAccountsParams) => {
  console.log(`Check3: Checking financial accounts are equal with accounts...`)
  if (!checkArraysEqual(finAccountIds.sort(), accountIds.sort())) {
    return `Error3: Financial Zone Account Ids don't match with Account Ids`
  } else {
    return 'Success'
  }
}

type assertEqualsAccountsBizAccountsParams = {
  bizaccounts: unknown[]
  accounts: AccountAllType[]
}

export const assertEqualsAccountsBizAccounts = ({ bizaccounts, accounts }: assertEqualsAccountsBizAccountsParams) => {
  console.log(`Check4: Checking business accounts zone ids are equal with accounts zone ids...`)

  const params = [
    { arr: bizaccounts, key: 0 },
    { arr: accounts, key: 0 },
  ]

  const [bizaccountsByOrder, accountsByOrder] = sortArrays(params)

  for (let i = 0; i < bizaccountsByOrder.length; i++) {
    const zoneIds = bizaccountsByOrder[i][1].map((v) => v[0])
    const zoneIdsByAccount = accountsByOrder[i][3]
    if (checkArraysEqual(zoneIds.sort(), zoneIdsByAccount.sort())) {
      return `Error4: Financial Zone Account Ids don't match with Account Ids`
    }
  }

  return 'Success'
}
