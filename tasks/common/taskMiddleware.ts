import { task as originalTask } from 'hardhat/config'
import type { ActionType, ConfigurableTaskDefinition } from 'hardhat/types'
import { printNetworkConsole } from '@/tasks/common/printNetworkConsole'

type WrappedTaskOptions = {
  filePath?: string
}

// headerで実行したい
export function wrappedTask(
  name: string,
  description?: string,
  { filePath }: WrappedTaskOptions = {},
): ConfigurableTaskDefinition {
  const newTask = originalTask(name, description)

  const originalSetAction = newTask.setAction.bind(newTask)
  newTask.setAction = (userAction: ActionType<any>) => {
    const wrappedAction: ActionType<any> = async (taskArgs, hre, runSuper) => {
      printNetworkConsole({ hre, path: filePath })
      return userAction(taskArgs, hre, runSuper)
    }
    return originalSetAction(wrappedAction)
  }

  return newTask
}
