import { HardhatRuntimeEnvironment } from 'hardhat/types'
import { envVers } from '@/envVers'
import { AwsKmsSigner, EthersAwsKmsSignerConfig } from '@/tools/common/awsKmsSigner'

type KmsSignerProviderOptions = {
  hre: HardhatRuntimeEnvironment
}

export const kmsSignerProvider = ({ hre }: KmsSignerProviderOptions): AwsKmsSigner => {
  const { network, ethers } = hre

  const provider = new ethers.JsonRpcProvider(envVers.provider.url)
  const kmsConfig: EthersAwsKmsSignerConfig = {
    credentials: {
      accessKeyId: envVers.aws.credentials.accessKeyId,
      secretAccessKey: envVers.aws.credentials.secretAccessKey,
      ...(network.name.includes('main') && {
        sessionToken: envVers.aws.credentials.sessionToken,
      }),
    },
    region: envVers.aws.region,
    keyId: network.name.includes('Fin') ? envVers.kms.keys.fin : envVers.kms.keys.biz,
    ...(network.name.includes('local') && {
      endpoint: envVers.kms.endpoint,
    }),
  }

  return new AwsKmsSigner(kmsConfig, provider)
}
