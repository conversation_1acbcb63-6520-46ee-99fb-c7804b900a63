import { BaseContract, Contract } from 'ethers'
import { HardhatRuntimeEnvironment } from 'hardhat/types'

type GetContractWithSignerParams = {
  hre: HardhatRuntimeEnvironment
  contractName: string
  hasSigner?: boolean
  hasDeployer?: boolean
}

export const getContractWithSigner = async <T extends BaseContract = Contract>({
  hre,
  contractName,
}: GetContractWithSignerParams): Promise<{
  deployed: Awaited<ReturnType<typeof hre.deployments.get>>
  contract: T
  deployer: Awaited<ReturnType<typeof hre.ethers.getSigner>>
}> => {
  const { ethers, deployments, getNamedAccounts } = hre

  const contractDeployed = await deployments.get(contractName)
  const contract = (await ethers.getContractAt(contractName, contractDeployed.address)) as unknown as T

  const { deployer: deployerAccount } = await getNamedAccounts()
  const deployerHardhat = await ethers.getSigner(deployerAccount)

  await contract.connect(deployerHardhat)

  return {
    deployed: contractDeployed,
    contract,
    deployer: deployerHardhat,
  }
}
