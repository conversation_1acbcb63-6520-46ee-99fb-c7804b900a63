import * as fs from 'fs'
import * as path from 'path'
import * as ethSigUtil from 'eth-sig-util'
import Web3 from 'web3'

function showRes({ res }) {
  console.log('txHash: ' + res.transactionHash)
  console.log('  [receipt]')
  console.log('    blockHash: ' + res.blockHash)
  console.log('    blockNumber: ' + res.blockNumber)
  console.log('    from: ' + res.from)
  console.log()
}

function showEthersRes({ res }) {
  const data = {
    txHash: res.transactionHash || res.hash,
    txStatus: res.status ? 'Success' : 'Failed',
    blockHash: res.blockHash,
    blockNumber: res.blockNumber.toString(),
    from: res.from,
  }

  printTable({ data })
}

function getTime() {
  return Math.floor(Date.now() / 1000) + 10
}

function convMisc(web3, value) {
  if (value.substring(0, 2) == '0x') {
    return value
  } else {
    const conv = parseInt(value, 10)
    const str = String(conv)
    if (value === str) {
      // 右詰めの32byteにする
      return web3.utils.padLeft(web3.utils.toHex(value), 64)
      // return web3.utils.numberToHex(value);
    }
  }
  return web3.utils.utf8ToHex(value)
}

function extSign(web3, privateKey, typesArray, parameters) {
  const abi = web3.eth.abi.encodeParameters(typesArray, parameters)
  const hash = web3.utils.keccak256(abi)
  return [ethSigUtil.personalSign(Buffer.from(privateKey, 'hex'), { data: hash }), hash]
}

function recoverSign(hash, signature) {
  const msgParams = {
    data: hash,
    sig: signature,
  }
  return ethSigUtil.recoverPersonalSignature(msgParams)
}

function showTx(tx) {
  console.log('txHash: ' + tx.tx)
  console.log('  [receipt]')
  console.log('    blockHash: ' + tx.receipt.blockHash)
  console.log('    blockNumber: ' + tx.receipt.blockNumber)
  console.log('    from: ' + tx.receipt.from)
  for (let lp = 0; lp < Object.keys(tx.logs).length; lp++) {
    console.log('  [logs]:' + lp)
    console.log('    address: ' + tx.logs[lp].address)
    console.log('    event: ' + tx.logs[lp].event)
    console.log('    args: ' + JSON.stringify(tx.logs[lp].args))
  }
  console.log()
}

function showThrow(web3, e) {
  console.log('*********** EXCEPTION ***********')
  if (e.tx != null) {
    console.log(`throw: ${e.name}`)
    console.log(`txHash: ${e.tx}`)
    console.log(`  [receipt]`)
    console.log(`    blockHash: ${e.receipt.blockHash}`)
    console.log(`    blockNumber: ${e.receipt.blockNumber}`)
    console.log(`    from: ${e.receipt.from}`)
    console.log(`    to  : ${e.receipt.to}`)
    console.log(`    status: ${e.receipt.status}`)
    if (e.receipt.revertReason == null) {
      console.log('    no revertReason')
    } else if (e.receipt.revertReason.length >= 202) {
      const reasonLen = web3.utils.hexToNumber('0x' + e.receipt.revertReason.substr(74, 64))
      console.log(`    revertReason: ${web3.utils.hexToUtf8('0x' + e.receipt.revertReason.substr(138, 2 * reasonLen))}`)
    } else {
      console.log('    bad revertReason: ' + JSON.stringify(e.receipt.revertReason))
    }
    console.log('*********************************')
    console.error(e.hijackedStack)
  } else {
    console.error(e)
  }
  console.log('*********************************')
}

function genContract(web3, provaddr, url) {
  const abi = JSON.parse(fs.readFileSync(path.resolve(__dirname + url)).toString())
  return new web3.eth.Contract(abi.abi, provaddr)
}

function convertResponse(pram) {
  pram.forEach(() => {})
  return
}

type PrintTableParams = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: Record<string, any>
  useErrorLog?: boolean
}
function printTable({ data, useErrorLog = false }: PrintTableParams) {
  const headers = ['Key', 'Value', 'ASCII']
  const rows = Object.entries(data).map(([key, value]) => {
    let asciiValue = ''
    if (typeof value === 'string' && value.startsWith('0x')) {
      try {
        const decodedValue = Web3.utils.hexToAscii(value).replace(/\u0000/g, '') // Remove null characters
        if (/^[\x00-\x7F]*$/.test(decodedValue)) {
          asciiValue = decodedValue
        }
      } catch (error) {
        // Handle error if value is not valid hex
      }
    }
    return [key, value.toString(), asciiValue]
  })

  const columnWidths = headers.map((header, i) => Math.max(...rows.map((row) => row[i].length), header.length))

  const printRow = (row) => {
    const line = row.map((cell, i) => cell.padEnd(columnWidths[i])).join(' | ')
    if (useErrorLog) {
      console.error(line)
    } else {
      console.log(line)
    }
  }

  printRow(headers)
  if (useErrorLog) {
    console.error(columnWidths.map((width) => '-'.repeat(width)).join('-|-'))
    rows.forEach(printRow)
    console.error(columnWidths.map((width) => '-'.repeat(width)).join('-|-') + '\n')
  } else {
    console.log(columnWidths.map((width) => '-'.repeat(width)).join('-|-'))
    rows.forEach(printRow)
    console.log(columnWidths.map((width) => '-'.repeat(width)).join('-|-') + '\n')
  }
}

type PrintSectionParams = {
  title: string
}
function printSection({ title }: PrintSectionParams) {
  const totalLength = title.length + 4 // Title length + 4 for padding and asterisks
  const border = '*'.repeat(totalLength)

  console.log('\n' + border)
  console.log(`* ${title} *`)
  console.log(border + '\n')
}

type ShowErrorDetailsParams = {
  error: Error & {
    code?: string
    method?: string
    reason?: string
    receipt?: {
      transactionHash: string
      blockNumber: number
      revertReason?: string
    }
  }
}
function showErrorDetails({ error }): void {
  error = error as ShowErrorDetailsParams
  console.error('** Error Details **\n')
  if (!error) {
    console.error('No error information available.')
    return
  }

  // 基本的なエラー情報を抽出
  const basicInfo = {
    ErrorCode: error.code || 'No code provided',
    ErrorMehtod: error.method || 'No method provided',
    ErrorReason: error.reason || 'No reason provided',
    // ErrorMessage: error.message || 'No message provided',
    // ErrorType: error.name || 'Unknown Error',
    // StackTrace: (error.stack || '').split('\n').slice(0, 3).join('\n'), // スタックトレースの先頭3行だけ表示
  }

  // エラーオブジェクトから追加情報を取得（存在する場合）
  const additionalInfo: { TransactionHash?: string; BlockNumber?: number; RevertReason?: string } = {}
  if (error.receipt) {
    additionalInfo.TransactionHash = error.receipt.transactionHash
    additionalInfo.BlockNumber = error.receipt.blockNumber
    additionalInfo.RevertReason = error.receipt.revertReason || 'No revert reason provided'
  }

  // テーブル形式で基本情報を出力
  printTable({ data: basicInfo, useErrorLog: true })

  // 追加情報がある場合はそれも出力
  if (Object.keys(additionalInfo).length > 0) {
    console.log('\nAdditional Error Information:')
    printTable({ data: additionalInfo, useErrorLog: true })
  }

  console.error('** Stack Trace:')
  console.error(error.stack)
}

const ADDR_SENDER = '******************************************'

export {
  ADDR_SENDER,
  convMisc,
  extSign,
  genContract,
  getTime,
  printSection,
  printTable,
  recoverSign,
  showErrorDetails,
  showEthersRes,
  showRes,
  showThrow,
  showTx,
}
