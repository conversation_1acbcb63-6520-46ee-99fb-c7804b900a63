import fs from 'fs'
import { md5 } from './md5Utils'

type SaveBackupToJsonParams<T> = {
  data: T
  fileName: string
  networkName: string
}

export const saveBackupToJson = async <T>({ data, fileName, networkName }: SaveBackupToJsonParams<T>) => {
  try {
    const json = JSON.stringify(data)
    const path = `./scripts/backup-restore/backupfiles/${networkName}/${fileName}.json`
    await fs.writeFileSync(path, json, 'utf8')
    await md5({ obj: json, item: fileName, network: networkName })
  } catch (err) {
    console.log(err)
  } finally {
    console.log(`${fileName}: The data has been successfully written to the JSON file.`)
  }
}
