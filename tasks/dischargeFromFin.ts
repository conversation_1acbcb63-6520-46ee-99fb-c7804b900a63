import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails, showEthersRes } from '@tasks/common/tools'
import { toUint256HexPadded } from '@tasks/common/toUint256HexPadded'
import { JPYTokenTransferBridge } from '@/types/contracts/JPYTokenTransferBridge'

wrappedTask('dischargeFromFin', 'Discharge DCJPY from Biz to Fin, but started from Fin.', {
  filePath: path.basename(__filename),
})
  .addParam('accountId', 'account id')
  .addParam('fromZoneId', 'from zone id')
  .addParam('toZoneId', 'to zone id')
  .addParam('amount', 'amount')
  .addParam('timeoutHeight', 'timeoutHeight')
  .setAction(async (taskArguments, hre) => {
    const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })
    const fromZoneId = Number(taskArguments.fromZoneId || '')
    const toZoneId = Number(taskArguments.toZoneId || '')
    const amount = Number(taskArguments.amount || '')
    const timeoutHeight = Number(taskArguments.timeoutHeight || '')
    const traceId = convertToHex({ hre, value: 'traceId' })

    console.log(`** Input parameter Information **\n`)
    printTable({
      data: {
        accountId,
        fromZoneId,
        toZoneId,
        amount,
        timeoutHeight,
        traceId,
      },
    })

    const { contract } = await getContractWithSigner<JPYTokenTransferBridge>({
      hre,
      contractName: 'JPYTokenTransferBridge',
    })

    try {
      const receipt = await contract.discharge(accountId, fromZoneId, toZoneId, amount, timeoutHeight, traceId)
      console.log(`** JPYTokenTransferBridge.transfer receipt Information **\n`)
      const res = await receipt.wait()
      showEthersRes({ res })

      const packetSequences = toUint256HexPadded(receipt)
      console.log(packetSequences)
    } catch (error) {
      showErrorDetails({ error })
    }
  })
