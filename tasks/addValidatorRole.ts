import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { executeReceipt } from './common/executeReceipt'
import { getTime } from './common/tools'
import { Validator } from '@/types/contracts/Validator'

wrappedTask('addValidatorRole', 'add validator role', { filePath: path.basename(__filename) })
  .addParam('validatorId', 'validator id')
  .addParam('validatorKey', 'validator key')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })

    const validatorId = convertToHex({ hre, value: taskArguments.validatorId || '' })
    const validatorKey = taskArguments.validatorKey || ''
    const traceId = convertToHex({ hre, value: 'trace1' })

    const addrValidator = new hre.ethers.Wallet(validatorKey).address

    const { contract } = await getContractWithSigner<Validator>({ hre, contractName: 'Validator' })

    console.log(`*** add validator role: ${validatorId}=${addrValidator}`)

    const deadline = await getTime()
    const kmsSig = await kmsSigner.sign(['bytes32', 'address', 'uint256'], [validatorId, addrValidator, deadline])

    await executeReceipt(contract.addValidatorRole(validatorId, addrValidator, traceId, deadline, kmsSig))
  })
