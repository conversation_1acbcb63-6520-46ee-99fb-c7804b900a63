import path from 'path'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'
import { FinancialCheck } from '@/types/contracts/FinancialCheck'

wrappedTask('getIssuerWithZone', 'Get Issuer With Zone.', { filePath: path.basename(__filename) })
  .addParam('zoneId', 'zone id')
  .setAction(async (taskArguments, hre) => {
    const zoneId = Number(taskArguments.zoneId || '')

    console.log(`** getIssuerWithZone Parameters **\n`)
    const params = {
      zoneId,
    }
    printTable({ data: params })

    const offset = 0
    const limit = 100

    try {
      const { contract } = await getContractWithSigner<FinancialCheck>({ hre, contractName: 'FinancialCheck' })

      const { issuers, totalCount } = await contract.getIssuerWithZone(zoneId, offset, limit)

      console.log(`** Issuer List Information (Total: ${totalCount}) **\n`)
      issuers.forEach(({ issuerId, name, bankCode }, index) => {
        console.log(`[${index + 1}]`)
        const issuerInfo = {
          issuerId,
          name,
          bankCode,
        }
        printTable({ data: issuerInfo })
      })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
