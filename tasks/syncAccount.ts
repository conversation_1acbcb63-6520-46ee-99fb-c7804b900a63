import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import web3 from 'web3'
import { AccountSyncBridge } from '@/types/contracts/AccountSyncBridge'

wrappedTask('syncAccount', 'Sync account status from Biz zone to Fin zone.', {
  filePath: path.basename(__filename),
})
  .addParam('validatorId', 'validator id')
  .addParam('accountId', 'account id')
  .addParam('accountName', 'account name')
  .addParam('fromZoneId', 'from zone id')
  .addParam('zoneName', 'zone name')
  .addParam('accountStatus', 'account status')
  .addParam('reasonCode', 'reasonCode')
  .addParam('approvalAmount', 'Approved amount')
  .addParam('traceId', 'trace id')
  .addParam('timeoutHeight', 'timeoutHeight')
  .setAction(async (taskArguments, hre) => {
    const validatorId = convertToHex({ hre, value: taskArguments.validatorId || '' })
    const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })
    const accountName = convertToHex({ hre, value: taskArguments.accountName || '' })
    const fromZoneId = Number(taskArguments.fromZoneId || '')
    const accountStatus = convertToHex({ hre, value: taskArguments.accountStatus || '' })
    const reasonCode = convertToHex({ hre, value: taskArguments.reasonCode || '' })
    const traceId = convertToHex({ hre, value: taskArguments.traceId || '' })
    const timeoutHeight = Number(taskArguments.timeoutHeight || '')
    const zoneName = taskArguments.zoneName || ''
    const approvalAmount = taskArguments.approvalAmount || '0'

    const toUint256HexPadded = (x) => web3.utils.padLeft(web3.utils.toHex(x), 64)

    const { contract } = await getContractWithSigner<AccountSyncBridge>({ hre, contractName: 'AccountSyncBridge' })

    const packetSequences = toUint256HexPadded(
      await contract.syncAccount(
        validatorId,
        accountId,
        accountName,
        fromZoneId,
        zoneName,
        accountStatus,
        reasonCode,
        approvalAmount,
        traceId,
        timeoutHeight,
      ),
    )
    console.log(packetSequences)
  })
