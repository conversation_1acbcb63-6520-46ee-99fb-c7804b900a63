import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'
import { Validator } from '@/types/contracts/Validator'

wrappedTask('getDestinationAccount', 'get DestinationAccount', { filePath: path.basename(__filename) })
  .addParam('accountId', 'account Id')
  .setAction(async (taskArguments, hre) => {
    try {
      const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })

      console.log(`** getDestinationAccount Parameters **\n`)
      const params = {
        accountId,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner<Validator>({ hre, contractName: 'Validator' })

      const { accountName, err } = await contract.getDestinationAccount(accountId)

      const accountInfo = {
        accountName: accountName,
        error: err,
      }

      console.log(`** Destination Account Information **\n`)
      printTable({ data: accountInfo })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
