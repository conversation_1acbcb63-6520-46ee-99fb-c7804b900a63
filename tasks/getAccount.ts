import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'
import { Issuer } from '@/types/contracts/Issuer'

wrappedTask('getAccount', 'get Account', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'issuer Id')
  .addParam('accountId', 'account Id')
  .setAction(async (taskArguments, hre) => {
    try {
      const issuerId = convertToHex({ hre, value: taskArguments.issuerId || '' })
      const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })

      console.log(`** getAccount Parameters **\n`)
      const params = {
        issuerId,
        accountId,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner<Issuer>({ hre, contractName: 'Issuer' })

      const { accountName, balance, accountStatus, reasonCode, err } = await contract.getAccount(issuerId, accountId)

      const accountInfo = {
        accountName,
        balance,
        accountStatus,
        reasonCode,
        error: err,
      }

      console.log(`** Account Information **\n`)
      printTable({ data: accountInfo })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
