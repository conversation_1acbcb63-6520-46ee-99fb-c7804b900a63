import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { executeReceipt } from './common/executeReceipt'
import { getTime, printTable, showErrorDetails } from './common/tools'
import { Issuer } from '@/types/contracts/Issuer'

wrappedTask('modIssuer', 'Modify issuer information', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'Issuer ID to modify')
  .addParam('issuerName', 'New issuer name')
  .setAction(async (taskArguments, hre) => {
    try {
      const kmsSigner = kmsSignerProvider({ hre })

      const issuerId = convertToHex({ hre, value: taskArguments.issuerId || '' })
      const issuerName = taskArguments.issuerName
      const traceId = convertToHex({ hre, value: 'trace1' })

      console.log(`** addIssuer Parameters **\n`)
      const params = {
        issuerId,
        issuerName,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner<Issuer>({ hre, contractName: 'Issuer' })

      const deadline = await getTime()
      const kmsSig = await kmsSigner.sign(['bytes32', 'string', 'uint256'], [issuerId, issuerName, deadline])

      await executeReceipt(contract.modIssuer(issuerId, issuerName, traceId, deadline, kmsSig))
    } catch (error) {
      showErrorDetails({ error })
    }
  })
