import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'

/**
 * Registers accounts in bulk through validator contract.
 *
 * This task creates sequential accounts with auto-generated IDs and names, implementing
 * rate limiting to prevent network congestion and ensure reliable batch processing.
 * Each account is registered and monitored for successful registration
 *
 * @task registerMassAccounts
 * @description register massive accounts
 *
 * @param {string} numberOfAccounts - Number of accounts to register (default: 1)
 * @param {string} accountId - Base account ID for sequential numbering (default: '')
 * @param {string} accountName - Base account name for sequential naming (default: '')
 * @param {string} validId - Validator ID in string format, will be converted to hex (default: '')
 */

wrappedTask('registerMassAccounts', 'register multiple accounts', {
  filePath: path.basename(__filename),
})
  .addParam('numberOfAccounts', 'number of accounts to register')
  .addParam('accountId', 'base account id')
  .addParam('accountName', 'base account name')
  .addParam('validId', 'validator id')
  .setAction(async (taskArguments, hre) => {
    const numberOfAccounts = Number(taskArguments.numberOfAccounts || '1')
    const accountId = taskArguments.accountId || ''
    const accountName = taskArguments.accountName || ''
    const validatorId = convertToHex({ hre, value: taskArguments.validId || '' })
    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract: validatorContract } = await getContractWithSigner({ hre, contractName: 'Validator' })
    const transactionMap = new Map()
    console.log(`*** Start registering ${numberOfAccounts} accounts, this may take a while...`)
    for (let i = 0; i < numberOfAccounts; i++) {
      // accountId = 300, number = 1000
      // => pad value = 0001, 0002,...
      // => accountId = 300001, 300002,...
      const padValue = i.toString().padStart(numberOfAccounts.toString().length, '0')
      const accountIdNumber = `${accountId}${padValue}`
      const padAccountId = convertToHex({ hre, value: `${accountId}${padValue}` })
      const padAccountName = convertToHex({ hre, value: `${accountName}${padValue}` })
      const tx = await validatorContract.addAccount(
        validatorId,
        padAccountId,
        padAccountName,
        [50000, 50000, 50000, 50000, 50000, [50000, 50000, 50000, 50000, 50000, 50000]],
        traceId,
      )
      transactionMap.set(accountIdNumber, String(tx.hash))
      // NOTE: 高TPSでアカウント登録リクエストの大量を送信すると、時々「Internal Error」というエラーが発生することがある（10回の中で1回発生する）ため、リクエストごとに0.1秒を待たせることでTPS（10）を下げて、エラーが発生する可能性を少なくする。
      await new Promise((resolve) => setTimeout(resolve, 100))
    }

    await new Promise((resolve) => setTimeout(resolve, 5000))

    let registrationSuccessCount = 0
    for (const [accountIdNumber, hash] of transactionMap) {
      const receipt = await hre.ethers.provider.getTransactionReceipt(hash)
      if (receipt && receipt.status === 1) {
        registrationSuccessCount++
      } else {
        console.log(`Failed to register account ${accountIdNumber}`)
      }
    }

    console.log(`Successfully registered ${registrationSuccessCount} accounts`)
  })
