import path from 'path'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { toBytes32 } from '@test/common/utils'
import { executeReceipt } from './common/executeReceipt'
import { getTime } from './common/tools'
import { AccountSyncBridge } from '@/types/contracts/AccountSyncBridge'
import { BalanceSyncBridge } from '@/types/contracts/BalanceSyncBridge'
import { JPYTokenTransferBridge } from '@/types/contracts/JPYTokenTransferBridge'

wrappedTask('setChannel', 'set channel id to bridge contract', {
  filePath: path.basename(__filename),
})
  .addParam('zoneId', 'zone id')
  .addParam('channelId', 'channel id')
  .addParam('bridge', 'bridge contract name')
  .setAction(async (taskArguments, hre) => {
    const { getNamedAccounts, ethers } = hre

    const kmsSigner = kmsSignerProvider({ hre })

    const { zoneId = '', channelId = '', bridge = '' } = taskArguments

    let bridgeContract
    let msgSalt

    switch (bridge) {
      case 'accountSync': {
        const { contract } = await getContractWithSigner<AccountSyncBridge>({ hre, contractName: 'AccountSyncBridge' })
        bridgeContract = contract
        msgSalt = toBytes32('accountSync')
        break
      }
      case 'balanceSync': {
        const { contract } = await getContractWithSigner<BalanceSyncBridge>({ hre, contractName: 'BalanceSyncBridge' })
        bridgeContract = contract
        msgSalt = toBytes32('balanceSync')
        break
      }
      case 'tokenTransfer': {
        const { contract } = await getContractWithSigner<JPYTokenTransferBridge>({
          hre,
          contractName: 'JPYTokenTransferBridge',
        })
        bridgeContract = contract
        msgSalt = toBytes32('tokenTransfer')
        break
      }
      default:
        console.log('bridge contract name is invalid')
        break
    }

    const { signer1 } = await getNamedAccounts()
    const signer1Hardhat = await ethers.getSigner(signer1)

    await bridgeContract.connect(signer1Hardhat)

    console.log(`*** channel id 登録: ${channelId}`)
    const deadline = await getTime()
    const kmsSig = await kmsSigner.sign(['bytes32', 'uint256'], [msgSalt, deadline])
    console.log('zoneId', zoneId)
    console.log('channelId', channelId)
    console.log('bridge', bridge)
    await executeReceipt(bridgeContract.setChannel(zoneId, channelId, deadline, kmsSig))
  })
