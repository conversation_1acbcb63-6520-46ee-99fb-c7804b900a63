import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable } from './common/tools'
import { Validator } from '@/types/contracts/Validator'

wrappedTask('getAccountAll', 'Get all Account information associated with Validator.', {
  filePath: path.basename(__filename),
})
  .addParam('validId', 'validator id')
  .addParam('accountId', 'account id')
  .setAction(async (taskArguments, hre) => {
    const { ethers } = hre

    const validatorId = convertToHex({ hre, value: taskArguments.validId || '' })
    const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })

    const { contract } = await getContractWithSigner<Validator>({ hre, contractName: 'Validator' })

    const receipt = await contract.getAccountAll(validatorId, accountId)

    console.log(`*** Account Information (with Business Zone Information) ***`)

    const accountInfo =
      receipt[0].registeredAt == ''
        ? { accountStatus: 'not exists' }
        : {
            accountId: ethers.toUtf8String(accountId),
            accountName: ethers.toUtf8String(receipt[0].accountName.toString()),
            accountStatus: ethers.toUtf8String(receipt[0].accountStatus.toString()),
            balance: receipt[0].balance,
            appliedAt: receipt[0].appliedAt.toString(),
            registeredAt: receipt[0].registeredAt.toString(),
            terminatingAt: receipt[0].terminatingAt.toString(),
            terminatedAt: receipt[0].terminatedAt.toString(),
            mintLimit: receipt[0].mintLimit.toString(),
            burnLimit: receipt[0].burnLimit.toString(),
            chargeLimit: receipt[0].chargeLimit.toString(),
            dischargeLimit: receipt[0].dischargeLimit.toString(),
            transferLimit: receipt[0].transferLimit.toString(),
            cumulativeLimit: receipt[0].cumulativeLimit.toString(),
            cumulativeAmount: receipt[0].cumulativeAmount.toString(),
            cumulativeDate: receipt[0].cumulativeDate.toString(),
            cumulativeMintLimit: receipt[0].cumulativeTransactionLimits.cumulativeMintLimit,
            cumulativeMintAmount: receipt[0].cumulativeTransactionLimits.cumulativeMintAmount.toString(),
            cumulativeBurnLimit: receipt[0].cumulativeTransactionLimits.cumulativeBurnLimit.toString(),
            cumulativeBurnAmount: receipt[0].cumulativeTransactionLimits.cumulativeBurnAmount.toString(),
            cumulativeChargeLimit: receipt[0].cumulativeTransactionLimits.cumulativeChargeLimit.toString(),
            cumulativeChargeAmount: receipt[0].cumulativeTransactionLimits.cumulativeChargeAmount.toString(),
            cumulativeDischargeLimit: receipt[0].cumulativeTransactionLimits.cumulativeDischargeLimit.toString(),
            cumulativeDischargeAmount: receipt[0].cumulativeTransactionLimits.cumulativeDischargeAmount.toString(),
            cumulativeTransferLimit: receipt[0].cumulativeTransactionLimits.cumulativeTransferLimit.toString(),
            cumulativeTransferAmount: receipt[0].cumulativeTransactionLimits.cumulativeTransferAmount.toString(),
          }

    printTable({ data: accountInfo })

    if (receipt[0].businessZoneAccounts.length > 0) {
      console.log('Business Zone Account Linkage Status: Linked')
      for (const businessZoneAccount of receipt[0].businessZoneAccounts) {
        const businessZoneInfo = {
          accountName: ethers.toUtf8String(businessZoneAccount.accountName.toString()),
          zoneId: businessZoneAccount.zoneId.toString(),
          zoneName: businessZoneAccount.zoneName.toString(),
          balance: businessZoneAccount.balance,
          accountStatus: ethers.toUtf8String(businessZoneAccount.accountStatus.toString()),
          appliedAt: businessZoneAccount.appliedAt.toString(),
          registeredAt: businessZoneAccount.registeredAt.toString(),
          terminatingAt: businessZoneAccount.terminatingAt.toString(),
          terminatedAt: businessZoneAccount.terminatedAt.toString(),
        }

        console.log('--- Linked Business Zone Account ---')
        printTable({ data: businessZoneInfo })
      }
    } else if (receipt[0].zoneId.toString() === '3000') {
      // Only Log if call from Finance Zone
      console.log('BusinessZoneAccounts: Not linked from Business Zone.')
    }
  })
