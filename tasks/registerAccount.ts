import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { executeReceipt } from './common/executeReceipt'
import { getTime, showEthersRes } from './common/tools'
import * as PrivateKey from '@/privateKey'

const MAX_LIMITS_VALUES = {
  mint: ************,
  burn: ************,
  charge: ************,
  discharge: ************,
  transfer: ************,
  cumulative: {
    total: ***************,
    mint: ***************,
    burn: ***************,
    charge: ***************,
    discharge: ***************,
    transfer: ***************,
  },
}
wrappedTask('registerAcc', 'register account', {
  filePath: path.basename(__filename),
})
  .addParam('accountId', 'id')
  .addParam('accountName', 'name')
  .addParam('accountKey', 'key')
  .addParam('issuerKey', 'issuer key')
  .addParam('issuerId', 'issuer id')
  .addParam('validId', 'validator id')
  .addOptionalParam('limitValues', 'limit values')
  .addParam('flag', 'flag')
  .setAction(async (taskArguments, hre) => {
    const { accountKey = '', issuerKey = '', flag = '' } = taskArguments

    const issuerId = convertToHex({ hre, value: taskArguments.issuerId || '' })
    const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })
    const accountName = convertToHex({ hre, value: taskArguments.accountName || '' })
    const validatorId = convertToHex({ hre, value: taskArguments.validId || '' })
    const limitValues = taskArguments.limitValues ? JSON.parse(taskArguments.limitValues) : MAX_LIMITS_VALUES

    const traceId = convertToHex({ hre, value: 'trace1' })

    const addrAccount = new hre.ethers.Wallet(accountKey).address

    const { contract: issuerContract } = await getContractWithSigner({ hre, contractName: 'Issuer' })
    const { contract: validatorContract } = await getContractWithSigner({
      hre,
      contractName: 'Validator',
    })

    if (flag[0] === '1') {
      console.log(`*** accountID登録: ${accountId}`)
      const receipt = await validatorContract.addAccount(validatorId, accountId, accountName, limitValues, traceId)
      await receipt
        .wait()
        .then((res) => {
          showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    }

    if (flag[1] === '1') {
      console.log(`*** accountID権限登録: ${accountId}=${addrAccount}`)

      const deadline = await getTime()
      const sig = await PrivateKey.sig(
        issuerKey,
        ['bytes32', 'bytes32', 'address', 'uint256'],
        [issuerId, accountId, addrAccount, deadline],
      )

      await executeReceipt(issuerContract.addAccountRole(issuerId, accountId, addrAccount, traceId, deadline, sig[0]))
    }
  })
