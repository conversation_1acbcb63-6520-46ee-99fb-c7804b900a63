import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable } from './common/tools'
import { FinancialCheck } from '@/types/contracts/FinancialCheck'

wrappedTask('getFinancialZoneAccount', 'get all financial zone account data', {
  filePath: path.basename(__filename),
})
  .addParam('accountId', 'account id')
  .setAction(async (taskArguments, hre) => {
    const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })

    const { contract } = await getContractWithSigner<FinancialCheck>({ hre, contractName: 'FinancialCheck' })
    const {
      accountLimitData: {
        mintLimit,
        burnLimit,
        transferLimit,
        chargeLimit,
        dischargeLimit,
        cumulativeLimit,
        cumulativeAmount,
        cumulativeDate,
        cumulativeTransactionLimits,
      },
    } = await contract.getAccountLimit(accountId)

    const accountInfo = {
      accountId,
      mintLimit,
      burnLimit,
      transferLimit,
      chargeLimit,
      dischargeLimit,
      cumulativeLimit,
      cumulativeAmount,
      cumulativeDate,
      cumulativeMintLimit: cumulativeTransactionLimits.cumulativeMintLimit,
      cumulativeMintAmount: cumulativeTransactionLimits.cumulativeMintAmount,
      cumulativeBurnLimit: cumulativeTransactionLimits.cumulativeBurnLimit,
      cumulativeBurnAmount: cumulativeTransactionLimits.cumulativeBurnAmount,
      cumulativeChargeLimit: cumulativeTransactionLimits.cumulativeChargeLimit,
      cumulativeChargeAmount: cumulativeTransactionLimits.cumulativeChargeAmount,
      cumulativeDischargeLimit: cumulativeTransactionLimits.cumulativeDischargeLimit,
      cumulativeDischargeAmount: cumulativeTransactionLimits.cumulativeDischargeAmount,
      cumulativeTransferLimit: cumulativeTransactionLimits.cumulativeTransferLimit,
      cumulativeTransferAmount: cumulativeTransactionLimits.cumulativeTransferAmount,
    }

    console.log(`--- Financial Zone Account---`)
    printTable({ data: accountInfo })
  })
