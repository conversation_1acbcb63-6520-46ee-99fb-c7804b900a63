import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'
import { Issuer } from '@/types/contracts/Issuer'

wrappedTask('hasIssuer', 'Check if issuer exists', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'issuer id')
  .setAction(async (taskArguments, hre) => {
    const issuerId = convertToHex({ hre, value: taskArguments.issuerId || '' })

    console.log(`** hasIssuer Parameters **\n`)
    const params = {
      issuerId,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner<Issuer>({ hre, contractName: 'Issuer' })

      const { success, err } = await contract.hasIssuer(issuerId)

      const formattedReceipt = {
        result: success ? 'ok' : 'failed',
        reason: err,
      }

      console.log(`** hasIssuer Receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
