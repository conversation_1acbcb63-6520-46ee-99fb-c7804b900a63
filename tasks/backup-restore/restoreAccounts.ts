import fs from 'fs'
import path from 'path'
import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { md5Valid } from '@tasks/common/md5Utils'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { showEthersRes } from '@tasks/common/tools'
import { Account } from '@/types/contracts/Account'
import { RemigrationRestore } from '@/types/contracts/remigration/RemigrationRestore'

wrappedTask('restoreAccounts', 'restore all account data', { filePath: path.basename(__filename) }).setAction(
  async (_, hre) => {
    let accounts: Array<any> = []

    const data = fs.readFileSync(`./scripts/backup-restore/backupfiles/${hre.network.name}/accounts.json`, 'utf8')
    const isValid = md5Valid({ obj: data, item: 'accounts', network: hre.network.name })

    if (!isValid) {
      throw new Error('The data is invalid backup data.')
    }

    accounts = JSON.parse(data)

    const { contract: remigrationContract } = await getContractWithSigner<RemigrationRestore>({
      hre,
      contractName: 'RemigrationRestore',
    })
    await getContractWithSigner<Account>({
      hre,
      contractName: 'Account',
    })

    console.log(`*** restore accounts data...`)
    const sigPrams = await getBackupSignature({ hre, salt: 'setAccountsAll' })
    const limit = 1000

    while (accounts.length > limit) {
      const receipt = await remigrationContract.restoreAccounts(
        accounts.slice(0, limit),
        sigPrams.deadline,
        sigPrams.sig,
      )
      await receipt
        .wait()
        .then((res) => {
          showEthersRes({ res })
        })
        .catch((error) => console.log(error))
      accounts = accounts.slice(limit)
    }

    if (accounts.length > 0) {
      const receipt = await remigrationContract.restoreAccounts(accounts, sigPrams.deadline, sigPrams.sig)
      await receipt
        .wait()
        .then((res) => {
          showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    }
  },
)
