import fs from 'fs'
import path from 'path'
import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { md5Valid } from '@tasks/common/md5Utils'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { showEthersRes } from '@tasks/common/tools'
import { RemigrationRestore } from '@/types/contracts/remigration/RemigrationRestore'

wrappedTask('restoreProviders', 'restore all provider data', { filePath: path.basename(__filename) }).setAction(
  async (_, hre) => {
    let provider: Array<any> = []

    const data = fs.readFileSync(`./scripts/backup-restore/backupfiles/${hre.network.name}/provider.json`, 'utf8')
    const isValid = md5Valid({ obj: data, item: 'provider', network: hre.network.name })

    if (!isValid) {
      throw new Error('The data is invalid backup data.')
    }

    provider = JSON.parse(data)

    const { contract } = await getContractWithSigner<RemigrationRestore>({
      hre,
      contractName: 'RemigrationRestore',
    })

    console.log(`*** restore provider data...`)

    const setProviderSig = await getBackupSignature({ hre, salt: 'setProviderAll' })

    const receipt = await contract.restoreProviders(provider, setProviderSig.deadline, setProviderSig.sig)
    await receipt
      .wait()
      .then((res) => {
        showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  },
)
