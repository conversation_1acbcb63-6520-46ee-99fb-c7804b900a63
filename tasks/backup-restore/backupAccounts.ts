import path from 'path'
import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { saveBackupToJson } from '@tasks/common/saveBackupToJson'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { Account } from '@/types/contracts/Account'
import { RemigrationBackup } from '@/types/contracts/remigration/RemigrationBackup'

wrappedTask('backupAccounts', 'backup all account data', { filePath: path.basename(__filename) }).setAction(
  async (_, hre) => {
    const { contract: remigrationContract } = await getContractWithSigner<RemigrationBackup>({
      hre,
      contractName: 'RemigrationBackup',
    })
    const { contract: accountContract } = await getContractWithSigner<Account>({ hre, contractName: 'Account' })

    console.log(`*** backup accounts data...`)
    const sigPrams = await getBackupSignature({ hre, salt: 'getAccountsAll' })

    const accounts: Array<any> = []
    let offset = 0
    const limit = 1000

    const totalCount = await accountContract.getAccountCount()
    console.log(`Total item: ${totalCount.toString()}`)

    while (accounts.length != Number(totalCount)) {
      if (accounts.length > Number(totalCount)) {
        console.error(`Error: Accounts count ${accounts.length} is greater than total count`)
        break
      }

      // [result, count, error]
      const [result, , err] = await remigrationContract.backupAccounts(offset, limit, sigPrams.deadline, sigPrams.sig)
      if (err != '') {
        console.log(`backup ${accounts.length + 1} ~ ${accounts.length + result.length} failed`)
        console.log('Error:', err)
        break
      } else {
        console.log(`backup ${accounts.length + 1} ~ ${accounts.length + result.length} items to local`)
        for (const key of Object.keys(result)) {
          const account = []
          for (const itemKey of Object.keys(result[key])) {
            const accountItem = result[key][itemKey]
            if (typeof accountItem === 'bigint') {
              account[itemKey] = accountItem.toString()
            } else {
              account[itemKey] = accountItem
            }
          }
          accounts.push(account)
        }
      }
      offset += limit
    }

    console.log(`All ${totalCount} (equle to the total count) items have been successfully backed up.`)

    await saveBackupToJson({ data: accounts, fileName: 'accounts', networkName: hre.network.name })
  },
)
