import fs from 'fs'
import path from 'path'
import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { md5Valid } from '@tasks/common/md5Utils'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { showEthersRes } from '@tasks/common/tools'
import { RenewableEnergyToken } from '@/types/contracts/renewableEnergyToken/RenewableEnergyToken'

wrappedTask('restoreRETokens', 'restore all renewable energy token data', {
  filePath: path.basename(__filename),
}).setAction(async (_, hre) => {
  let retokens: Array<any> = []

  const data = fs.readFileSync(`./scripts/backup-restore/backupfiles/${hre.network.name}/retokens.json`, 'utf8')
  const isValid = md5Valid({ obj: data, item: 'retokens', network: hre.network.name })
  retokens = JSON.parse(data)

  if (!isValid) {
    throw new Error('The data is invalid backup data.')
  }

  const { contract } = await getContractWithSigner<RenewableEnergyToken>({
    hre,
    contractName: 'RenewableEnergyToken',
  })

  console.log(`*** restore renewable energy token data...`)
  const sigPrams = await getBackupSignature({ hre, salt: 'setRETokensAll' })
  const limit = 1000

  while (retokens.length > limit) {
    const receipt = await contract.restoreRenewableEnergyTokens(
      retokens.slice(0, limit),
      sigPrams.deadline,
      sigPrams.sig,
    )
    await receipt
      .wait()
      .then((res) => {
        showEthersRes({ res })
      })
      .catch((error) => console.log(error))
    retokens = retokens.slice(limit)
  }

  if (retokens.length > 0) {
    const receipt = await contract.restoreRenewableEnergyTokens(retokens, sigPrams.deadline, sigPrams.sig)
    await receipt
      .wait()
      .then((res) => {
        showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  }
})
