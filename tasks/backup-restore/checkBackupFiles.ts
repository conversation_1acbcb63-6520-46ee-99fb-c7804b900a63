import fs from 'fs'
import path from 'path'
import {
  assertEqualsAccountBalance,
  assertEqualsAccountsBizAccounts,
  assertEqualsAccountsFinAccounts,
  assertEqualsValidatorIdIssuerIdAccId,
} from '@tasks/common/checkBackupFileUtils'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { getItemArray } from '@tasks/common/utils'

wrappedTask('checkBackupFiles', 'backup all account data', { filePath: path.basename(__filename) })
  .addParam('path', 'backup files path')
  .setAction(async (taskArguments, _) => {
    console.log()
    console.log(`******** start check backup files ********`)

    const { path = '' } = taskArguments

    const issuers = JSON.parse(fs.readFileSync(`${path}/issuers.json`, 'utf8'))
    const validators = JSON.parse(fs.readFileSync(`${path}/validators.json`, 'utf8'))
    const accounts = JSON.parse(fs.readFileSync(`${path}/accounts.json`, 'utf8'))
    const finaccounts = JSON.parse(fs.readFileSync(`${path}/finaccounts.json`, 'utf8'))
    const bizaccounts = JSON.parse(fs.readFileSync(`${path}/bizaccounts.json`, 'utf8'))
    const token = JSON.parse(fs.readFileSync(`${path}/token.json`, 'utf8'))

    const accountIds = getItemArray(accounts, 0)

    const checkResult_1 = assertEqualsValidatorIdIssuerIdAccId({ validators, issuers, accountIds })
    console.log('Check1 Result: ', checkResult_1)

    const acocuntBalances = getItemArray(accounts, 4)

    const checkResult_2 = assertEqualsAccountBalance({ balances: acocuntBalances, token })
    console.log('Check2 Result: ', checkResult_2)

    const finAccountIds = getItemArray(finaccounts, 0)
    const checkResult_3 = assertEqualsAccountsFinAccounts({ finAccountIds, accountIds })
    console.log('Check3 Result: ', checkResult_3)

    const checkResult_4 = assertEqualsAccountsBizAccounts({ bizaccounts, accounts })
    console.log('Check4 Result: ', checkResult_4)
  })
