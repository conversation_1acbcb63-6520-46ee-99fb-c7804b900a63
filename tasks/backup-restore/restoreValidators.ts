import fs from 'fs'
import path from 'path'
import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { md5Valid } from '@tasks/common/md5Utils'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { showEthersRes } from '@tasks/common/tools'
import { RemigrationRestore } from '@/types/contracts/remigration/RemigrationRestore'

wrappedTask('restoreValidators', 'restore all validator data', { filePath: path.basename(__filename) }).setAction(
  async (_, hre) => {
    const { network } = hre

    let validators: Array<any> = []

    const data = fs.readFileSync(`./scripts/backup-restore/backupfiles/${network.name}/validators.json`, 'utf8')
    const isValid = md5Valid({ obj: data, item: 'validators', network: network.name })
    validators = JSON.parse(data)

    if (!isValid) {
      throw new Error('The data is invalid or empty data.')
    }

    const { contract } = await getContractWithSigner<RemigrationRestore>({
      hre,
      contractName: 'RemigrationRestore',
    })

    console.log(`*** restore validators data...`)
    const sigPrams = await getBackupSignature({ hre, salt: 'setValidatorsAll' })
    const limit = 1000

    while (validators.length > limit) {
      const receipt = await contract.restoreValidators(validators.slice(0, limit), sigPrams.deadline, sigPrams.sig)
      await receipt
        .wait()
        .then((res) => {
          showEthersRes({ res })
        })
        .catch((error) => console.log(error))
      validators = validators.slice(limit)
    }

    if (validators.length > 0) {
      const receipt = await contract.restoreValidators(validators, sigPrams.deadline, sigPrams.sig)
      await receipt
        .wait()
        .then((res) => {
          showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    }
  },
)
