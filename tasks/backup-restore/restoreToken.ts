import fs from 'fs'
import path from 'path'
import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { md5Valid } from '@tasks/common/md5Utils'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { showEthersRes } from '@tasks/common/tools'
import { RemigrationRestore } from '@/types/contracts/remigration/RemigrationRestore'

wrappedTask('restoreToken', 'restore all token data', { filePath: path.basename(__filename) }).setAction(
  async (_, hre) => {
    const data = fs.readFileSync(`./scripts/backup-restore/backupfiles/${hre.network.name}/token.json`, 'utf8')
    const isValid = md5Valid({ obj: data, item: 'token', network: hre.network.name })
    const token = JSON.parse(data)

    if (!isValid) {
      throw new Error('The data is invalid or empty data.')
    }

    const { contract } = await getContractWithSigner<RemigrationRestore>({
      hre,
      contractName: 'RemigrationRestore',
    })

    console.log(`*** restore token data...`)

    const sigPrams = await getBackupSignature({ hre, salt: 'setTokenAll' })
    const receipt = await contract.restoreToken(token, sigPrams.deadline, sigPrams.sig)
    await receipt
      .wait()
      .then((res) => {
        showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  },
)
