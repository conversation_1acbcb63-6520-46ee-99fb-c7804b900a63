import path from 'path'
import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { saveBackupToJson } from '@tasks/common/saveBackupToJson'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { Provider } from '@/types/contracts/Provider'
import { RemigrationBackup } from '@/types/contracts/remigration/RemigrationBackup'

wrappedTask('backupProviders', 'backup all provider data', { filePath: path.basename(__filename) }).setAction(
  async (_, hre) => {
    const { contract: providerContract } = await getContractWithSigner<Provider>({
      hre,
      contractName: 'Provider',
    })
    const { contract } = await getContractWithSigner<RemigrationBackup>({
      hre,
      contractName: 'RemigrationBackup',
    })

    console.log(`*** backup provider data...`)
    const sigPrams = await getBackupSignature({ hre, salt: 'getProviderAll' })

    const totalCount = await providerContract.getProviderCount()
    console.log(`Total item: ${totalCount.toString()}`)

    const provider = await contract.backupProviders(sigPrams.deadline, sigPrams.sig)
    if (provider.err != '') {
      console.log(provider.err)
    } else {
      console.log(`All ${totalCount} (equle to the total count) items have been successfully backed up.`)

      await saveBackupToJson({ data: provider.providers, fileName: 'provider', networkName: hre.network.name })
    }
  },
)
