import path from 'path'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { Contract } from 'ethers'
import { AccountSyncBridge } from '@/types/contracts/AccountSyncBridge'
import { BalanceSyncBridge } from '@/types/contracts/BalanceSyncBridge'
import { JPYTokenTransferBridge } from '@/types/contracts/JPYTokenTransferBridge'

type ContractType = {
  Error: Contract
  OwnableIBCHandler: Contract
  AccountSyncBridge: AccountSyncBridge
  BalanceSyncBridge: BalanceSyncBridge
  JPYTokenTransferBridge: JPYTokenTransferBridge
}

const TARGET: (keyof ContractType)[] = [
  'Error',
  'OwnableIBCHandler',
  'AccountSyncBridge',
  'BalanceSyncBridge',
  'JPYTokenTransferBridge',
] as const

wrappedTask('deployConfirmation_ibc', 'Prints contracts setted to ContractManager', {
  filePath: path.basename(__filename),
}).setAction(async (_, hre) => {
  const { network } = hre

  console.log(`*** Deployed Contract 情報 on ${network.name} network ***`)
  console.log('Name\t/\tAddress\t/\tVersion')
  for (let lp = 0; lp < TARGET.length; lp++) {
    const { deployed, contract } = await getContractWithSigner<ContractType[keyof ContractType]>({
      hre,
      contractName: TARGET[lp],
    })

    // Error、OwnableIBCHandler はversion()が無いため、アドレスのみ表示
    if (TARGET[lp] == 'Error' || TARGET[lp] == 'OwnableIBCHandler') {
      console.log('%s\t%s', TARGET[lp], deployed.address)
      continue
    }
    const version = await contract.version()
    console.log('%s\t %s\t %s', TARGET[lp], deployed.address, version)
  }
})
