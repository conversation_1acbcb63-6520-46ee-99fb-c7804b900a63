import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'
import { FinancialCheck } from '@/types/contracts/FinancialCheck'

wrappedTask('getBizZoneAccountStatus', 'Get Biz Zone Account.', {
  filePath: path.basename(__filename),
})
  .addParam('accountId', 'account id')
  .addParam('zoneId', 'zone id')
  .setAction(async (taskArguments, hre) => {
    const { ethers } = hre

    const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })
    const zoneId = Number(taskArguments.zoneId || '')

    console.log(`** getBizZoneAccountStatus Parameters **\n`)
    const params = {
      accountId,
      zoneId,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner<FinancialCheck>({ hre, contractName: 'FinancialCheck' })

      const receipt = await contract.getBizZoneAccountStatus(accountId, zoneId)

      printTable({ data: { accountStatus: ethers.toUtf8String(receipt.accountStatus.toString()) } })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
