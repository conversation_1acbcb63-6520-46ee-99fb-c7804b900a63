import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'
import { Issuer } from '@/types/contracts/Issuer'

wrappedTask('isFrozen', 'Check if account is frozen', { filePath: path.basename(__filename) })
  .addParam('accountId', 'account id')
  .setAction(async (taskArguments, hre) => {
    try {
      const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })

      console.log(`** isFrozen Parameters **\n`)
      const params = {
        accountId,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner<Issuer>({ hre, contractName: 'Issuer' })

      const { frozen, err } = await contract.isFrozen(accountId)

      const formattedReceipt = {
        result: frozen ? 'frozen' : 'not frozen',
        reason: err,
      }

      console.log(`** isFrozen Receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
