import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import * as utils from '@test/common/utils'
import { getTime, printTable, showErrorDetails } from './common/tools'
import PrivateKey from '@/privateKey'
import { FinancialCheck } from '@/types/contracts/FinancialCheck'

wrappedTask('checkTransaction', 'Check if an account can transfer.', { filePath: path.basename(__filename) })
  .addParam('validId', 'validator id')
  .addParam('zoneId', 'zone id')
  .addParam('sendAccountId', 'send account id')
  .addParam('fromAccountId', 'from account id')
  .addParam('fromAccountIssuerId', 'from account issuer id')
  .addParam('toAccountId', 'to account id')
  .addParam('amount', 'amount')
  .setAction(async (taskArguments, hre) => {
    const sendAccountId = convertToHex({ hre, value: taskArguments.sendAccountId || '' })
    const fromAccountId = convertToHex({ hre, value: taskArguments.fromAccountId || '' })
    const fromAccountIssuerId = convertToHex({ hre, value: taskArguments.fromAccountIssuerId || '' })
    const toAccountId = convertToHex({ hre, value: taskArguments.toAccountId || '' })
    const zoneId = Number(taskArguments.zoneId || '')
    const validId = convertToHex({ hre, value: taskArguments.validId || '' })
    const amount = Number(taskArguments.amount || '')

    const miscValue1 = convertToHex({ hre, value: '' })
    const miscValue2 = ''

    const orderN = '0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141'
    const accountPrivateKey = '0x47e179ec197488593b187f80a00eb0da91f1b9d0b13f8733639f19c30a34926a'
    const issuerPrivateKey = '0x7c852118294e51e653712a81e05800f419141751be58f605c371e15141b007a6'
    const commitPrivateKey = '0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80'
    const deadline = await getTime()

    const sigInfo = await utils.siginfoGenerator(
      commitPrivateKey,
      accountPrivateKey,
      orderN,
      issuerPrivateKey,
      deadline,
    )

    const msgSalt = utils.toBytes32('transfer')
    const accSigInfo = PrivateKey.sig(
      sigInfo.signer,
      ['bytes32', 'bytes32', 'bytes32', 'uint256', 'bytes32'],
      [sendAccountId, fromAccountId, toAccountId, amount, msgSalt],
    )

    console.log(`** checkTransaction Parameters **\n`)
    const params = {
      validId,
      zoneId,
      sendAccountId,
      fromAccountId,
      toAccountId,
      fromAccountIssuerId,
      amount,
      miscValue1,
      miscValue2,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner<FinancialCheck>({ hre, contractName: 'FinancialCheck' })

      const { success, err } = await contract.checkTransaction(
        zoneId,
        sendAccountId,
        fromAccountId,
        toAccountId,
        fromAccountIssuerId,
        amount,
        miscValue1,
        miscValue2,
        accSigInfo[0],
        sigInfo.info,
      )

      const formattedReceipt = {
        result: success ? 'ok' : 'failed',
        reason: err,
      }
      console.log(`** checkTransaction receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
