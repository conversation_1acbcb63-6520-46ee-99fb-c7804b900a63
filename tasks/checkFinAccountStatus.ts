import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'
import { FinancialCheck } from '@/types/contracts/FinancialCheck'

wrappedTask('checkFinAccountStatus', 'Check fin account status.', { filePath: path.basename(__filename) })
  .addParam('accountId', 'account id')
  .setAction(async (taskArguments, hre) => {
    const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })

    console.log(`** checkFinAccountStatus Parameters **\n`)
    const params = {
      accountId,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner<FinancialCheck>({ hre, contractName: 'FinancialCheck' })

      const { accountStatus, err } = await contract.checkFinAccountStatus(accountId)

      const formattedReceipt = {
        accountStatus: hre.ethers.toUtf8String(accountStatus.toString()),
        reason: err,
      }

      console.log(`** checkFinAccountStatus receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
