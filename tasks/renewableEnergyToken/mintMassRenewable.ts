import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { RenewableEnergyToken } from '@/types/contracts/renewableEnergyToken/RenewableEnergyToken'

wrappedTask('mintMassRenewable', 'mint multiple renewable', {
  filePath: path.basename(__filename),
})
  .addParam('numberOfMintRenewable', 'number of mint')
  .addParam('tokenId', 'token id')
  .addParam('metadataId', 'metadata id')
  .addParam('metadataHash', 'metadata hash')
  .addParam('mintAccountId', 'mintAccountId')
  .addParam('ownerAccountId', 'ownerAccountId')
  .setAction(async (taskArguments, hre) => {
    const numberOfMint = Number(taskArguments.numberOfMintRenewable || '1')

    const tokenId = taskArguments.tokenId || ''
    const metadataId = taskArguments.metadataId || ''
    const metadataHash = taskArguments.metadataHash || ''
    const mintAccountId = convertToHex({ hre, value: taskArguments.mintAccountId })
    const ownerAccountId = convertToHex({ hre, value: taskArguments.ownerAccountId })

    const isLocked = false
    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner<RenewableEnergyToken>({
      hre,
      contractName: 'RenewableEnergyToken',
    })

    const transactionMap = new Map()
    console.log(`*** Start registering ${numberOfMint} mint, this may take a while...`)
    for (let i = 0; i < numberOfMint; i++) {
      // tokenId = 300, number = 1000
      // => pad value = 0001, 0002,...
      // => tokenId = 300001, 300002,...
      const padValue = i.toString().padStart(numberOfMint.toString().length, '0')
      const mintNumber = `${tokenId}${padValue}`
      const padTokenId = convertToHex({ hre, value: `${tokenId}${padValue}` })
      const padMetadataId = convertToHex({ hre, value: `${metadataId}${padValue}` })
      const padMetadataHash = convertToHex({ hre, value: `${metadataHash}${padValue}` })

      try {
        const tx = await contract.mint(
          padTokenId,
          padMetadataId,
          padMetadataHash,
          mintAccountId,
          ownerAccountId,
          isLocked,
          traceId,
        )
        transactionMap.set(mintNumber, tx.hash)
      } catch (e: any) {
        console.error(`Revert at callStatic for mint ${mintNumber}`)
        console.error(`Reason: ${e?.error?.message || e?.message || JSON.stringify(e)}`)
        continue
      }

      await new Promise((resolve) => setTimeout(resolve, 100))
    }

    await new Promise((resolve) => setTimeout(resolve, 1000))

    let registrationSuccessCount = 0
    for (const [mintIdNumber, hash] of transactionMap) {
      const receipt = await hre.ethers.provider.getTransactionReceipt(hash)
      if (receipt && receipt.status === 1) {
        console.log(`Success to mint ${mintIdNumber}`)
        registrationSuccessCount++
      } else {
        console.log(`Failed to mint ${mintIdNumber}`)
      }
    }

    console.log(`Successfully registered ${registrationSuccessCount} mint`)
  })
