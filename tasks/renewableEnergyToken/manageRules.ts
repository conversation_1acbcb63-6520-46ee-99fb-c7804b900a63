import path from 'path'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { showEthersRes } from '@tasks/common/tools'

wrappedTask('manageRule', 'manage rule', { filePath: path.basename(__filename) })
  .addParam('manageRule', 'manage rule')
  .setAction(async (taskArguments, hre) => {
    const { manageRule = '' } = taskArguments

    const { contract: transferProxyContract } = await getContractWithSigner({
      hre,
      contractName: 'TransferProxy',
    })
    const { contract: renewableEnergyTokenContract } = await getContractWithSigner({
      hre,
      contractName: 'RenewableEnergyToken',
    })

    console.log(`MANAGE_RULE= ${manageRule}`)
    console.log(`TRANSFER_PROXY_ADDRESS= ${transferProxyContract.address}`)
    console.log(`CUSTOM_CONTRACT_ADDRESS= ${renewableEnergyTokenContract.address}`)
    let receipt
    switch (manageRule) {
      case 'add':
        console.log(`*** TransferProxyにカスタムコントラクトを追加する`)
        receipt = await transferProxyContract.addRule(renewableEnergyTokenContract.address, 0)
        await receipt
          .wait()
          .then((res) => {
            showEthersRes({ res })
          })
          .catch((error) => console.log(error))
        break
      case 'check': {
        console.log(`*** TransferProxyにカスタムコントラクトがデプロイされているかを確認する`)
        const isRegistered = await transferProxyContract.isRegistered(renewableEnergyTokenContract.address)
        console.log(`result: ${isRegistered}`)
        break
      }
      case 'delete':
        console.log(`*** TransferProxyからカスタムコントラクトを削除する`)
        receipt = await transferProxyContract.deleteRule(renewableEnergyTokenContract.address)
        await receipt
          .wait()
          .then((res) => {
            showEthersRes({ res })
          })
          .catch((error) => console.log(error))
        break
      case 'findAll': {
        console.log(`*** TransferProxyに登録されているカスタムコントラクトを全て取得する`)
        const findAll = await transferProxyContract.findAll()
        console.log(`result: ${findAll}`)
        break
      }
      case 'clear':
        console.log(`*** TransferProxyからカスタムコントラクトを全て削除する`)
        receipt = await transferProxyContract.clearRule()
        await receipt
          .wait()
          .then((res) => {
            showEthersRes({ res })
          })
          .catch((error) => console.log(error))
        break
    }
  })
