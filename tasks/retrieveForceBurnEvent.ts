import path from 'path'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printSection, printTable } from '@tasks/common/tools'

wrappedTask('retrieveForceBurnEvent', 'check if ForceBurnRequested event is emitted', {
  filePath: path.basename(__filename),
}).setAction(async (_, hre) => {
  const { network, getNamedAccounts, ethers } = hre

  printSection({ title: `Retrieve ForceBurnRequestedEvent on ${network.name} network: ${path.basename(__filename)}` })

  const accountContractDeployed = await hre.deployments.get('Account')
  const accountContract = await ethers.getContractAt('Account', accountContractDeployed.address)
  const { signer1 } = await getNamedAccounts()
  const signer1Hardhat = await ethers.getSigner(signer1)
  accountContract.connect(signer1Hardhat)

  const forceBurnEvent = await accountContract.queryFilter(accountContract.filters.ForceBurn())
  console.log(`** ForceBurn Event Information **\n`)

  if (forceBurnEvent.length > 0) {
    const formattedArgs = forceBurnEvent.map((log) => ({
      validatorId: log.args[0],
      accountId: log.args[1],
      traceId: log.args[2],
      burnedAmount: log.args[3],
      burnedBalance: log.args[4],
      forceDischarge: log.args[5],
    }))

    formattedArgs.forEach((args) => printTable({ data: args }))
  } else {
    console.log('No ForceBurn events emitted.')
  }
})
