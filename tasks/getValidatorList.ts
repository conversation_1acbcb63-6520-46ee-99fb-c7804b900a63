import path from 'path'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable } from './common/tools'
import { Validator } from '@/types/contracts/Validator'

wrappedTask('getValidatorList', 'Gets the Information for account ID.', {
  filePath: path.basename(__filename),
}).setAction(async (_, hre) => {
  const offset = 0
  const limit = 100

  const { contract } = await getContractWithSigner<Validator>({ hre, contractName: 'Validator' })

  const receipt = await contract.getValidatorList(limit, offset)
  console.log('Validator List:')
  receipt[0].forEach(({ validatorId, name, issuerId }, index) => {
    const validatorInfo = {
      validatorId,
      name,
      issuerId,
    }

    console.log(`--- Validator ${index + 1} ---`)
    printTable({ data: validatorInfo })
  })

  console.log(`Total Count: ${receipt.totalCount}`)
})
