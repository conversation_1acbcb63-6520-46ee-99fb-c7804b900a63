import path from 'path'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { getTime } from '@tasks/common/tools'
import { toBytes32 } from '@test/common/utils'
import { executeReceipt } from './common/executeReceipt'
import { AccountSyncBridge } from '@/types/contracts/AccountSyncBridge'
import { BalanceSyncBridge } from '@/types/contracts/BalanceSyncBridge'
import { JPYTokenTransferBridge } from '@/types/contracts/JPYTokenTransferBridge'

wrappedTask('setAddress', 'set main contract address for bridge contract', {
  filePath: path.basename(__filename),
})
  .addParam('validatorAddress', 'validator address')
  .addParam('ibcTokenAddress', 'token service address')
  .addParam('accountAddress', 'account address')
  .addParam('accessCtrlAddress', 'access ctrl address')
  .addParam('businessZoneAccountAddress', 'business zone account address')
  .addParam('providerAddress', 'provider address')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })

    const {
      validatorAddress = '',
      ibcTokenAddress = '',
      accountAddress = '',
      accessCtrlAddress = '',
      businessZoneAccountAddress = '',
      providerAddress = '',
    } = taskArguments

    console.log(`VALIDATOR_ADDRESS= ${validatorAddress}`)
    console.log(`IBC_TOKEN_ADDRESS= ${ibcTokenAddress}`)
    console.log(`ACCOUNT_ADDRESS= ${accountAddress}`)
    console.log(`ACCESS_CTRL_ADDRESS= ${accessCtrlAddress}`)
    console.log(`BUSINESS_ZONE_ACCOUNT_ADDRESS= ${businessZoneAccountAddress}`)
    console.log(`PROVIDER_ADDRESS= ${providerAddress}`)

    const { contract: accountSyncBridge } = await getContractWithSigner<AccountSyncBridge>({
      hre,
      contractName: 'AccountSyncBridge',
    })
    const { contract: tokenTransferBridge } = await getContractWithSigner<JPYTokenTransferBridge>({
      hre,
      contractName: 'JPYTokenTransferBridge',
    })
    const { contract: balanceSyncBridge } = await getContractWithSigner<BalanceSyncBridge>({
      hre,
      contractName: 'BalanceSyncBridge',
    })

    tokenTransferBridge.connect(kmsSigner)
    accountSyncBridge.connect(kmsSigner)
    balanceSyncBridge.connect(kmsSigner)

    let deadline
    let msgSalt
    let kmsSig

    console.log(`*** AccountSyncBridgeの参照先コントラクトアドレス更新`)
    deadline = (await getTime()) + 10
    msgSalt = toBytes32('accountSync')
    kmsSig = await kmsSigner.sign(['bytes32', 'uint256'], [msgSalt, deadline])

    await executeReceipt(
      accountSyncBridge.setAddress(
        providerAddress,
        validatorAddress,
        accessCtrlAddress,
        businessZoneAccountAddress,
        ibcTokenAddress,
        deadline,
        kmsSig,
      ),
    )

    console.log(`*** BalanceSyncBridgeの参照先コントラクトアドレス更新`)
    deadline = (await getTime()) + 10
    msgSalt = toBytes32('balanceSync')
    kmsSig = await kmsSigner.sign(['bytes32', 'uint256'], [msgSalt, deadline])

    await executeReceipt(
      balanceSyncBridge.setAddress(ibcTokenAddress, accountAddress, accessCtrlAddress, deadline, kmsSig),
    )

    console.log(`*** JPYTokenTransferBridgeの参照先コントラクトアドレス更新`)
    deadline = (await getTime()) + 10
    msgSalt = toBytes32('tokenTransfer')
    kmsSig = await kmsSigner.sign(['bytes32', 'uint256'], [msgSalt, deadline])

    await executeReceipt(tokenTransferBridge.setAddress(ibcTokenAddress, accessCtrlAddress, deadline, kmsSig))
  })
