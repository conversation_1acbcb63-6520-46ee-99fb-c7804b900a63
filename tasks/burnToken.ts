import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { executeReceipt } from './common/executeReceipt'
import { Token } from '@/types/contracts/Token'

wrappedTask('burnToken', 'burn token', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'issuer id')
  .addParam('accountId', 'account id')
  .addParam('amount', 'mint amount')
  .addParam('issuerKey', 'issuer key')
  .setAction(async (taskArguments, hre) => {
    const issuerId = convertToHex({ hre, value: taskArguments.issuerId || '' })
    const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })
    const amount = Number(taskArguments.amount || '')
    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner<Token>({ hre, contractName: 'Token' })

    console.log(`*** Token burn: ${amount}`)

    await executeReceipt(contract.burn(issuerId, accountId, amount, traceId))
  })
