import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { executeReceipt } from './common/executeReceipt'
import { getTime } from './common/tools'
import * as PrivateKey from '@/privateKey'
import { Issuer } from '@/types/contracts/Issuer'

wrappedTask('setAccountStatus', 'set account status', {
  filePath: path.basename(__filename),
})
  .addParam('issuerId', 'issuer id')
  .addParam('accountId', 'account id')
  .addParam('accountStatus', 'account status')
  .addParam('reasonCode', 'reason code')
  .addParam('issuerKey', 'issuer key')
  .setAction(async (taskArguments, hre) => {
    const issuerId = convertToHex({ hre, value: taskArguments.issuerId || '' })
    const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })
    const accountStatus = convertToHex({ hre, value: taskArguments.accountStatus || '' })
    const reasonCode = convertToHex({ hre, value: taskArguments.reasonCode || '' })
    const issuerKey = taskArguments.issuerKey || ''

    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner<Issuer>({ hre, contractName: 'Issuer' })

    console.log(`*** Set Account Status`)
    const deadline = await getTime()
    const sig = await PrivateKey.sig(
      issuerKey,
      ['bytes32', 'bytes32', 'bytes32', 'uint256'],
      [issuerId, accountId, reasonCode, deadline],
    )

    await executeReceipt(
      contract.setAccountStatus(issuerId, accountId, accountStatus, reasonCode, traceId, deadline, sig[0]),
    )
  })
