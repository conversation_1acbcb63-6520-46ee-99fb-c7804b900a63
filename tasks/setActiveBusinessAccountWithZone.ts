import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { executeReceipt } from './common/executeReceipt'
import { Validator } from '@/types/contracts/Validator'

wrappedTask('setActiveBusinessAccountWithZone', 'Set Active BusinessZone Account.', {
  filePath: path.basename(__filename),
})
  .addParam('validatorId', 'validator id')
  .addParam('zoneId', 'zone id')
  .addParam('accountId', 'account id')
  .setAction(async (taskArguments, hre) => {
    const validatorId = convertToHex({ hre, value: taskArguments.validatorId || '' })
    const zoneId = taskArguments.zoneId || ''
    const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })
    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner<Validator>({ hre, contractName: 'Validator' })

    await executeReceipt(contract.setActiveBusinessAccountWithZone(validatorId, zoneId, accountId, traceId))
  })
