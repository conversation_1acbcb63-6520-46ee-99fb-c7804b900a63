#!/bin/bash
ROOT_DIR=$(
  cd $(dirname "${BASH_SOURCE[0]}")/.. || exit
  pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)

# Select network from "hardhat list-networks"
if [ -n "$1" ]; then
  CHOICE=$1
else
  # Hardhatからネットワーク一覧を取得
  DIRECTORIES=$(npx hardhat list-networks)
  menu "Select the network to create kms key:" "$DIRECTORIES"
  # menu 関数後の $CHOICE を確認
  if [ -z "$CHOICE" ]; then
    message "err" "Invalid selection. Please try again."
    exit 1
  fi
fi

# set NETWORK
export NETWORK=$CHOICE

getBizZoneAccountStatus() {
  local account_id=$1

  npx hardhat getBizZoneAccountStatus --network "${NETWORK}" \
    --account-id "${account_id}" \
    --zone-id "3001"
}

show_accounts_status() {
  local account_id=$1

  pushd ${ROOT_DIR} > /dev/null
  echo "info" "show status of FIN Zone accounts: ${account_id}"
  npx hardhat getAccountAll --network "${NETWORK}" \
    --account-id "${account_id}" \
    --valid-id "${VALID_ID}"

  popd > /dev/null
}

frozenForceBurn() {
  local account_id=$1

  pushd ${ROOT_DIR} > /dev/null
  npx hardhat setAccountStatus --network "${NETWORK}" \
    --issuer-id "${ISSUER_ID}" \
    --account-id "${account_id}" \
    --account-status "frozen" \
    --reason-code "${REASON_CODE}" \
    --issuer-key "${KEY_ISSUER}"

  show_accounts_status "${account_id}"

  # Check the output of checkExchange
  npx hardhat forceBurnToken --network "${NETWORK}" \
    --issuer-id "${ISSUER_ID}" \
    --account-id "${account_id}" \
    --issuer-key "${KEY_ISSUER}"

  show_accounts_status "${account_id}"

  npx hardhat setAccountStatus --network "${NETWORK}" \
    --issuer-id "${ISSUER_ID}" \
    --account-id "${account_id}" \
    --account-status "active" \
    --reason-code "${REASON_CODE}" \
    --issuer-key "${KEY_ISSUER}"

  getBizZoneAccountStatus "${account_id}"
}

if [[ "$NETWORK" == *Fin ]]; then
  frozenForceBurn "${ACCOUNT_ID_4}"
  pushd . > /dev/null
  output=$(npx hardhat checkSyncAccount --network "${NETWORK}" \
    --account-id "${ACCOUNT_ID_4}" \
    --valid-id "${VALID_ID}" \
    --zone-id "${ZONE_ID}" \
    --account-status "${STATUS_APPLYING}")

  echo "$output"

  if [[ $output != *"result | ok"* ]]; then
    echo "failed checkSyncAccount for account ${account_id}."
  fi
fi

sync_account() {
  local account_id=$1
  local account_name=$2

  echo "info" "start syncAccount for account ${account_id}."

  npx hardhat syncAccount --network "${NETWORK}" \
    --validator-id "${VALID_ID}" \
    --account-id "${account_id}" \
    --account-name "${account_name}" \
    --from-zone-id "${ZONE_ID}" \
    --zone-name "${ZONE_NAME}" \
    --account-status "${STATUS_APPLYING}" \
    --approval-amount "${ZERO_AMOUNT}" \
    --trace-id "${TRACE_ID}" \
    --timeout-height 1000000 \
    --reason-code "${REASON_CODE}"

  echo "success" "syncAccount for account ${account_id} is successful."

  show_accounts_status "${account_id}"

  popd > /dev/null
}

if [[ "$NETWORK" == *Biz ]]; then
  sync_account "${ACCOUNT_ID_4}" "${ACCOUNT_NAME_4}"
fi
