#!/bin/bash

ROOT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")/.." || exit; pwd)
BIN_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")"; pwd)

# Select network from "hardhat list-networks"
if [ -n "$1" ]; then
  CHOICE=$1
else
  # Hardhatからネットワーク一覧を取得
  DIRECTORIES=$(npx hardhat list-networks)
  menu "Select the network to create kms key:" "$DIRECTORIES"

  # menu 関数後の $CHOICE を確認
  if [ -z "$CHOICE" ]; then
    message "err" "Invalid selection. Please try again."
    exit 1
  fi
fi

# Set NETWORK
export NETWORK=$CHOICE

if [[ "$NETWORK" == *Biz ]]; then
  npx hardhat syncAccount --network "${NETWORK}" \
    --validator-id "${VALID_ID}" \
    --account-id "${ACCOUNT_ID_3}" \
    --account-name "${ACCOUNT_NAME_3}" \
    --from-zone-id "${ZONE_ID}" \
    --zone-name "${ZONE_NAME}" \
    --account-status "${STATUS_APPLYING}" \
    --approval-amount "${ZERO_AMOUNT}" \
    --trace-id "${TRACE_ID}" \
    --timeout-height 3747488 \
    --reason-code "${REASON_CODE}"

  npx hardhat syncAccount --network "${NETWORK}" \
    --validator-id "${VALID_ID}" \
    --account-id "${ACCOUNT_ID_4}" \
    --account-name "${ACCOUNT_NAME_4}" \
    --from-zone-id "${ZONE_ID}" \
    --zone-name "${ZONE_NAME}" \
    --account-status "${STATUS_APPLYING}" \
    --approval-amount "${ZERO_AMOUNT}" \
    --trace-id "${TRACE_ID}" \
    --timeout-height 3747488 \
    --reason-code "${REASON_CODE}"
fi

output=$(npx hardhat getAccountAll --network "${NETWORK}" \
  --account-id "${ACCOUNT_ID_3}" \
  --valid-id "${VALID_ID}")
echo "$output"

output=$(npx hardhat getAccountAll --network "${NETWORK}" \
  --account-id "${ACCOUNT_ID_4}" \
  --valid-id "${VALID_ID}")
echo "$output"

if [[ "$NETWORK" == *Fin ]]; then
  npx hardhat setActiveBusinessAccountWithZone --network "${NETWORK}" \
    --validator-id "${VALID_ID}" \
    --account-id "${ACCOUNT_ID_3}" \
    --zone-id "${BIZ_ZONE_ID}"

  npx hardhat getAccountAll --network "${NETWORK}" \
    --account-id "${ACCOUNT_ID_3}" \
    --valid-id "${VALID_ID}"

  npx hardhat setActiveBusinessAccountWithZone --network "${NETWORK}" \
    --validator-id "${VALID_ID}" \
    --account-id "${ACCOUNT_ID_4}" \
    --zone-id "${BIZ_ZONE_ID}"

  npx hardhat getAccountAll --network "${NETWORK}" \
    --account-id "${ACCOUNT_ID_4}" \
    --valid-id "${VALID_ID}"
fi
