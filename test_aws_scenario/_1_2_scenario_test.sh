#!/bin/bash
# ローカルの場合、_1_2_scenario_test.sh local
# AWS環境でシングル・マルチテナントの場合、_1_2_scenario_test.sh
# AWS環境でマルチテナントで2回目（別ゾーン）の場合、_1_2_scenario_test.sh multi {対象のゾーン}

ROOTDIR=$(cd "$(dirname "${BASH_SOURCE[0]}")"/.. && pwd)
SCRIPTDIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)

if [[ "$1" != "local" ]]; then

  # biz
  BIZ_AWS_PROFILE=$AWS_PROFILE

  # Load environment variables
  if [ -f "${SCRIPTDIR}/env/.${AWS_PROFILE}" ]; then
    export $(grep -v "^#" "${SCRIPTDIR}/env/.${AWS_PROFILE}" | xargs)
  else
    message "err" "Please specify an environment name that exists in ./bin/main/env/"
    exit 1
  fi

  # 2.ポートフォワード接続する
  "${SCRIPTDIR}/../_2_port_forward.sh" mainBiz
  sleep 5
  wait

  source "${SCRIPTDIR}/_load_env_test_main.sh"

  if [[ "$1" == "multi" ]]; then
    export ZONE_ID=$2
  fi
  # 1_2.BZ口座開設
  "${SCRIPTDIR}/_1_2_synchronous_industry.sh" mainBiz

  # 6. ポートフォワードを切断する
  "${SCRIPTDIR}/../_6_disconnect_port_forward.sh" mainBiz

  # fin
  FIN_AWS_PROFILE=$AWS_PROFILE_FIN
  export AWS_PROFILE=$FIN_AWS_PROFILE

  # Load environment variables
  if [ -f "${SCRIPTDIR}/env/.${AWS_PROFILE}" ]; then
    export $(grep -v "^#" "${SCRIPTDIR}/env/.${AWS_PROFILE}" | xargs)
  else
    message "err" "Please specify an environment name that exists in ./bin/main/env/"
    exit 1
  fi

  # 2.ポートフォワード接続する
  "${SCRIPTDIR}/../_2_port_forward.sh" mainFin
  sleep 5
  wait

  source "${SCRIPTDIR}/_load_env_test_main.sh"

  if [[ "$1" == "multi" ]]; then
    export BIZ_ZONE_ID=$2
  fi
  # 1_2.BZ口座開設
  "${SCRIPTDIR}/_1_2_synchronous_industry.sh" mainFin

  # 6. ポートフォワードを切断する
  "${SCRIPTDIR}/../_6_disconnect_port_forward.sh" mainFin

else
  source "${SCRIPTDIR}/_load_env_test_main.sh" localBiz

  # 1_2.BZ口座開設
  "${SCRIPTDIR}/_1_2_synchronous_industry.sh" localBiz

  source "${SCRIPTDIR}/_load_env_test_main.sh" localFin

  # 1_2.BZ口座開設
  "${SCRIPTDIR}/_1_2_synchronous_industry.sh" localFin
fi