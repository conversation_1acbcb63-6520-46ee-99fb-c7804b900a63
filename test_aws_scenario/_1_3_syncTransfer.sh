#!/bin/bash

ROOT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)
BIN_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)

# ネットワーク選択
if [ -n "$1" ]; then
  CHOICE=$1
else
  DIRECTORIES=$(npx hardhat list-networks)
  menu "Select the network to create kms key:" "$DIRECTORIES"
  if [ -z "$CHOICE" ]; then
    message "err" "Invalid selection. Please try again."
    exit 1
  fi
fi

export NETWORK=$CHOICE

if [[ "$NETWORK" == *Fin ]]; then
  npx hardhat registerEscrowAcc \
    --src-zone-id "$ZONE_ID" \
    --dst-zone-id "$BIZ_ZONE_ID" \
    --escrow-account "$ACCOUNT_ID_1" \
    --network "$NETWORK"

  npx hardhat mintToken \
    --network "$NETWORK" \
    --issuer-id "$ISSUER_ID" \
    --account-id "$ACCOUNT_ID_3" \
    --amount "$MINT_AMOUNT" \
    --issuer-key "$KEY_ISSUER"

  npx hardhat getAccountAll --network "$NETWORK" \
    --account-id "$ACCOUNT_ID_3" \
    --valid-id "$VALID_ID"

  output=$(npx hardhat checkExchange --network "$NETWORK" \
    --account-id "$ACCOUNT_ID_3" \
    --to-zone-id "$BIZ_ZONE_ID" \
    --from-zone-id "$ZONE_ID" \
    --amount "$CHARGE_AMOUNT")

  echo "$output"

  if [[ "$output" == *"result | ok"* ]]; then
    message "info" "start charge to account3"

    npx hardhat transfer --network "$NETWORK" \
      --account-id "$ACCOUNT_ID_3" \
      --from-zone-id "$ZONE_ID" \
      --to-zone-id "$BIZ_ZONE_ID" \
      --amount "$CHARGE_AMOUNT" \
      --timeout-height 1000000
  else
    message "err" "Failed checkExchange."
  fi

  npx hardhat getAccountAll --network "$NETWORK" \
    --account-id "$ACCOUNT_ID_3" \
    --valid-id "$VALID_ID"

  output=$(npx hardhat checkTransaction --network "$NETWORK" \
    --send-account-id "$ACCOUNT_ID_3" \
    --from-account-id "$ACCOUNT_ID_3" \
    --to-account-id "$ACCOUNT_ID_4" \
    --zone-id "$ZONE_ID" \
    --valid-id "$VALID_ID" \
    --amount "$TRANSFER_AMOUNT")

  echo "$output"

  if [[ "$output" != *"result | ok"* ]]; then
    message "err" "Failed checkTransaction."
  fi
fi

if [[ "$NETWORK" == *Biz ]]; then
  npx hardhat registerEscrowAcc \
    --src-zone-id "$ZONE_ID" \
    --dst-zone-id "$BIZ_ZONE_ID" \
    --escrow-account "$ACCOUNT_ID_1" \
    --network "$NETWORK"

  for ACCOUNT in "$ACCOUNT_ID_3" "$ACCOUNT_ID_4"; do
    npx hardhat getAccountAll --network "$NETWORK" \
      --account-id "$ACCOUNT" \
      --valid-id "$VALID_ID"
  done

  npx hardhat transferSingle --network "$NETWORK" \
    --send-account-id "$ACCOUNT_ID_3" \
    --from-account-id "$ACCOUNT_ID_3" \
    --to-account-id "$ACCOUNT_ID_4" \
    --amount "$TRANSFER_AMOUNT" \
    --misc-value-1 "$MISC_VALUE_1" \
    --misc-value-2 "$MISC_VALUE_2" \
    --memo "$MEMO" \
    --trace-id "$TRACE_ID"

  for ACCOUNT in "$ACCOUNT_ID_3" "$ACCOUNT_ID_4"; do
    npx hardhat getAccountAll --network "$NETWORK" \
      --account-id "$ACCOUNT" \
      --valid-id "$VALID_ID"
  done
fi
