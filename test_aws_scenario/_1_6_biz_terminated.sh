#!/bin/bash
ROOT_DIR=$(
  cd $(dirname "${BASH_SOURCE[0]}")/.. || exit
  pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)

# Select network from "hardhat list-networks"
if [ -n "$1" ]; then
  CHOICE=$1
else
  # Hardhatからネットワーク一覧を取得
  DIRECTORIES=$(npx hardhat list-networks)
  menu "Select the network to create kms key:" "$DIRECTORIES"
  # menu 関数後の $CHOICE を確認
  if [ -z "$CHOICE" ]; then
    message "err" "Invalid selection. Please try again."
    exit 1
  fi
fi

# set NETWORK
export NETWORK=$CHOICE

biz_terminated() {
  local account_id=$1

  pushd ${ROOT_DIR} > /dev/null
  local output=$(npx hardhat getAccountAll --network "${NETWORK}" \
    --account-id "${account_id}" \
    --valid-id "${VALID_ID}")

  echo "$output"
  popd > /dev/null

  # Check the output of getAccountAll
  if [[ $output != *"Not linked from Biz Zone."* ]]; then
    npx hardhat setBizZoneTerminated --network "${NETWORK}" \
      --account-id "${account_id}" \
      --zone-id "${BIZ_ZONE_ID}"
    
    output=$(npx hardhat getAccountAll --network "${NETWORK}" \
      --account-id "${account_id}" \
      --valid-id "${VALID_ID}")

    echo "$output"
  else
    echo "BZ account is not linked."
  fi
}

biz_terminated "${ACCOUNT_ID_4}"
