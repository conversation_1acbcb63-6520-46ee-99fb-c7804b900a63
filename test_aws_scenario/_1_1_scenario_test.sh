#!/bin/bash
# ローカルの場合、_1_1_scenario_test.sh local
# AWS環境でシングル・マルチテナントの場合、_1_1_scenario_test.sh
# AWS環境でマルチテナントで2回目（別ゾーン）の場合、_1_1_scenario_test.sh multi {対象のゾーン}
# bizのプロファイルを指定して実施する

ROOTDIR=$(cd "$(dirname "${BASH_SOURCE[0]}")"/.. && pwd)
SCRIPTDIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)

if [[ "$1" != "local" ]]; then
  # biz
  BIZ_AWS_PROFILE=$AWS_PROFILE

  # Load environment variables
  if [ -f "${SCRIPTDIR}/env/.${AWS_PROFILE}" ]; then
    export $(grep -v "^#" "${SCRIPTDIR}/env/.${AWS_PROFILE}" | xargs)
  else
    message "err" "Please specify an environment name that exists in ./bin/main/env/"
    exit 1
  fi

  # 1.kmsファイルを作成する
  "${SCRIPTDIR}/../_1a_generate_kms_key.sh" mainBiz

  # 2.ポートフォワード接続する
  "${SCRIPTDIR}/../_2_port_forward.sh" mainBiz
  sleep 5
  wait

  source "${SCRIPTDIR}/_load_env_test_main.sh"

  if [[ "$1" == "multi" ]]; then
    export ZONE_ID=$2
  fi
  # 1_1.BZ口座開設準備
  "${SCRIPTDIR}/_1_1_prepare_synchronous.sh" mainBiz

  # 5. ポートフォワードを切断する
  "${SCRIPTDIR}/../_6_disconnect_port_forward.sh" mainBiz

  # fin
  FIN_AWS_PROFILE=$AWS_PROFILE_FIN
  export AWS_PROFILE=$FIN_AWS_PROFILE

  # Load environment variables
  if [ -f "${SCRIPTDIR}/env/.${AWS_PROFILE}" ]; then
    export $(grep -v "^#" "${SCRIPTDIR}/env/.${AWS_PROFILE}" | xargs)
  else
    message "err" "Please specify an environment name that exists in ./bin/main/env/"
    exit 1
  fi

  # 1.kmsファイルを作成する
  "${SCRIPTDIR}/../_1a_generate_kms_key.sh" mainFin

  # 2.ポートフォワード接続する
  "${SCRIPTDIR}/../_2_port_forward.sh" mainFin
  sleep 5
  wait

  source "${SCRIPTDIR}/_load_env_test_main.sh"

  if [[ "$1" == "multi" ]]; then
    # 1_1.BZ口座開設準備
    "${SCRIPTDIR}/_1_1_prepare_synchronous.sh" mainFin multi
  else
    # 1_1.BZ口座開設準備
    "${SCRIPTDIR}/_1_1_prepare_synchronous.sh" mainFin
  fi

  # 5. ポートフォワードを切断する
  "${SCRIPTDIR}/../_6_disconnect_port_forward.sh" mainFin

else
  source "${SCRIPTDIR}/_load_env_test_main.sh" localBiz
  "${SCRIPTDIR}/_1_1_prepare_synchronous.sh" localBiz

  source "${SCRIPTDIR}/_load_env_test_main.sh" localFin
  "${SCRIPTDIR}/_1_1_prepare_synchronous.sh" localFin
fi
