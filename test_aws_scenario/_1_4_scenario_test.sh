#!/bin/bash
# ローカルの場合、_1_4_scenario_test.sh local
# AWS環境でシングル・マルチテナントの場合、_1_4_scenario_test.sh
# AWS環境でマルチテナントで2回目（別ゾーン）の場合、_1_4_scenario_test.sh multi {対象のゾーン}

ROOTDIR=$(cd $(dirname $BASH_SOURCE)/..; pwd)
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)

if [[ "$1" != "local" ]]; then

  # biz
  BIZ_AWS_PROFILE=$AWS_PROFILE
  export AWS_PROFILE=$BIZ_AWS_PROFILE

  # 環境変数を読み込む
  if [ -f ${SCRIPTDIR}/env/."$AWS_PROFILE" ]; then
    export $(cat ${SCRIPTDIR}/env/."$AWS_PROFILE" | grep -v "^#" | xargs)
  else
    message "err" "Please specify an environment name that exists in ./bin/main/env/"
    exit 1
  fi

  # 2.ポートフォワード接続する
  ${SCRIPTDIR}/../_2_port_forward.sh mainBiz
  sleep 5
  wait

  source "${SCRIPTDIR}"/_load_env_test_main.sh

  if [[ "$1" == "multi" ]]; then
    export ZONE_ID=$2
  fi
  ${SCRIPTDIR}/_1_4_discharge.sh mainBiz

  # 5. ポートフォワードを切断する
  ${SCRIPTDIR}/../_6_disconnect_port_forward.sh mainBiz

  # fin
  FIN_AWS_PROFILE=$AWS_PROFILE_FIN
  export AWS_PROFILE=FIN_AWS_PROFILE

  # 環境変数を読み込む
  if [ -f ${SCRIPTDIR}/env/."$AWS_PROFILE" ]; then
    export $(cat ${SCRIPTDIR}/env/."$AWS_PROFILE" | grep -v "^#" | xargs)
  else
    message "err" "Please specify an environment name that exists in ./bin/main/env/"
    exit 1
  fi

  # 2.ポートフォワード接続する
  ${SCRIPTDIR}/../_2_port_forward.sh mainFin
  sleep 5
  wait

  source "${SCRIPTDIR}"/_load_env_test_main.sh

  if [[ "$1" == "multi" ]]; then
    export BIZ_ZONE_ID=$2
  fi
  ${SCRIPTDIR}/_1_4_discharge.sh mainFin

  # 5. ポートフォワードを切断する
  ${SCRIPTDIR}/../_6_disconnect_port_forward.sh mainFin

else

  source "${SCRIPTDIR}"/_load_env_test_main.sh localFin

  ${SCRIPTDIR}/_1_4_discharge.sh localFin

  source "${SCRIPTDIR}"/_load_env_test_main.sh localBiz

  ${SCRIPTDIR}/_1_4_discharge.sh localBiz

fi
