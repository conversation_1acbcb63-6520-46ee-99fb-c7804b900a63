#!/bin/bash
ROOT_DIR=$(
  cd $(dirname "${BASH_SOURCE[0]}")/.. || exit
  pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)

# Select network from "hardhat list-networks"
if [ -n "$1" ]; then
  CHOICE=$1
else
  # Hardhatからネットワーク一覧を取得
  DIRECTORIES=$(npx hardhat list-networks)
  menu "Select the network to create kms key:" "$DIRECTORIES"
  # menu 関数後の $CHOICE を確認
  if [ -z "$CHOICE" ]; then
    message "err" "Invalid selection. Please try again."
    exit 1
  fi
fi

# set NETWORK
export NETWORK=$CHOICE

if [[ "$NETWORK" == *Fin ]]; then
  output=$(npx hardhat checkSyncAccount --network "${NETWORK}" \
    --account-id "${ACCOUNT_ID_4}" \
    --valid-id "${VALID_ID}" \
    --zone-id "${BIZ_ZONE_ID}" \
    --account-status "${STATUS_TERMINATING}")

  echo "$output"

  if [[ $output != *"result | ok"* ]]; then
    echo "failed checkSyncAccount."
  fi
fi

if [[ "$NETWORK" == *Biz ]]; then
  npx hardhat syncAccount --network "${NETWORK}" \
    --validator-id "${VALID_ID}" \
    --account-id "${ACCOUNT_ID_4}" \
    --account-name "${ACCOUNT_NAME_4}" \
    --from-zone-id "${ZONE_ID}" \
    --zone-name "${ZONE_NAME}" \
    --account-status "${STATUS_TERMINATING}" \
    --approval-amount "${ZERO_AMOUNT}"  \
    --trace-id "${TRACE_ID}"  \
    --timeout-height 1000000  \
    --reason-code "${REASON_CODE}"
fi
