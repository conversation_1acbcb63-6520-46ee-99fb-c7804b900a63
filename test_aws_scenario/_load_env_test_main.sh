#!/bin/bash

NETWORK=$1

# privateKey.ts[0]~[4]を使用
export KEY_PROV=59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d
export KEY_VALID=5de4111afa1a4b94908f83103eb1f1706367c2e68ca870fc3fb9a804cdab365a
export KEY_ISSUER=7c852118294e51e653712a81e05800f419141751be58f605c371e15141b007a6
export KEY_ACCOUNT=47e179ec197488593b187f80a00eb0da91f1b9d0b13f8733639f19c30a34926a

export STATUS_APPLYING="applying"
export STATUS_ACTIVE="active"
export STATUS_TERMINATING="terminating"
export STATUS_TERMINATED="terminated"

export REASON_CODE=1

export ZERO_AMOUNT=0

export TRACE_ID="test"


export MISC_VALUE_1=""
export MISC_VALUE_2=""
export MEMO="test"

export PROV_ID=1111
export PROV_NAME="provider"

export ISSUER_ID=2221
export BANK_CODE=9999
export ISSUER_NAME="issuer"

export VALID_ID=8888
export VALID_NAME="validator"

export ACCOUNT_ID_1=300
export ACCOUNT_ID_2=301
export ACCOUNT_ID_3=302
export ACCOUNT_ID_4=303
export ACCOUNT_NAME_1="account1"
export ACCOUNT_NAME_2="account2"
export ACCOUNT_NAME_3="account3"
export ACCOUNT_NAME_4="account4"

export MINT_AMOUNT=3000

export CHARGE_AMOUNT=1000

export DISCHARGE_AMOUNT=500

export TRANSFER_AMOUNT=750

export LIMIT_AMOUNT=5000

export DAILY_LIMIT_AMOUNT=6000

export TOKEN_ID=3000
export TOKEN_NAME="token"
export TOKEN_PEGKIND="JPY"
export TOKEN_SYMBOL="symbol"
export TOKEN_DEPOSITED=false

export RENEWABLE_ID_1="1"
export METADATA_ID="test"
export METADATA_HASH="test"

if [[ "$NETWORK" == *Fin ]]; then
  export ZONE_ID=3000
  export ZONE_NAME="finzone"
  export BIZ_ZONE_ID=3001
  export BIZ_ZONE_NAME="bizzone"
else
  export ZONE_ID=3001
  export ZONE_NAME="bizzone"
  export FIN_ZONE_ID=3000
  export FIN_ZONE_NAME="finzone"
fi

if [ $NETWORK == "localFin" ]; then
  export PROVIDER="http://localhost:18451"
  export KMS_ENDPOINT_URL=http://localhost:14566
else
  export PROVIDER="http://localhost:28451"
  export KMS_ENDPOINT_URL=http://localhost:24566
fi
