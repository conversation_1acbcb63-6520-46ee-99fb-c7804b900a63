#!/bin/bash

set -euo pipefail

ROOT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)
BIN_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)

# ネットワーク選択
if [ -n "${1:-}" ]; then
  CHOICE=$1
else
  DIRECTORIES=$(npx hardhat list-networks)
  menu "Select the network to create kms key:" "$DIRECTORIES"
  if [ -z "${CHOICE:-}" ]; then
    message "err" "Invalid selection. Please try again."
    exit 1
  fi
fi

export NETWORK=$CHOICE

if [[ "$NETWORK" == *Fin ]]; then
  for ACCOUNT in "$ACCOUNT_ID_3" "$ACCOUNT_ID_4"; do
    npx hardhat getAccountAll --network "$NETWORK" \
      --account-id "$ACCOUNT" \
      --valid-id "$VALID_ID"
  done

  output=$(npx hardhat checkExchange --network "$NETWORK" \
    --account-id "$ACCOUNT_ID_4" \
    --to-zone-id "$ZONE_ID" \
    --from-zone-id "$BIZ_ZONE_ID" \
    --amount "$DISCHARGE_AMOUNT")

  echo "$output"

  if [[ "$output" != *"result | ok"* ]]; then
    message "err" "Exchange check failed."
    exit 1
  fi
fi

if [[ "$NETWORK" == *Biz ]]; then
  npx hardhat transfer --network "$NETWORK" \
    --account-id "$ACCOUNT_ID_4" \
    --from-zone-id "$ZONE_ID" \
    --to-zone-id "$FIN_ZONE_ID" \
    --amount "$DISCHARGE_AMOUNT" \
    --timeout-height 1000000

  npx hardhat getAccountAll --network "$NETWORK" \
    --account-id "$ACCOUNT_ID_4" \
    --valid-id "$VALID_ID"
fi
