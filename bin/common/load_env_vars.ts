import fs from 'fs'
import path from 'path'
import { message, SCRIPTDIR } from './utils'

/**
 * 環境変数の読み込みとエクスポート
 * @param awsProfile AWSプロファイル名
 * @param zoneId 上書きしたい場合設定する（デフォルトは指定しなくていい)
 */
export function loadEnvVars(awsProfile: string, zoneId?: string): void {
  const envPathByAwsProfile = path.join(SCRIPTDIR, `env/.${awsProfile}`)
  const envPathByProjectEnv = path.join(SCRIPTDIR, `env/.${process.env.PROJECT_ENV}`)

  let envPath = ''
  if (envPathByAwsProfile && fs.existsSync(envPathByAwsProfile)) {
    envPath = envPathByAwsProfile
  } else if (envPathByProjectEnv && fs.existsSync(envPathByProjectEnv)) {
    envPath = envPathByProjectEnv
  }

  if (fs.existsSync(envPath)) {
    const envLines = fs
      .readFileSync(envPath, 'utf-8')
      .split('\n')
      .filter((line) => line.trim() && !line.startsWith('#'))

    for (const line of envLines) {
      const index = line.indexOf('=')
      const key = line.slice(0, index).trim()
      const value = line
        .slice(index + 1)
        .trim()
        .replace(/^"|"$/g, '')
      if (key && value !== undefined) {
        process.env[key] = value
      }
    }

    // zoneIdの指定がある場合上書き
    if (envPath == envPathByProjectEnv && zoneId) {
      process.env.ZONE_ID = zoneId
    }
  } else {
    message('err', 'Please specify an environment name that exists in ./bin/main/env/')
    process.exit(1)
  }
}
