import path from 'path'
import { SCRIPTDIR } from './utils'

/**
 * AWSプロファイル読み込み
 * @param network 対象ネットワーク
 * @returns AWSプロファイル
 */
export function loadAwsProf(network: string): string {
  if (network.includes('local')) {
    process.env.AWS_CONFIG_FILE = path.join(SCRIPTDIR, 'env/.aws/config-localstack')

    if (network === 'localFin') {
      process.env.AWS_PROFILE = 'localstack-fin'
    } else {
      process.env.AWS_PROFILE = 'localstack-biz'
    }
  }

  return process.env.AWS_PROFILE ?? ''
}
