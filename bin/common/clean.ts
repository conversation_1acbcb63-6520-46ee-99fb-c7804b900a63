import { execSync } from 'child_process'
import * as fs from 'fs'
import * as path from 'path'
import { message } from './utils'

function main() {
  const args = process.argv.slice(2)
  if (args.length !== 1) {
    console.error('Error: Please specify a folder name.')
    process.exit(1)
  }

  const network = args[0]

  // __dirname は実行中スクリプトのパスを取得
  const deploymentsDir = path.resolve(__dirname, '../../deployments')
  const targetDir = path.join(deploymentsDir, network)

  // Hardhat clean
  execSync('npx hardhat clean', { stdio: 'inherit' })

  if (fs.existsSync(targetDir)) {
    message('success', `Deleting folder: ${targetDir}`)
    fs.rmSync(targetDir, { recursive: true, force: true })

    console.log('Hardhat project cleaned successfully.')
  } else {
    message('err', `Not Found folder: ${targetDir}`)
  }
}

main()
