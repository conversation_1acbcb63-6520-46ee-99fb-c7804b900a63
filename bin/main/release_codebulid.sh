#!/bin/bash -eu
ROOTDIR=$(cd $(dirname $BASH_SOURCE)/../..; pwd)
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
source $SCRIPTDIR/../common/utils.sh

# 引数としてデプロイ環境を指定する(github workflowから渡された値をbuildspec.yml側で指定)
if [ $# -eq 0 ]; then
  echo "環境名が指定されていません"
  exit 1
fi
if [[ "$2" = *[!0-9]* ]]; then
  echo "$1 は数字ではありません。"
  exit 1
fi    
export PROJECT_ENV=$1
export NUMBER_OF_BIZZONE=${2:-1}
export CODEBUILD_ENV=true

# 引数からNETWORKを指定する(github workflowから渡された値をbuildspec.yml側で指定)
if [[ "$1" == *"fin"* ]]; then
  export NETWORK=mainFin
else
  export NETWORK=mainBiz
fi
message "info" "Using provided network: $NETWORK"

# Error handling function
handle_error() {
    message "err" "An unexpected error occurred. Exiting."
    exit 1
}
trap 'handle_error' ERR

# Load environment variables
if [ -f ${SCRIPTDIR}/env/."$PROJECT_ENV" ]; then
    export $(cat ${SCRIPTDIR}/env/."$PROJECT_ENV" | grep -v "^#" | xargs)
else
    message "err" "Please specify an environment name that exists in ./bin/main/env/"
    exit 1
fi

# 1.envファイルを作成する
# Create KMS key
${SCRIPTDIR}/_1a_generate_kms_key.sh ${NETWORK}
echo $BESU_NAMESPACE

# 2.BESUのプライベートIPを設定する
BESU_IP=$(aws ec2 describe-instances --filters "Name=tag:Name,Values=${BESU_NAMESPACE}" --query 'Reservations[*].Instances[*].PrivateIpAddress[]' --region ap-northeast-1 --output json | jq -r '.[0]')

echo $BESU_IP
export CODEBUILD_PROVIDER="http://${BESU_IP}:8451"
echo $CODEBUILD_PROVIDER
# Migrationを実行する
RESULT=$(curl -v ${CODEBUILD_PROVIDER}/liveness)
echo $RESULT
if [[ "$RESULT" == *"UP"* ]]; then
  # 3.contract-mainのMigrationを実行する
  ${SCRIPTDIR}/_3_migrate_main.sh
  # 4.デプロイされたコントラクトを確認する
  ${SCRIPTDIR}/_4_deployed_confirmation.sh main
  # contract-ibcのデプロイ準備
  mkdir -p $ROOTDIR/s3-restore/deployments/${NETWORK}
  cp $ROOTDIR/deployments/${NETWORK}/IBCToken.json $ROOTDIR/s3-restore/deployments/${NETWORK}/IBCToken.json
  cp $ROOTDIR/deployments/${NETWORK}/Validator.json $ROOTDIR/s3-restore/deployments/${NETWORK}/Validator.json
  cp $ROOTDIR/deployments/${NETWORK}/Account.json $ROOTDIR/s3-restore/deployments/${NETWORK}/Account.json
  cp $ROOTDIR/deployments/${NETWORK}/AccessCtrl.json $ROOTDIR/s3-restore/deployments/${NETWORK}/AccessCtrl.json
  cp $ROOTDIR/deployments/${NETWORK}/BusinessZoneAccount.json $ROOTDIR/s3-restore/deployments/${NETWORK}/BusinessZoneAccount.json
  # 3.contract-ibcのMigrationを実行する
  ${SCRIPTDIR}/_3_migrate_ibc.sh
  ${SCRIPTDIR}/_3a_set_ibc_app.sh
  # 4.デプロイされたコントラクトを確認する
  ${SCRIPTDIR}/_4_deployed_confirmation.sh ibc
  # 5.EscrowAccountをBizZoneの数だけ追加する
  ${SCRIPTDIR}/_5_register_escrow_account.sh ${NETWORK} ${NUMBER_OF_BIZZONE}
  if [[ $NETWORK == "mainFin" ]]; then
      # 7.コントラクトのバックアップファイルをアップロードする
      ${SCRIPTDIR}/_7_backup_abi_files.sh ${NETWORK}
      # 8.ABIファイルをバックアップする
      ${SCRIPTDIR}/_8_upload_abi_files.sh ${NETWORK} ${ZONE_ID}
  else
      BIZ_ZONE_ID=$((ZONE_ID+NUMBER_OF_BIZZONE-1))
      # 7.コントラクトのバックアップファイルをアップロードする
      ${SCRIPTDIR}/_7_backup_abi_files.sh ${NETWORK} ${BIZ_ZONE_ID}
      # 8.ABIファイルをバックアップする
      ${SCRIPTDIR}/_8_upload_abi_files.sh ${NETWORK} ${BIZ_ZONE_ID}
  fi
else
    echo "接続できません。"
    exit 1
fi

message "success" "Deployment process completed successfully."