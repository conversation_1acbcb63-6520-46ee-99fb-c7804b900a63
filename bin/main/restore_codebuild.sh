#!/bin/bash -eu
ROOTDIR=$(cd $(dirname $BASH_SOURCE)/../..; pwd)
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
source $SCRIPTDIR/../common/utils.sh

if [ $# -eq 0 ]; then
  echo "PROJECT_ENVが指定されていません"
  exit 1
fi
   
export BACKUP_ZONE_ID=$ZONE_ID
export CODEBUILD_ENV=true

# 引数からNETWORKを指定する(github workflowから渡された値をbuildspec.yml側で指定)
if [[ "$1" == *"fin"* ]]; then
  export NETWORK=mainFin
else
  export NETWORK=mainBiz
fi
message "info" "Using provided network: $NETWORK"

# Error handling function
handle_error() {
    message "err" "An unexpected error occurred. Exiting."
    exit 1
}
trap 'handle_error' ERR

# Load environment variables
if [ -f ${SCRIPTDIR}/env/."$PROJECT_ENV" ]; then
    export $(cat ${SCRIPTDIR}/env/."$PROJECT_ENV" | grep -v "^#" | xargs)
else
    message "err" "Please specify an environment name that exists in ./bin/main/env/"
    exit 1
fi

# 1.ABI情報をS3より取得
cd $ROOTDIR || exit 1

# hardhat.config.tsを退避
mv $ROOTDIR/hardhat.config.ts $ROOTDIR/hardhat.config_bk.ts

# Determine S3 prefix based on the environment
S3_PREFIX="s3://$BACKUP/$ZONE_ID/contract/$NETWORK/"
DIRECTORY=$(aws s3 ls $S3_PREFIX | awk '{if ($2 ~ /\/$/) print $2}' | sort | tail -n 1)

if [ -z "$DIRECTORY" ]; then
  message "err" "No backups found."
  exit 1
fi

message "info" "Restoring backup from \"$S3_PREFIX$DIRECTORY\" to \"$ROOTDIR\"..."
aws s3 cp "$S3_PREFIX$DIRECTORY" "$ROOTDIR" --recursive

# hardhat.config.tsを最新に更新
mv $ROOTDIR/hardhat.config_bk.ts $ROOTDIR/hardhat.config.ts

# 2.バックアップファイルのダウンロード
S3_BACKUP_RESTORE_PREFIX="s3://$BACKUP_RESTORE_DATA_S3/$BACKUP_ZONE_ID"
BACKUP_FILES="$ROOTDIR/scripts/backup-restore/backupfiles/$NETWORK"
message "info" "Restoring backup json from \"$S3_BACKUP_RESTORE_PREFIX\" to \"$BACKUP_FILES\"..."
aws s3 cp "$S3_BACKUP_RESTORE_PREFIX" "$BACKUP_FILES" --recursive

# TODO 3.バックアップファイルのチェックポイントデータを作成する
# 4.BESUのプライベートIPを設定する
BESU_IP=$(aws ec2 describe-instances --filters "Name=tag:Name,Values=${BESU_NAMESPACE}" --query 'Reservations[*].Instances[*].PrivateIpAddress[]' --region ap-northeast-1 --output json | jq -r '.[0]')

echo $BESU_IP
export CODEBUILD_PROVIDER="http://${BESU_IP}:8451"
echo $CODEBUILD_PROVIDER
RESULT=$(curl -v ${CODEBUILD_PROVIDER}/liveness)
echo $RESULT
if [[ "$RESULT" == *"UP"* ]]; then
  # 5.リストアを実行する
  ${ROOTDIR}/scripts/backup-restore/restore.sh ${NETWORK}

  # TODO 6.照合用のコントラクトのデータを取得
  # TODO 7.3のバックアップファイルのチェックポイントデータと6の照合用のコントラクトのデータの突合
  # TODO 8.カスタムコントラクトアドレスの登録
else
    echo "接続できません。"
    exit 1
fi

message "success" "Deployment process completed successfully."