#!/bin/bash
ROOTDIR=$(cd $(dirname $BASH_SOURCE)/../..; pwd)
export ROOTDIR=$(cd $(dirname $BASH_SOURCE)/../..; pwd)
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
SCRIPTNAME=$(basename "$0")
source ${SCRIPTDIR}/../common/utils.sh

# Load local AWS_PROFILE
if [ -f ${SCRIPTDIR}/env/."$AWS_PROFILE" ]; then
    source ${SCRIPTDIR}/env/."$AWS_PROFILE"
elif [ -f ${SCRIPTDIR}/env/."$PROJECT_ENV" ]; then
    source ${SCRIPTDIR}/env/."$PROJECT_ENV"
else
    message "err" "Please specify an environment name that exists in ./bin/main/env/"
    exit 1
fi

# Ensure NETWORK environment variable is set
if [ -z "$NETWORK" ]; then
  message "err" "NETWORK environment variable is not set."
  exit 1
fi

message "info" "Starting deployment for network: ${NETWORK}"

export IBC_TOKEN_ADDRESS=$(cat $ROOTDIR/s3-restore/deployments/${NETWORK}/IBCToken.json | jq -r ".address")
export VALIDATOR_ADDRESS=$(cat $ROOTDIR/s3-restore/deployments/${NETWORK}/Validator.json | jq -r ".address")
export ACCOUNT_ADDRESS=$(cat $ROOTDIR/s3-restore/deployments/${NETWORK}/Account.json | jq -r ".address")
export ACCESS_CTRL_ADDRESS=$(cat $ROOTDIR/s3-restore/deployments/${NETWORK}/AccessCtrl.json | jq -r ".address")
export BUSINESS_ZONE_ACCOUNT_ADDRESS=$(cat $ROOTDIR/s3-restore/deployments/${NETWORK}/BusinessZoneAccount.json | jq -r ".address")
export PROVIDER_ADDRESS=$(cat $ROOTDIR/s3-restore/deployments/${NETWORK}/Provider.json | jq -r ".address")

echo デプロイ対象は以下環境で間違いないでしょうか？
echo NETWORK=$NETWORK
echo PROVIDER=$PROVIDER
echo NETWORK_ID=$NETWORK_ID
echo IBC_TOKEN_ADDRESS=$IBC_TOKEN_ADDRESS
echo VALIDATOR_ADDRESS=$VALIDATOR_ADDRESS
echo ACCOUNT_ADDRESS=$ACCOUNT_ADDRESS
echo ACCESS_CTRL_ADDRESS=$ACCESS_CTRL_ADDRESS
echo BUSINESS_ZONE_ACCOUNT_ADDRESS=$BUSINESS_ZONE_ACCOUNT_ADDRESS
echo PROVIDER_ADDRESS=$PROVIDER_ADDRESS

# Check the value of ZONE_ID and set the appropriate tags
case "$ZONE_ID" in
  3000 | [3][0-9][0-9][1-9]) 
    DEPLOY_CMD="npx hardhat deploy --network ${NETWORK} --tags ibc-contracts"
    ;;
  *)
    message "err" "Invalid ZONE_ID: ${ZONE_ID}. Please set ZONE_ID to 3000 ~ 3999."
    exit 1
    ;;
esac

# Execute the deployment command
$DEPLOY_CMD
if [ $? -ne 0 ]; then
  message "err" "Failed to hardhat deploy."
  exit 1
fi

message "success" "Deployment for network: ${NETWORK} completed"