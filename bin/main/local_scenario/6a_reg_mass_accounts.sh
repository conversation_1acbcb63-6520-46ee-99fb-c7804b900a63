#!/bin/bash
# 6a_reg_mass_accounts.sh {NETWORK} {登録件数:1~1000}
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)

if [ -z "$2" ]; then
  echo "Please enter the number of executions"
  exit 1
fi
source "${BIN_DIR}"/_load_env.sh "$1"

FLAG="11"

pushd ${ROOT_DIR} > /dev/null

for ((i=1;i<=$2;i++))
do
  echo ${i}
  ACCOUNT_ID=200${i}
  ACCOUNT_NAME=name${i}
  npx hardhat registerAcc --network "${NETWORK}" \
  --account-id "${ACCOUNT_ID}" \
  --account-name "${ACCOUNT_NAME}" \
  --account-key "${KEY_ACCOUNT}" \
  --valid-id "${VALID_ID}" \
  --issuer-id "${ISSUER_ID}" \
  --issuer-key "${KEY_ISSUER}" \
  --flag "${FLAG}"
done

wait
popd > /dev/null
