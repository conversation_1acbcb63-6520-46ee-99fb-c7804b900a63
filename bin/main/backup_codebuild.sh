#!/bin/bash -eu
ROOTDIR=$(cd $(dirname $BASH_SOURCE)/../..; pwd)
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
source $SCRIPTDIR/../common/utils.sh

if [ $# -eq 0 ]; then
  echo "PROJECT_ENVが指定されていません"
  exit 1
fi
   
export BACKUP_ZONE_ID=$ZONE_ID
export CODEBUILD_ENV=true

# 引数からNETWORKを指定する(github workflowから渡された値をbuildspec.yml側で指定)
if [[ "$1" == *"fin"* ]]; then
  export NETWORK=mainFin
else
  export NETWORK=mainBiz
fi
message "info" "Using provided network: $NETWORK"

# Error handling function
handle_error() {
    message "err" "An unexpected error occurred. Exiting."
    exit 1
}
trap 'handle_error' ERR

# Load environment variables
if [ -f ${SCRIPTDIR}/env/."$PROJECT_ENV" ]; then
    export $(cat ${SCRIPTDIR}/env/."$PROJECT_ENV" | grep -v "^#" | xargs)
else
    message "err" "Please specify an environment name that exists in ./bin/main/env/"
    exit 1
fi

# 1.ABI情報をS3より取得
cd $ROOTDIR || exit 1
# hardhat.config.tsを退避
mv $ROOTDIR/hardhat.config.ts $ROOTDIR/hardhat.config_bk.ts
# Determine S3 prefix based on the environment
S3_PREFIX="s3://$BACKUP/$BACKUP_ZONE_ID/contract/$NETWORK/"
DIRECTORY=$(aws s3 ls $S3_PREFIX | awk '{if ($2 ~ /\/$/) print $2}' | sort | tail -n 1)

if [ -z "$DIRECTORY" ]; then
  message "err" "No backups found."
  exit 1
fi

message "info" "Restoring backup from \"$S3_PREFIX$DIRECTORY\" to \"$ROOTDIR\"..."
aws s3 cp "$S3_PREFIX$DIRECTORY" "$ROOTDIR" --recursive

# hardhat.config.tsを最新に更新
mv $ROOTDIR/hardhat.config_bk.ts $ROOTDIR/hardhat.config.ts

# 2.BESUのプライベートIPを設定する
BESU_IP=$(aws ec2 describe-instances --filters "Name=tag:Name,Values=${BESU_NAMESPACE}" --query 'Reservations[*].Instances[*].PrivateIpAddress[]' --region ap-northeast-1 --output json | jq -r '.[0]')

echo $BESU_IP
export CODEBUILD_PROVIDER="http://${BESU_IP}:8451"
echo $CODEBUILD_PROVIDER
RESULT=$(curl -v ${CODEBUILD_PROVIDER}/liveness)
echo $RESULT
if [[ "$RESULT" == *"UP"* ]]; then
  # 3.バックアップを実行する
  ${ROOTDIR}/scripts/backup-restore/backup.sh ${NETWORK}

  # TODO 4.バックアップファイルの整合性チェック
  # 5.バックアップファイルのアップロード
  BACKUP_FILES="$ROOTDIR/scripts/backup-restore/backupfiles/$NETWORK"
  S3_BACKUP_RESTORE_PREFIX="s3://$BACKUP_RESTORE_DATA_S3/$BACKUP_ZONE_ID"
  aws s3 cp  "$BACKUP_FILES" "$S3_BACKUP_RESTORE_PREFIX" --recursive
else
    echo "接続できません。"
    exit 1
fi

message "success" "Deployment process completed successfully."