import * as fs from 'fs'
import path from 'path'
import { message } from '../../../common/utils'

// スクリプト名の取得
const scriptName = path.basename(__filename)

/**
 * 指定されたネットワークのJSONから、アドレス部分を取得する
 * @param network ネットワーク名
 * @param jsonName JSON名
 */
export function getExtractBridgeAddress(network: string, jsonName: string): string {
  const filePath = path.join('deployments', network, `${jsonName}.json`)

  try {
    const rawData = fs.readFileSync(filePath, 'utf-8')
    const json = JSON.parse(rawData)

    if (!json.address) {
      console.error(`Error: "address" field not found in ${filePath}`)
      process.exit(1)
    }

    return json.address
  } catch (err) {
    console.error(`Failed to read or parse ${filePath}:`, err)
    process.exit(1)
  }
}

// 直接実行用
if (require.main === module) {
  // 引数チェック
  const network = process.argv[2]
  const jsonName = process.argv[3]

  if (!network || !jsonName) {
    message('err', 'Please specify the NETWORK as the first argument.')
    console.log(`npx ts-node bin/main/functions/helpers/${scriptName} [NETWORK name] [JSON name]`)
    process.exit(1)
  }

  getExtractBridgeAddress(network, jsonName)
}
