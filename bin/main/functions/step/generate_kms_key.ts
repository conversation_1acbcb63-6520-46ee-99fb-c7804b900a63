import { execSync } from 'child_process'
import fs from 'fs'
import path from 'path'
import { loadAwsProf } from '../../../common/load_aws_prof'
import { loadEnvVars } from '../../../common/load_env_vars'
import { menu, message, choice, ROOTDIR } from '../../../common/utils'

/**
 * hardhatのlist-networksコマンドでネットワーク一覧から、
 * ユーザーに対象ネットワークを対話式CLIで選択させる
 * @returns ユーザーが選択したネットワーク
 */
async function selectNetwork(): Promise<string> {
  const args = process.argv.slice(2)
  if (args.length > 0) {
    return args[0]
  }

  const stdout = execSync('npx hardhat list-networks', { encoding: 'utf8' })
  const networks = stdout.trim().split('\n')

  const choice = await menu('Select the network to create kms key:', networks)
  if (choice == null) {
    message('err', 'Invalid selection. Please try again.')
    process.exit(1)
  }

  return choice
}

/**
 * 指定環境用のKMSキーを作成する
 * @param network 環境名
 */
export async function generateKmsKey(network?: string) {
  try {
    // 環境が指定されていない場合、ユーザーに一覧から選択させる
    if (!network) {
      network = await selectNetwork()
      process.env.NETWORK = network
    }
    message('info', `Using provided network: ${network}`)

    // AWS_PROFILEの設定
    const profile = loadAwsProf(network)
    message('info', `AWS_PROFILE is set to "${profile}".`)
    await choice(`Do you want to proceed with the KMS key creation using this "${profile}"?`)

    // 環境変数の読み出しとエクスポート
    loadEnvVars(profile)

    const region = process.env.REGION
    const keyAlias = process.env.KEY_ALIAS || ''

    if (!region) {
      throw new Error('Missing REGION in environment.')
    }

    // エイリアスがすでに存在するか確認
    let keyId = execSync(
      `aws kms list-aliases --query "Aliases[?AliasName=='alias/${keyAlias}'].TargetKeyId" --output text --region ${region}`,
      { encoding: 'utf8' },
    ).trim()

    // 既に存在する場合はそのキーを使用する
    if (keyId) {
      console.log(`KMS key with alias '${keyAlias}' already exists. Using existing key ID: ${keyId}`)
      // 存在しない場合は新規作成する
    } else {
      console.log(`Creating a new KMS key in region ${region}...`)
      keyId = execSync(
        `aws kms create-key --description "KMS key for contract deployment" --key-usage SIGN_VERIFY --key-spec ECC_SECG_P256K1 --region ${region} --query KeyMetadata.KeyId --output text`,
        { encoding: 'utf8' },
      ).trim()

      if (!keyId) {
        console.error('Failed to create KMS key.')
        process.exit(1)
      }

      console.log(`KMS key created with ID: ${keyId}`)

      // キーにエイリアスを設定
      console.log('Creating alias for the KMS key...')
      execSync(`aws kms create-alias --alias-name "alias/${keyAlias}" --target-key-id "${keyId}" --region ${region}`, {
        stdio: 'inherit',
      })
      console.log(`Alias created: alias/${keyAlias}`)
    }

    // KMSキーIDを.kmsファイルに保存
    console.log('Saving KMS Key ID to .kms ...')
    const kmsFile = path.join(ROOTDIR, '.kms')
    const line = network.includes('Fin') ? `KMS_KEY_ID_FIN=${keyId}\n` : `KMS_KEY_ID_BIZ=${keyId}\n`
    fs.writeFileSync(kmsFile, line)
    console.log(`KMS key setup complete. Key ID: ${keyId}`)
  } catch (err) {
    message('err', `An unexpected error occurred: ${(err as Error).message}`)
    process.exit(1)
  }
}

// 直接実行用
if (require.main === module) {
  const network = process.argv[2]

  generateKmsKey(network).catch((err) => {
    console.error('[ERROR]', err)
    process.exit(1)
  })
}
