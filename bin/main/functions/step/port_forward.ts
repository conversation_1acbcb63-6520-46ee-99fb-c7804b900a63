import { execSync, spawn } from 'child_process'
import path from 'path'
import axios from 'axios'
import { loadAwsProf } from '../../../common/load_aws_prof'
import { loadEnvVars } from '../../../common/load_env_vars'
import { message } from '../../../common/utils'

// スクリプト名の取得
const scriptName = path.basename(__filename)

/**
 * 指定環境にポートフォワードする
 * @param network 環境名
 */
export async function portForward(network: string) {
  try {
    // AWS_PROFILEの設定
    const profile = loadAwsProf(network)

    // 環境変数の読み出しとエクスポート
    loadEnvVars(profile)

    // ローカル環境向けのリリースの場合、ポートフォワードはスキップする
    if (network.includes('local')) {
      message('info', `Skipping ${scriptName} for local environments.`)
      process.exit(0)
    }

    // PROVIDER 確認とポート抽出
    const provider = process.env.PROVIDER
    if (!provider) {
      message('err', 'PROVIDER environment variable is not set.')
      process.exit(1)
    }
    const providerParts = provider.split(':')
    const providerPort = providerParts[2]
    if (!providerPort) {
      message('err', 'Failed to extract port number from PROVIDER environment variable.')
      process.exit(1)
    }

    // BESUインスタンスからインスタンスIDを取得
    const BESUInstance = process.env.BESU_INSTANCE
    if (!BESUInstance) {
      message('err', 'BESU_INSTANCE environment variable is not set.')
      process.exit(1)
    }

    let instanceId = ''
    const result = execSync(
      `aws ec2 describe-instances --filters "Name=tag:Name,Values=${BESUInstance}" --query "Reservations[].Instances[].InstanceId"`,
      {
        encoding: 'utf-8',
      },
    )
    const match = result.match(/"?(i-[a-zA-Z0-9]+)"?/)
    instanceId = match?.[1] ?? ''

    if (!instanceId) {
      message('err', 'instance ID Not found.')
      process.exit(1)
    }

    // ポートフォワード開始
    message('info', 'Starting port forwarding to BESU VALIDATOR...')
    spawn(
      'aws',
      [
        'ssm',
        'start-session',
        '--target',
        instanceId.substring(0, 19),
        '--document-name',
        'AWS-StartPortForwardingSession',
        '--parameters',
        `portNumber=8451,localPortNumber=${providerPort}`,
      ],
      {
        detached: true,
        stdio: 'inherit',
      },
    ).unref()

    // ポートフォワード接続待機
    message('info', 'Waiting for port forwarding to be established...')

    let success = false
    const wait = async () => {
      for (let i = 0; i < 30; i++) {
        try {
          const res = await axios.get(`${provider}/liveness`)
          if (res.data.status === 'UP') {
            success = true
            break
          }
        } catch (_) {
          // ignore
        }
        await new Promise((r) => setTimeout(r, 1000))
      }
    }

    wait()
      .then(() => {
        if (success) {
          message('success', 'Port forwarding established successfully.')
          process.exit(0)
        } else {
          message('err', 'Failed to establish port forwarding after 30 seconds.')
          process.exit(1)
        }
      })
      .catch((err) => {
        message('err', `Unexpected error while waiting: ${err}`)
        process.exit(1)
      })
  } catch (err) {
    message('err', `An unexpected error occurred: ${(err as Error).message}`)
    process.exit(1)
  }
}

// 直接実行用
if (require.main === module) {
  // 引数チェック
  const network = process.argv[2]
  if (!network) {
    message('err', 'Please specify the NETWORK as the first argument.')
    console.log(`npx ts-node bin/main/functions/step/${scriptName} [NETWORK name]`)
    process.exit(1)
  }

  portForward(network).catch((err) => {
    console.error('[ERROR]', err)
    process.exit(1)
  })
}
