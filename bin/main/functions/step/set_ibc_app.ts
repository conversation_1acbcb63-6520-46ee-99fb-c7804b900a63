import path from 'path'
import { setIBCApp } from '../../../../tools/setIBCApp'
import { message, IBCAPP_TOKEN_TRANSFER, IBCAPP_ACCOUNT_SYNC, IBCAPP_BALANCE_SYNC } from '../../../common/utils'
import { getExtractBridgeAddress } from '../helpers/get_extract_bridge_address'

// スクリプト名の取得
const scriptName = path.basename(__filename)

/**
 * IBCの設定を行う
 * @param network 環境名
 */
export async function runSetIBCApp(network: string) {
  try {
    if (!network) {
      message('err', 'NETWORK environment variable is not set.')
      process.exit(1)
    }

    message('info', `Starting set ibc app for network: ${network}`)

    const ibcAppAddrTokenTransfer = getExtractBridgeAddress(network, IBCAPP_TOKEN_TRANSFER)
    const ibcAppAddrAccountSync = getExtractBridgeAddress(network, IBCAPP_ACCOUNT_SYNC)
    const ibcAppAddrBalanceSync = getExtractBridgeAddress(network, IBCAPP_BALANCE_SYNC)

    console.log('以下のコントラクトアドレスをBridgeコントラクトとしてメインコントラクトに登録します')
    console.log(`ADDR_TOKEN_TRANSFER=${ibcAppAddrTokenTransfer}`)
    console.log(`ADDR_ACCOUNT_SYNC=${ibcAppAddrAccountSync}`)
    console.log(`ADDR_BALANCE_SYNC=${ibcAppAddrBalanceSync}`)

    await setIBCApp(network, ibcAppAddrTokenTransfer, IBCAPP_TOKEN_TRANSFER)
    await setIBCApp(network, ibcAppAddrAccountSync, IBCAPP_ACCOUNT_SYNC)
    await setIBCApp(network, ibcAppAddrBalanceSync, IBCAPP_BALANCE_SYNC)

    message('success', `Registration for Bridge Contracts to Main Contracts: ${network} completed`)
  } catch (err) {
    message('err', `An unexpected error occurred: ${(err as Error).message}`)
    process.exit(1)
  }
}

// 直接実行用
if (require.main === module) {
  // 引数チェック
  const network = process.argv[2]
  if (!network) {
    message('err', 'Please specify the NETWORK as the first argument.')
    console.log(`npx ts-node bin/main/functions/step/${scriptName} [NETWORK name]`)
    process.exit(1)
  }

  runSetIBCApp(network).catch((err) => {
    console.error('[ERROR]', err)
    process.exit(1)
  })
}
