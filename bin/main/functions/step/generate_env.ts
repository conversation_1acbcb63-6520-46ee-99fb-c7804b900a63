import { execSync } from 'child_process'
import fs from 'fs'
import path from 'path'
import { loadAwsProf } from '../../../common/load_aws_prof'
import { loadEnvVars } from '../../../common/load_env_vars'
import { message, ENV_FILE_PATH } from '../../../common/utils'

// スクリプト名の取得
const scriptName = path.basename(__filename)

/**
 * envファイル作成
 * @param network 対象の環境名
 */
export async function generateEnv(network: string) {
  try {
    // AWS_PROFILEの設定
    const profile = loadAwsProf(network)

    // 環境変数の読み出しとエクスポート
    loadEnvVars(profile)

    // KEY_ADMIN を生成
    const keyOutput = execSync('node ./tools/generateRandomHex.js').toString()
    const keyMatch = keyOutput.match(/privateKey= 0x([0-9a-fA-F]+)/)
    if (!keyMatch) {
      message('err', 'Failed to generate KEY_ADMIN')
      process.exit(1)
    }

    const envVars: Record<string, string | undefined> = {
      KEY_ADMIN: keyMatch[1],
      NETWORK_ID: process.env.NETWORK_ID,
      BESU_NAMESPACE: process.env.BESU_NAMESPACE,
      PROVIDER: process.env.PROVIDER,
      NETWORK: process.env.NETWORK,
      ZONE_ID: process.env.ZONE_ID,
    }

    // .envファイルから古い変数を削除
    removeKeysFromFile(envVars, ENV_FILE_PATH)

    // .envファイルに変数を追加
    appendKeysToFile(envVars, ENV_FILE_PATH)
  } catch (err) {
    message('err', `An unexpected error occurred: ${(err as Error).message}`)
    process.exit(1)
  }
}

/**
 * 指定したファイルから指定したキーワードを削除する
 * @param keys キーワードの配列
 * @param filePath ファイルパス
 */
function removeKeysFromFile(envVars: Record<string, string | undefined>, filePath: string) {
  if (!fs.existsSync(filePath)) {
    message('err', '.env is not found')
    process.exit(1)
  }

  let content = fs.readFileSync(filePath, 'utf-8')
  Object.entries(envVars).forEach((envVar) => {
    const regex = new RegExp(`^${envVar[0]}=.*$`, 'gm')
    content = content.replace(regex, '')
  })

  // 空行を整理
  content = content
    .split('\n')
    .filter((line) => line.trim() !== '')
    .join('\n')
    .trimEnd()
  fs.writeFileSync(filePath, content)
}

/**
 * 指定したファイルに指定した環境変数を追記する
 * @param envVars 環境変数のキーと値のペア
 * @param filePath ファイルパス
 */
function appendKeysToFile(envVars: Record<string, string | undefined>, filePath: string) {
  if (!fs.existsSync(filePath)) {
    message('err', '.env is not found')
    process.exit(1)
  }

  Object.entries(envVars).forEach(([key, value]) => {
    const safeValue = value ?? ''
    fs.appendFileSync(filePath, `${key}=${safeValue}\n`)
  })
}

// 直接実行用
if (require.main === module) {
  // 引数チェック
  const network = process.argv[2]
  if (!network) {
    message('err', 'Please specify the NETWORK as the first argument.')
    console.log(`npx ts-node bin/main/functions/step/${scriptName} [NETWORK name]`)
    process.exit(1)
  }

  generateEnv(network).catch((err) => {
    console.error('[ERROR]', err)
    process.exit(1)
  })
}
