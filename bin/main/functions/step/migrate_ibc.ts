import { execSync } from 'child_process'
import fs from 'fs'
import path from 'path'
import { loadAwsProf } from '../../../common/load_aws_prof'
import { loadEnvVars } from '../../../common/load_env_vars'
import { message, choice, ROOTDIR } from '../../../common/utils'

// スクリプト名の取得
const scriptName = path.basename(__filename)

/**
 * IBCコントラクトのデプロイを行う
 * @param network 環境名
 */
export async function migrateIbc(network: string) {
  try {
    if (!network) {
      message('err', 'NETWORK environment variable is not set.')
      process.exit(1)
    }

    // AWS_PROFILEの設定
    const profile = loadAwsProf(network)

    // 環境変数の読み出しとエクスポート
    loadEnvVars(profile)

    message('info', `Starting deployment for network: ${network}`)

    // アドレスを環境変数に設定
    process.env.IBC_TOKEN_ADDRESS = readAddress('IBCToken', network)
    process.env.VALIDATOR_ADDRESS = readAddress('Validator', network)
    process.env.ACCOUNT_ADDRESS = readAddress('Account', network)
    process.env.ACCESS_CTRL_ADDRESS = readAddress('AccessCtrl', network)
    process.env.BUSINESS_ZONE_ACCOUNT_ADDRESS = readAddress('BusinessZoneAccount', network)
    process.env.PROVIDER_ADDRESS = readAddress('Provider', network)

    // デプロイ前確認
    console.log('デプロイ対象は以下環境で間違いないでしょうか？')
    console.log(`NETWORK=${network}`)
    console.log(`PROVIDER=${process.env.PROVIDER}`)
    console.log(`NETWORK_ID=${process.env.NETWORK_ID}`)
    console.log(`IBC_TOKEN_ADDRESS=${process.env.IBC_TOKEN_ADDRESS}`)
    console.log(`VALIDATOR_ADDRESS=${process.env.VALIDATOR_ADDRESS}`)
    console.log(`ACCOUNT_ADDRESS=${process.env.ACCOUNT_ADDRESS}`)
    console.log(`ACCESS_CTRL_ADDRESS=${process.env.ACCESS_CTRL_ADDRESS}`)
    console.log(`BUSINESS_ZONE_ACCOUNT_ADDRESS=${process.env.BUSINESS_ZONE_ACCOUNT_ADDRESS}`)
    console.log(`PROVIDER_ADDRESS=${process.env.PROVIDER_ADDRESS}`)
    await choice('以上で間違いなければyで進んでください')

    // デプロイコマンド実行
    const zoneId = process.env.ZONE_ID
    let DEPLOY_CMD: string

    if (zoneId && /^3\d{3}$/.test(zoneId) && Number(zoneId) >= 3000 && Number(zoneId) <= 3999) {
      DEPLOY_CMD = `npx hardhat deploy --network ${network} --tags ibc-contracts`
    } else {
      message('err', `Invalid ZONE_ID: ${zoneId}. Please set ZONE_ID to 3000 ~ 3999.`)
      process.exit(1)
    }

    execSync(DEPLOY_CMD, { stdio: 'inherit' })
    message('success', `Deployment for network: ${network} completed`)
  } catch (err) {
    message('err', `An unexpected error occurred: ${(err as Error).message}`)
    process.exit(1)
  }
}

/**
 * 指定したコントラクトのアドレスを取得する
 * @param contractName コントラクト名
 * @returns アドレス
 */
function readAddress(contractName: string, network: string): string {
  const filePath = path.join(ROOTDIR, 's3-restore/deployments', network, `${contractName}.json`)
  const data = JSON.parse(fs.readFileSync(filePath, 'utf-8'))
  return data.address
}

// 直接実行用
if (require.main === module) {
  // 引数チェック
  const network = process.argv[2]
  if (!network) {
    message('err', 'Please specify the NETWORK as the first argument.')
    console.log(`npx ts-node bin/main/functions/step/${scriptName} [NETWORK name]`)
    process.exit(1)
  }

  migrateIbc(network).catch((err) => {
    console.error('[ERROR]', err)
    process.exit(1)
  })
}
