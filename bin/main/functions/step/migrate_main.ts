import { execSync } from 'child_process'
import path from 'path'
import { loadAwsProf } from '../../../common/load_aws_prof'
import { loadEnvVars } from '../../../common/load_env_vars'
import { message } from '../../../common/utils'

// 定数定義
const MAIN_ZONE_ID = '3000'
const ZONE_ID_MIN = 3000
const ZONE_ID_MAX = 3999
const MAIN_CONTRACTS_TAG = 'main-contracts'
const RENEWABLE_TAG = 'renewable'

// スクリプト名の取得
const scriptName = path.basename(__filename)

/**
 * ZONE_IDがrenewableタグ用の有効な値かチェック
 * 元のシェルスクリプトのパターン: 3001 | [3][0-9][0-9][1-9] に対応
 * @param zoneId チェック対象のZONE_ID
 * @returns 有効な場合true
 */
function isValidRenewableZoneId(zoneId: string): boolean {
  // 3001の場合
  if (zoneId === '3001') {
    return true
  }

  // [3][0-9][0-9][1-9] パターンをチェック
  // 3000-3999の範囲で、末尾が1-9（0以外）
  const match = zoneId.match(/^3(\d)(\d)([1-9])$/)
  return match !== null
}

/**
 * mainコントラクトのデプロイを行う
 * @param network 環境名
 */
export async function migrateMain(network: string): Promise<void> {
  try {
    if (!network) {
      message('err', 'NETWORK environment variable is not set.')
      process.exit(1)
    }

    // AWS_PROFILEの設定
    const profile = loadAwsProf(network)

    // 環境変数の読み出しとエクスポート
    loadEnvVars(profile)

    message('info', `Starting deployment for network: ${network}`)

    // ZONE_ID に応じてデプロイコマンドを作成
    const zoneId = process.env.ZONE_ID
    let DEPLOY_CMD: string

    if (!zoneId) {
      message('err', 'ZONE_ID environment variable is not set.')
      process.exit(1)
    }

    if (zoneId === MAIN_ZONE_ID) {
      DEPLOY_CMD = `npx hardhat deploy --network ${network} --tags ${MAIN_CONTRACTS_TAG}`
    } else if (isValidRenewableZoneId(zoneId)) {
      DEPLOY_CMD = `npx hardhat deploy --network ${network} --tags ${RENEWABLE_TAG}`
    } else {
      message('err', `Invalid ZONE_ID: ${zoneId}. Please set ZONE_ID to ${ZONE_ID_MIN} ~ ${ZONE_ID_MAX}.`)
      process.exit(1)
    }

    execSync(DEPLOY_CMD, { stdio: 'inherit' })
    message('success', `Deployment for network: ${network} completed`)
  } catch (err) {
    message('err', `Failed to hardhat deploy.`)
    console.error(err)
    process.exit(1)
  }
}

// 直接実行用
if (require.main === module) {
  // 引数チェック
  const network = process.argv[2]
  if (!network) {
    message('err', 'Please specify the NETWORK as the first argument.')
    console.log(`npx ts-node bin/main/functions/step/${scriptName} [NETWORK name]`)
    process.exit(1)
  }

  migrateMain(network).catch((err) => {
    console.error('[ERROR]', err)
    process.exit(1)
  })
}
