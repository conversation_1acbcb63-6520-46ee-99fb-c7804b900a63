#!/bin/bash

SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
SCRIPTNAME=$(basename "$0")
source $SCRIPTDIR/../common/utils.sh
ROOTDIR=$(cd $(dirname $BASH_SOURCE)/../..; pwd)

# Skip if NETWORK is local
if [[ "$NETWORK" == *"local"* ]]; then
    message "info" "Skipping $SCRIPTNAME for local environments."
    exit 0
fi

if [ $# -ne 1 ]; then
    message "err" "Please specify the NETWORK as the first argument."
    echo "./bin/main/$SCRIPTNAME [NETWORK name]"
    exit 1
fi

source $SCRIPTDIR/_load_aws_prof.sh "$1"
# Load environment variables
if [ -f ${SCRIPTDIR}/env/."$AWS_PROFILE" ]; then
  export $(cat ${SCRIPTDIR}/env/."$AWS_PROFILE" | grep -v "^#" | xargs)
else
  message "err" "Please specify an environment name that exists in ./bin/main/env/"
  exit 1
fi

# Determine S3 prefix based on the environment
S3_PREFIX="s3://$BACKUP/$ZONE_ID/contract/$NETWORK/"

message "info" "Fetching list of backed up directories from S3..."
DIRECTORIES=$(aws s3 ls $S3_PREFIX | awk '{if ($2 ~ /\/$/) print $2}')

if [ -z "$DIRECTORIES" ]; then
  message "err" "No backups found."
  exit 1
fi

# Display the directories and prompt for selection
menu "Select a backup directory to restore:" $DIRECTORIES
if [ -z "$CHOICE" ]; then
  message "err" "Invalid selection. Please try again."
  exit 1
fi

# Confirm the selection
choice "Are you sure you want to restore from \"$S3_PREFIX$CHOICE\" to \"$ROOTDIR\"?"

# Copy the selected directory from S3 to the local directory
message "info" "Restoring backup from \"$S3_PREFIX$CHOICE\" to \"$ROOTDIR\"..."
aws s3 cp "$S3_PREFIX$CHOICE" "$ROOTDIR" --recursive

message "success" "Backup restoration completed."
