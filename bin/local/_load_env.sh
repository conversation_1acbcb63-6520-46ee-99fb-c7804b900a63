#!/bin/bash
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)

set -a  # すべての変数をエクスポートする

if [ $# != 1 ]; then
  export NETWORK=`basename $(cd $(dirname ${BASH_SOURCE[0]}); pwd)`
else
  NETWORK=$1
fi

# privateKey.ts[0]~[4]を使用
KEY_PROV=59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d
KEY_VALID=5de4111afa1a4b94908f83103eb1f1706367c2e68ca870fc3fb9a804cdab365a
KEY_ISSUER=7c852118294e51e653712a81e05800f419141751be58f605c371e15141b007a6
KEY_ACCOUNT=47e179ec197488593b187f80a00eb0da91f1b9d0b13f8733639f19c30a34926a

STATUS_APPLYING="applying"
STATUS_ACTIVE="active"
STATUS_TERMINATING="terminating"
STATUS_TERMINATED="terminated"

REASON_CODE=1

MINT_AMOUNT=5000
BURN_AMOUNT=1000 
TRANSFER_AMOUNT=1000  
CHARGE_AMOUNT=1000
DISCHARGE_AMOUNT=500
ZERO_AMOUNT=0

TRACE_ID="test"

MISC_VALUE_1=""
MISC_VALUE_2=""
MEMO="test"

REGION="ap-northeast-1"
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test
KMS_ALIAS=admin

if [ $NETWORK == "localFin" ]; then
  PROVIDER="http://localhost:18451"
  KMS_ENDPOINT_URL=http://localhost:14566

  PROV_ID=1111
  PROV_NAME="provider"

  ZONE_ID=3000
  ZONE_NAME="finzone"
  BIZ_ZONE_ID=3001
  BIZ_ZONE_NAME="bizzone"

  ISSUER_ID=2221
  BANK_CODE=9999
  ISSUER_NAME="issuer"

  VALID_ID=8888
  VALID_NAME="validator"

  export ACCOUNT_ID_1=300
  export ACCOUNT_ID_2=301
  export ACCOUNT_ID_3=302
  export ACCOUNT_ID_4=303
  ACCOUNT_NAME_1="account1"
  ACCOUNT_NAME_2="account2"
  ACCOUNT_NAME_3="account3"
  ACCOUNT_NAME_4="account4"

  TOKEN_ID=3000
  TOKEN_NAME="token"
  TOKEN_PEGKIND="JPY"
  TOKEN_SYMBOL="symbol"
  TOKEN_DEPOSITED=false

else
  PROVIDER="http://localhost:28451"
  KMS_ENDPOINT_URL=http://localhost:24566

  PROV_ID=1112
  PROV_NAME="provider"
  ZONE_ID=3001
  ZONE_NAME="bizzone"
  FIN_ZONE_ID=3000

  ISSUER_ID=2222
  BANK_CODE=9999
  ISSUER_NAME="issuer"

  VALID_ID=8888
  VALID_NAME="validator"

  export ACCOUNT_ID_1=300
  export ACCOUNT_ID_2=301
  export ACCOUNT_ID_3=302
  export ACCOUNT_ID_4=303
  ACCOUNT_NAME_1="account1"
  ACCOUNT_NAME_2="account2"
  ACCOUNT_NAME_3="account3"
  ACCOUNT_NAME_4="account4"

  TOKEN_ID=3000
  TOKEN_NAME="token"
  TOKEN_PEGKIND="JPY"
  TOKEN_SYMBOL="symbol"
  TOKEN_DEPOSITED=false

  RENEWABLE_ID_1="1"
  RENEWABLE_ID_2="2"
  RENEWABLE_ID_3="3"
  METADATA_ID="test"
  METADATA_HASH="test"

  OFFSET=0
  LIMIT=100
  SORT_ORDER="asc"
fi

