#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)

source "${BIN_DIR}"/utils.sh

if [[ $# -eq 0 ]]; then
  export NETWORK=$(basename $(
    cd $(dirname $BASH_SOURCE)
    pwd
  ))
else
  export NETWORK=$1
fi

source "${BIN_DIR}"/_load_env.sh "$1"

pushd "${ROOT_DIR}" >/dev/null

message "info" "Start Deploying the contract for ${NETWORK}..."
# Check the value of ZONE_ID and set the appropriate tags
case "$ZONE_ID" in
  "3000")
    DEPLOY_CMD="npx hardhat deploy --network ${NETWORK} --tags main-contracts"
    ;;
  "3001")
    DEPLOY_CMD="npx hardhat deploy --network ${NETWORK} --tags renewable"
    ;;
  *)
    message "err" "Invalid ZONE_ID: ${ZONE_ID}. Please set ZONE_ID to 3000 ~ 3001."
    exit 1
    ;;
esac

$DEPLOY_CMD
if [ $? -eq 0 ]; then
  message "success" "Success deploying the contract for ${NETWORK}."
else
  message "err" "Failed Deploying the contract for ${NETWORK}..."
  exit 1
fi

popd >/dev/null
