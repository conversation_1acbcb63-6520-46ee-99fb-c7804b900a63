#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)

source "${BIN_DIR}"/utils.sh

if [[ $# -eq 0 ]]; then
  export NETWORK=$(basename $(
    cd $(dirname $BASH_SOURCE)
    pwd
  ))
else
  export NETWORK=$1
fi

source "${BIN_DIR}"/_load_env.sh "$1"

pushd "${ROOT_DIR}" >/dev/null

# Check if KMS_ENDPOINT_URL is set
if [ -z "$KMS_ENDPOINT_URL" ]; then
    message "err" "KMS_ENDPOINT_URL is not set. Please set KMS_ENDPOINT_URL and try again."
    exit 1
fi

# エイリアスがすでに存在するか確認
EXISTING_KEY_ID=$(aws --endpoint-url=$KMS_ENDPOINT_URL kms list-aliases --query "Aliases[?AliasName=='alias/$KEY_ALIAS'].TargetKeyId" --output text)

if [ -n "$EXISTING_KEY_ID" ]; then
    echo "KMS key with alias '$KEY_ALIAS' already exists. Using existing key ID: $EXISTING_KEY_ID"
    KEY_ID=$EXISTING_KEY_ID
else
    # KMSキーの作成
    echo "Creating a new KMS key in region $REGION and endpoint $KMS_ENDPOINT_URL..."
    KEY_ID=$(aws --endpoint-url=$KMS_ENDPOINT_URL kms create-key --description "KMS key for contract deployment" \
        --key-usage SIGN_VERIFY --key-spec ECC_SECG_P256K1 --region $REGION \
        --query KeyMetadata.KeyId --output text)

    # キーが作成されたか確認
    if [ -z "$KEY_ID" ]; then
        echo "Failed to create KMS key."
        exit 1
    fi

    echo "KMS key created with ID: $KEY_ID"

    # キーにエイリアスを設定
    echo "Creating alias for the KMS key..."
    aws --endpoint-url=$KMS_ENDPOINT_URL kms create-alias --alias-name "alias/$KEY_ALIAS" --target-key-id "$KEY_ID" --region $REGION

    echo "Alias created: alias/$KEY_ALIAS"
fi

# KMSキーIDを.kmsファイルに保存
echo "Saving KMS Key ID to .kms ..."
if [ $1 == "localFin" ]; then
    echo "KMS_KEY_ID_FIN=$KEY_ID" >> $ROOT_DIR/.kms
    export KMS_KEY_ID_FIN=$KEY_ID
    echo "KMS key setup complete. Key ID: $KMS_KEY_ID_FIN"
else
    echo "KMS_KEY_ID_BIZ=$KEY_ID" >> $ROOT_DIR/.kms
    export KMS_KEY_ID_BIZ=$KEY_ID
    echo "KMS key setup complete. Key ID: $KMS_KEY_ID_BIZ"
fi


