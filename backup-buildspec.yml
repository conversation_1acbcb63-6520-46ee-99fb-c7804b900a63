version: 0.2

env:
  shell: bash

phases:
  install:
    runtime-versions:
      nodejs: 18
    commands:
      - n 18.17.1
      - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
      - unzip awscliv2.zip
      - ./aws/install --bin-dir /root/.pyenv/shims --install-dir /usr/local/aws-cli --update
  pre_build:
    commands:
      - node -v
      - npm -v
      - npm install
  build:
    commands:
      - echo "PROJECT_ENV $PROJECT_ENV"
      - echo "Backup for zone $ZONE_ID"
      - bash ./bin/main/backup_codebuild.sh $PROJECT_ENV $ZONE_ID