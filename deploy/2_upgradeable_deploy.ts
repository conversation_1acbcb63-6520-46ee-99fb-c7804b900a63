/**
 * Hardhat Deployment Script
 * 1. ネットワーク: 'local'と'main'を'hardhat.config.ts'で設定
 * 2. ウォレット: 'local'の場合、'admin'と'deployer'はデフォルトで設定されるプライベートキーを利用する
 * 3. デプロイライブラリ: 'hardhat-deploy'を利用し、Proxyとして'OpenZeppelinTransparentProxy'を設定
 */
import { showDeployNetwork, showDeploySuccessWithMsg } from '@deploy/common/consoles'
import { deployContractWithSaveABI } from '@deploy/common/deployContractWithSaveABI'
import { getContractInstance } from '@deploy/common/getContractInstance'
import { handleTransaction } from '@deploy/common/handleTransaction'
import { kmsSignerProvider } from '@deploy/common/kmsSignerProvider'
import { saveChainId } from '@deploy/common/saveChainId'
import { ethers } from 'hardhat'
import { DeployFunction } from 'hardhat-deploy/types'

const func: DeployFunction = async () => {
  try {
    await saveChainId()
    console.log('.chainId file created')
  } catch (error) {
    console.error('.chainId file creation error:', error)
  }

  // ethers.js v6のgetContractFactoryを利用してdeployを行う
  // getContractFactory()の引数には送信者を設定するsigner, Link対象コントラクトを設定するlibrariesが存在する
  // Deployの順番は前後順番があるので注意する：(1)Library系(Errorなど) (2)ContractManager (3)Others
  showDeployNetwork({ title: '' })

  // SetContractで利用するContract情報を入れるMap
  const contractMap = new Map()
  const signer = kmsSignerProvider()

  // nonce reset code
  let currentNonce = await ethers.provider.getTransactionCount(await signer.getAddress())
  console.log(`Current nonce for deployer: ${currentNonce}`)

  const deployerAddress = await signer.getAddress()
  console.log(`Deployer address: ${deployerAddress}`)

  // Deploy ContractManager
  const contractManagerContract = await deployContractWithSaveABI({
    contractName: 'ContractManager',
    options: { signer },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy Error
  await deployContractWithSaveABI({
    contractName: 'Error',
    options: { signer },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy ProviderLib
  await deployContractWithSaveABI({
    contractName: 'ProviderLib',
    options: { signer },
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy IssuerLib
  await deployContractWithSaveABI({
    contractName: 'IssuerLib',
    options: { signer },
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy ValidatorLib
  await deployContractWithSaveABI({
    contractName: 'ValidatorLib',
    options: { signer },
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy TokenLib
  await deployContractWithSaveABI({
    contractName: 'TokenLib',
    options: { signer },
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy AccountLib
  await deployContractWithSaveABI({
    contractName: 'AccountLib',
    options: { signer },
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy FinancialZoneAccountLib
  await deployContractWithSaveABI({
    contractName: 'FinancialZoneAccountLib',
    options: { signer },
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy BusinessZoneAccountLib
  await deployContractWithSaveABI({
    contractName: 'BusinessZoneAccountLib',
    options: { signer },
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy RemigrationLib
  await deployContractWithSaveABI({
    contractName: 'RemigrationLib',
    options: { signer },
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Get Contract Instance
  const providerLibInstance = await getContractInstance({
    contractName: 'ProviderLib',
    contractAddress: contractMap.get('ProviderLib'),
    signer,
  })
  const issuerLibInstance = await getContractInstance({
    contractName: 'IssuerLib',
    contractAddress: contractMap.get('IssuerLib'),
    signer,
  })
  const validatorLibInstance = await getContractInstance({
    contractName: 'ValidatorLib',
    contractAddress: contractMap.get('ValidatorLib'),
    signer,
  })
  const accountLibInstance = await getContractInstance({
    contractName: 'AccountLib',
    contractAddress: contractMap.get('AccountLib'),
    signer,
  })
  const financialZoneAccountLibInstance = await getContractInstance({
    contractName: 'FinancialZoneAccountLib',
    contractAddress: contractMap.get('FinancialZoneAccountLib'),
    signer,
  })
  const businessZoneAccountLibInstance = await getContractInstance({
    contractName: 'BusinessZoneAccountLib',
    contractAddress: contractMap.get('BusinessZoneAccountLib'),
    signer,
  })
  const tokenLibInstance = await getContractInstance({
    contractName: 'TokenLib',
    contractAddress: contractMap.get('TokenLib'),
    signer,
  })
  const remigrationLibInstance = await getContractInstance({
    contractName: 'RemigrationLib',
    contractAddress: contractMap.get('RemigrationLib'),
    signer,
  })

  // Deploy AccessCtrl
  await deployContractWithSaveABI({
    contractName: 'AccessCtrl',
    options: {
      signer,
    },
    initialize: {
      args: [contractMap.get('ContractManager'), deployerAddress],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Deploy Provider
  await deployContractWithSaveABI({
    contractName: 'Provider',
    options: {
      signer,
      libraries: {
        ProviderLib: providerLibInstance,
        RemigrationLib: remigrationLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Deploy Issuer
  await deployContractWithSaveABI({
    contractName: 'Issuer',
    options: {
      signer,
      libraries: {
        IssuerLib: issuerLibInstance,
        RemigrationLib: remigrationLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Deploy Validator
  await deployContractWithSaveABI({
    contractName: 'Validator',
    options: {
      signer,
      libraries: {
        ValidatorLib: validatorLibInstance,
        RemigrationLib: remigrationLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Deploy Account
  await deployContractWithSaveABI({
    contractName: 'Account',
    options: {
      signer,
      libraries: {
        AccountLib: accountLibInstance,
        RemigrationLib: remigrationLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Deploy FinancialZoneAccount
  await deployContractWithSaveABI({
    contractName: 'FinancialZoneAccount',
    options: {
      signer,
      libraries: {
        FinancialZoneAccountLib: financialZoneAccountLibInstance,
        RemigrationLib: remigrationLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Deploy BusinessZoneAccount
  await deployContractWithSaveABI({
    contractName: 'BusinessZoneAccount',
    options: {
      signer,
      libraries: {
        BusinessZoneAccountLib: businessZoneAccountLibInstance,
        RemigrationLib: remigrationLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Deploy Token
  await deployContractWithSaveABI({
    contractName: 'Token',
    options: {
      signer,
      libraries: {
        TokenLib: tokenLibInstance,
        RemigrationLib: remigrationLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Deploy IBCToken
  await deployContractWithSaveABI({
    contractName: 'IBCToken',
    options: {
      signer,
      libraries: {
        TokenLib: tokenLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Deploy FinancialCheck
  await deployContractWithSaveABI({
    contractName: 'FinancialCheck',
    options: {
      signer,
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Deploy TransferProxy
  await deployContractWithSaveABI({
    contractName: 'TransferProxy',
    options: {
      signer,
    },
    initialize: {
      args: [contractMap.get('ContractManager'), contractMap.get('Token')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Deploy RemigrationRestore
  await deployContractWithSaveABI({
    contractName: 'RemigrationRestore',
    options: {
      signer,
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Deploy RemigrationBackup
  await deployContractWithSaveABI({
    contractName: 'RemigrationBackup',
    options: {
      signer,
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  console.log('*************************************')
  console.log(`* Set contract addresses to ContractManager *`)
  console.log('*************************************')
  console.log('Setting all contract addresses to ContractManager...')

  const deadline = (await Math.floor(Date.now() / 1000)) + 10
  const types = [
    'address',
    'address',
    'address',
    'address',
    'address',
    'address',
    'address',
    'address',
    'address',
    'address',
    'address',
    'uint256',
  ]
  const data = [
    contractMap.get('AccessCtrl'),
    contractMap.get('Provider'),
    contractMap.get('Issuer'),
    contractMap.get('Validator'),
    contractMap.get('Account'),
    contractMap.get('FinancialZoneAccount'),
    contractMap.get('BusinessZoneAccount'),
    contractMap.get('Token'),
    contractMap.get('IBCToken'),
    contractMap.get('FinancialCheck'),
    contractMap.get('TransferProxy'),
    deadline,
  ]

  const signatureBytes = await signer.sign(types, data)

  await handleTransaction({
    transaction: contractManagerContract.setContracts(
      {
        ctrlAddress: contractMap.get('AccessCtrl'),
        providerAddress: contractMap.get('Provider'),
        issuerAddress: contractMap.get('Issuer'),
        validatorAddress: contractMap.get('Validator'),
        accountAddress: contractMap.get('Account'),
        financialZoneAccountAddress: contractMap.get('FinancialZoneAccount'),
        businessZoneAccountAddress: contractMap.get('BusinessZoneAccount'),
        tokenAddress: contractMap.get('Token'),
        ibcTokenAddress: contractMap.get('IBCToken'),
        financialCheckAddress: contractMap.get('FinancialCheck'),
        transferProxyAddress: contractMap.get('TransferProxy'),
      },
      deadline,
      signatureBytes,
      { nonce: currentNonce++ },
    ),
  })

  // Contract set to manageContract情報をFormatしてPrintする
  showDeploySuccessWithMsg({
    deployerAddress,
    contractObj: {
      ContractManager: contractMap.get('ContractManager'),
      AccessCtrl: contractMap.get('AccessCtrl'),
      Provider: contractMap.get('Provider'),
      Issuer: contractMap.get('Issuer'),
      Validator: contractMap.get('Validator'),
      Account: contractMap.get('Account'),
      FinancialZoneAccount: contractMap.get('FinancialZoneAccount'),
      BusinessZoneAccount: contractMap.get('BusinessZoneAccount'),
      Token: contractMap.get('Token'),
      IBCToken: contractMap.get('IBCToken'),
      FinancialCheck: contractMap.get('FinancialCheck'),
      TransferProxy: contractMap.get('TransferProxy'),
      ErrorLib: contractMap.get('Error'),
      ProviderLib: contractMap.get('ProviderLib'),
      IssuerLib: contractMap.get('IssuerLib'),
      ValidatorLib: contractMap.get('ValidatorLib'),
      AccountLib: contractMap.get('AccountLib'),
      FinancialZoneAccountLib: contractMap.get('FinancialZoneAccountLib'),
      BusinessZoneAccountLib: contractMap.get('BusinessZoneAccountLib'),
      ADMIN: deployerAddress,
    },
  })

  // chainIdを保存
  await saveChainId()
}

export default func
func.tags = ['main-contracts']
