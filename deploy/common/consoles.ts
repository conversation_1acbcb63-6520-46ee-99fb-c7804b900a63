import { Addressable } from 'ethers'
import { network } from 'hardhat'

type ShowDeployNetworkProps = {
  title: string
}
export const showDeployNetwork = ({ title }: ShowDeployNetworkProps) => {
  console.log('*************************************')
  console.log(`* Deploying ${title} contracts to ${network.name} network *`)
  console.log('*************************************')
}

type ShowDeploySuccessWithMsgProps = {
  deployerAddress: string
  contractObj: { [key: string]: string | Addressable }
}
export const showDeploySuccessWithMsg = ({ deployerAddress, contractObj }: ShowDeploySuccessWithMsgProps) => {
  const entries = Object.entries(contractObj)
  const maxKeyLength = entries.reduce((max, [key]) => Math.max(max, key.length), 0)
  const contractLines = entries.map(([key, value]) => `${key.padEnd(maxKey<PERSON>ength)} = ${value}`).join('\n')

  const lines = [
    '-------------------------------------------------------',
    `date: ${new Date().toString()}`,
    `network: ${network.name}`,
    `chainId: ${network.config.chainId}`,
    `deployer: ${deployerAddress}`,
    '',
    contractLines,
    '-------------------------------------------------------',
  ]

  const msg = lines.join('\n')

  console.log('')
  console.log('All contracts deployed Successfully')
  console.log(msg)
  console.log('')
}
