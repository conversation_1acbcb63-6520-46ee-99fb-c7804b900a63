import { saveABI } from '@deploy/common/abiTools'
import '@nomicfoundation/hardhat-ethers'
import * as Tools from '@tasks/common/tools'
import { Addressable, Contract, Signer } from 'ethers'
import { ethers, network } from 'hardhat'
import 'hardhat-deploy'

export type DeployContractOptions = {
  signer?: Signer
  libraries?: { [libraryName: string]: Contract }
}

export type DeployContractProps = {
  contractName: string
  options?: DeployContractOptions
  initialize?: {
    args: unknown[] // Map<any, any>[] だが、安定性のためにunknown[]としている、any使うより良いと思う
  }
  deployArgs?: (string | Addressable)[]
  saveABIflag?: boolean
  contractMap?: Map<string, string>
  overrides?: {
    nonce?: number
  }
  initializeOverrides?: {
    nonce?: number
  }
}

export const deployContractWithSaveABI = async <TContract extends Contract>({
  contractName,
  options = {},
  initialize,
  deployArgs = [],
  saveABIflag = false,
  contractMap,
  overrides = {},
  initializeOverrides = {},
}: DeployContractProps): Promise<TContract> => {
  console.log(`Deploying ${contractName}...`)

  const factory = await ethers.getContractFactory(contractName, options)
  const contract = (await factory.deploy(...deployArgs, overrides)) as TContract

  console.log(`${contractName} deployed to: ${contract.target}`)

  await contract.waitForDeployment()
  await contract
    .deploymentTransaction()
    ?.wait()
    .then((res) => Tools.showEthersRes({ res }))

  if (initialize) {
    console.log(`Initializing ${contractName}...`)
    const initTx = await contract.initialize(...initialize.args, initializeOverrides)
    await initTx.wait().then((res) => Tools.showEthersRes({ res }))
  }

  if (saveABIflag) {
    await saveABI({ contractName, contractInstance: contract, networkName: network.name })
  }

  if (contractMap) {
    contractMap.set(contractName, contract.target as string)
  }

  return contract
}
