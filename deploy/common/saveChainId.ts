import fs from 'fs'
import path from 'path'
import { ethers, network } from 'hardhat'

// chainIdを保存
export const saveChainId = async () => {
  try {
    const { chainId } = await ethers.provider.getNetwork()

    const deploymentDir = path.join(__dirname, `../../deployments/${network.name}`)
    const chainIdFilePath = path.join(deploymentDir, '.chainId')

    if (!fs.existsSync(deploymentDir)) {
      fs.mkdirSync(deploymentDir, { recursive: true })
    }

    fs.writeFileSync(chainIdFilePath, chainId.toString(), 'utf-8')
    console.log(`Chain ID ${chainId} saved to ${chainIdFilePath}`)
  } catch (error) {
    console.error('Failed to save chain ID:', error)
  }
}
