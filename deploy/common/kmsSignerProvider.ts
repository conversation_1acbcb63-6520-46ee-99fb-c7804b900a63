import { AwsKmsSigner, EthersAwsKmsSignerConfig } from '@tools/common/awsKmsSigner'
import { ethers, network } from 'hardhat'
import { envVers } from '@/envVers'

export const kmsSignerProvider = (): AwsKmsSigner => {
  const provider = new ethers.JsonRpcProvider(envVers.provider.url)
  const kmsConfig: EthersAwsKmsSignerConfig = {
    credentials: {
      accessKeyId: envVers.aws.credentials.accessKeyId,
      secretAccessKey: envVers.aws.credentials.secretAccessKey,
      ...(network.name.includes('main') && {
        sessionToken: envVers.aws.credentials.sessionToken,
      }),
    },
    region: envVers.aws.region,
    keyId: network.name.includes('Fin') ? envVers.kms.keys.fin : envVers.kms.keys.biz,
    ...(network.name.includes('local') && {
      endpoint: envVers.kms.endpoint,
    }),
  }

  return new AwsKmsSigner(kmsConfig, provider)
}
