import { AwsKmsSigner } from '@tools/common/awsKmsSigner'
import { Contract } from 'ethers'
import { ethers } from 'hardhat'

type GetContractInstanceProps = {
  contractName: string
  contractAddress: string | unknown
  signer: AwsKmsSigner
}
export const getContractInstance = async ({
  contractName,
  contractAddress,
  signer,
}: GetContractInstanceProps): Promise<Contract> => {
  if (!contractAddress || typeof contractAddress !== 'string') {
    throw new Error(`Invalid contract address for ${contractName}`)
  }
  return await ethers.getContractAt(contractName, contractAddress, signer)
}
