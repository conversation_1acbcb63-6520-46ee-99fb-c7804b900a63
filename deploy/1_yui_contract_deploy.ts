/**
 * <PERSON><PERSON> Contracts Deployment Script
 * 1. ネットワーク: 'local'と'main'を'hardhat.config.ts'で設定
 * 2. ウォレット: 'local'の場合、'admin'と'deployer'はデフォルトで設定されるプライベートキーを利用する
 * 3. デプロイライブラリ: 'hardhat-deploy'を利用し、Proxyとして'OpenZeppelinTransparentProxy'を設定
 */
import { showDeployNetwork, showDeploySuccessWithMsg } from '@deploy/common/consoles'
import { deployContractWithSaveABI } from '@deploy/common/deployContractWithSaveABI'
import { handleTransaction } from '@deploy/common/handleTransaction'
import { kmsSignerProvider } from '@deploy/common/kmsSignerProvider'
import { saveChainId } from '@deploy/common/saveChainId'
import '@nomicfoundation/hardhat-ethers'
import 'hardhat-deploy'
import { ethers } from 'hardhat'
import { DeployFunction } from 'hardhat-deploy/types'

const func: DeployFunction = async () => {
  showDeployNetwork({ title: 'IBC' })

  const signer = kmsSignerProvider()
  const deployerAddress = await signer.getAddress()
  console.log(`Deployer Address: ${deployerAddress}`)

  // Initialize nonce management
  let currentNonce = await ethers.provider.getTransactionCount(deployerAddress)
  console.log(`Current nonce for deployer: ${currentNonce}`)

  // Deploy IBCConnectionLib
  await deployContractWithSaveABI({
    contractName: 'IBCConnectionLib',
    options: { signer },
    overrides: { nonce: currentNonce++ },
  })

  // Deploy IBCChannelLib
  await deployContractWithSaveABI({
    contractName: 'IBCChannelLib',
    options: { signer },
    overrides: { nonce: currentNonce++ },
  })

  // Deploy IBCClient
  const ibcClient = await deployContractWithSaveABI({
    contractName: 'IBCClient',
    options: { signer },
    overrides: { nonce: currentNonce++ },
  })

  // Deploy IBCConnectionHandshake
  const ibcConnectionSelfStateNoValidation = await deployContractWithSaveABI({
    contractName: 'IBCConnectionSelfStateNoValidation',
    options: { signer: signer },
    overrides: { nonce: currentNonce++ },
  })

  // Deploy IBCConnectionHandshake
  const ibcChannelHandshake = await deployContractWithSaveABI({
    contractName: 'IBCChannelHandshake',
    options: { signer: signer },
    overrides: { nonce: currentNonce++ },
  })

  // Deploy IBCChannelPacketSendRecv
  const ibcChannelPacketSendRecv = await deployContractWithSaveABI({
    contractName: 'IBCChannelPacketSendRecv',
    options: { signer: signer },
    overrides: { nonce: currentNonce++ },
  })

  // Deploy IBCChannelPacketTimeout
  const ibcChannelPacketTimeout = await deployContractWithSaveABI({
    contractName: 'IBCChannelPacketTimeout',
    options: { signer: signer },
    overrides: { nonce: currentNonce++ },
  })

  // Deploy IBCHandler
  const ibcHandler = await deployContractWithSaveABI({
    contractName: 'OwnableIBCHandler',
    options: { signer },
    deployArgs: [
      ibcClient.target,
      ibcConnectionSelfStateNoValidation.target,
      ibcChannelHandshake.target,
      ibcChannelPacketSendRecv.target,
      ibcChannelPacketTimeout.target,
    ],
    saveABIflag: true,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy IBFT2Client
  const ibft2Client = await deployContractWithSaveABI({
    contractName: 'IBFT2Client',
    options: { signer },
    deployArgs: [ibcHandler.target],
    overrides: { nonce: currentNonce++ },
  })

  // Deploy IBCMockApp
  const ibcMockApp = await deployContractWithSaveABI({
    contractName: 'IBCMockApp',
    options: { signer },
    deployArgs: [ibcHandler.target],
    overrides: { nonce: currentNonce++ },
  })

  console.log('********* Registering Client *********')
  await handleTransaction({
    transaction: ibcHandler.registerClient('hb-ibft2', ibft2Client.target, { nonce: currentNonce++ }),
  })

  console.log('********* Binding Port to MockApp *********')
  await handleTransaction({
    transaction: ibcHandler.bindPort('mockapp', ibcMockApp.target, { nonce: currentNonce++ }),
  })

  // Deploy情報をFormatしてPrintする
  showDeploySuccessWithMsg({
    deployerAddress,
    contractObj: {
      OwnableIBCHandler: ibcHandler.target,
      Ibft2Client: ibft2Client.target,
      IbcMockApp: ibcMockApp.target,
    },
  })
  // chainIdを保存
  await saveChainId()
}

export default func
func.tags = ['yui-contracts']
