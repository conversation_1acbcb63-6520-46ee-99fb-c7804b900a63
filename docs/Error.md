# Solidity API

## Error

_Error文字列_

### ACTRL_SIG_TIMEOUT

```solidity
string ACTRL_SIG_TIMEOUT
```

_Error: 署名タイムアウト_

### ACTRL_BAD_ROLE

```solidity
string ACTRL_BAD_ROLE
```

_Error: roleが不正_

### ACTRL_BAD_SIG

```solidity
string ACTRL_BAD_SIG
```

_Error: 署名長が不正(署名の不正はtry-catchで捕捉できないため定義しない)_

### ACTRL_NOT_ROLE

```solidity
string ACTRL_NOT_ROLE
```

_Error: 処理を実行する権限が無い_

### ACTRL_INVALID_VAL

```solidity
string ACTRL_INVALID_VAL
```

_Error: 値が不正_

### ACTRL_NOT_ADMIN_ROLE

```solidity
string ACTRL_NOT_ADMIN_ROLE
```

_Error: 署名復元したアドレスがAdmin権限を持たない(hash不一致の可能性もある)_

### PROV_ID_EXIST

```solidity
string PROV_ID_EXIST
```

_Error: 登録済みプロバイダID_

### PROV_ID_NOT_EXIST

```solidity
string PROV_ID_NOT_EXIST
```

_Error: 未登録プロバイダID_

### PROV_ADDR_EXIST

```solidity
string PROV_ADDR_EXIST
```

_Error: 登録済みアドレス_

### PROV_DISABLED

```solidity
string PROV_DISABLED
```

_Error: 無効プロバイダ_

### PROV_CURRID_EXIST

```solidity
string PROV_CURRID_EXIST
```

_Error: 登録済みcurrID_

### NOT_PROVIDER_ID

```solidity
string NOT_PROVIDER_ID
```

_Error: ProviderIDではない_

### PROV_NOT_ROLE

```solidity
string PROV_NOT_ROLE
```

_Error: 処理を実行する権限が無い_

### PROV_NOT_EXIST

```solidity
string PROV_NOT_EXIST
```

_Error: プロバイダ未登録_

### ZONE_NOT_EXIST

```solidity
string ZONE_NOT_EXIST
```

_Error: プロバイダ未登録_

### NOT_PROVIDER_CONTRACT

```solidity
string NOT_PROVIDER_CONTRACT
```

_Error: 呼び出し元がProviderのコントラクトではない_

### PROV_INVALID_VAL

```solidity
string PROV_INVALID_VAL
```

_Error: 値が不正_

### PROV_NOT_ADMIN_ROLE

```solidity
string PROV_NOT_ADMIN_ROLE
```

_Error: 署名復元したアドレスがAdmin権限を持たない(hash不一致の可能性もある)_

### ISSUER_ID_EXIST

```solidity
string ISSUER_ID_EXIST
```

_Error: 登録済み発行者ID_

### ISSUER_ID_NOT_EXIST

```solidity
string ISSUER_ID_NOT_EXIST
```

_Error: 未登録発行者ID_

### ISSUER_ADDR_EXIST

```solidity
string ISSUER_ADDR_EXIST
```

_Error: 登録済みアドレス_

### ISSUER_DISABLED

```solidity
string ISSUER_DISABLED
```

_Error: 無効発行者_

### ISSUER_ACCOUNT_EXIST

```solidity
string ISSUER_ACCOUNT_EXIST
```

_Error: 登録済みユーザID_

### ISSUER_TOKEN_EXIST

```solidity
string ISSUER_TOKEN_EXIST
```

_Error: 登録済みトークンID_

### ISSUER_HAS_THIS_ACCOUNT_ID

```solidity
string ISSUER_HAS_THIS_ACCOUNT_ID
```

_Error: 登録済みAccountID_

### ITEMFLGS_INVALID_VAL

```solidity
string ITEMFLGS_INVALID_VAL
```

_Error: itemFlgs配列の要素数が不正_

### LIMITAMOUNT_INVALID_VAL

```solidity
string LIMITAMOUNT_INVALID_VAL
```

_Error: limitAmount配列の要素数が不正_

### ISSUER_NOT_ROLE

```solidity
string ISSUER_NOT_ROLE
```

_Error: 処理を実行する権限が無い_

### ISSUER_OUT_OF_INDEX

```solidity
string ISSUER_OUT_OF_INDEX
```

_Error: 範囲外を指定された_

### ISSUER_TOO_LARGE_LIMIT

```solidity
string ISSUER_TOO_LARGE_LIMIT
```

_Error: 一覧検索時のlimitとして最大取得件数を超える値が指定された_

### ISSUER_OFFSET_OUT_OF_INDEX

```solidity
string ISSUER_OFFSET_OUT_OF_INDEX
```

_Error: 一覧検索時のoffsetとして範囲外を指定された_

### NOT_ISSUER_CONTRACT

```solidity
string NOT_ISSUER_CONTRACT
```

_Error: 呼び出し元がIssuerのコントラクトではない_

### ISSUER_INVALID_VAL

```solidity
string ISSUER_INVALID_VAL
```

_Error: 値が不正_

### ISSUER_NOT_ADMIN_ROLE

```solidity
string ISSUER_NOT_ADMIN_ROLE
```

_Error: 署名復元したアドレスがAdmin権限を持たない(hash不一致の可能性もある)_

### ISSUER_EXCEED_REGISTER_LIMIT

```solidity
string ISSUER_EXCEED_REGISTER_LIMIT
```

_Error: 署名復元したアドレスがAdmin権限を持たない(hash不一致の可能性もある)_

### ACCOUNT_ID_EXIST

```solidity
string ACCOUNT_ID_EXIST
```

_Error: 登録済みアカウントID_

### ACCOUNT_ID_NOT_EXIST

```solidity
string ACCOUNT_ID_NOT_EXIST
```

_Error: 未登録アカウントID_

### ACCOUNT_ADDR_EXIST

```solidity
string ACCOUNT_ADDR_EXIST
```

_Error: 登録済みアドレス_

### ACCOUNT_DISABLED

```solidity
string ACCOUNT_DISABLED
```

_Error: 無効アカウント_

### OWNER_NOT_EXIST

```solidity
string OWNER_NOT_EXIST
```

_Error: ownerId未登録 approve用_

### SPENDER_NOT_EXIST

```solidity
string SPENDER_NOT_EXIST
```

_Error: spenerId未登録 approve用_

### ALLOWANCE_NOT_ENOUGH

```solidity
string ALLOWANCE_NOT_ENOUGH
```

_Error: Allowance額が送金額に届いていない際のエラー_

### BALANCE_NOT_ENOUGH

```solidity
string BALANCE_NOT_ENOUGH
```

_Error: balance残高不足_

### ACCOUNT_NOT_IDENTIFIED

```solidity
string ACCOUNT_NOT_IDENTIFIED
```

_Error: 本人確認登録なし_

### ACCOUNT_TERMINATED

```solidity
string ACCOUNT_TERMINATED
```

_Error: 解約済アカウント_

### ACCOUNT_BALANCE_EXIST

```solidity
string ACCOUNT_BALANCE_EXIST
```

_Error: 解約時に残高あり_

### ACCOUNT_NOT_FROZEN

```solidity
string ACCOUNT_NOT_FROZEN
```

_Error: アカウント解約時にアカウントが凍結状態でない_

### ACCOUNT_BALANCE_NOT_ZERO

```solidity
string ACCOUNT_BALANCE_NOT_ZERO
```

_Error: アカウント解約時にアカウントの残高が0でない_

### ACCOUNT_APPLYING_OPENING

```solidity
string ACCOUNT_APPLYING_OPENING
```

_Error: アカウント開設申込中_

### ACCOUNT_EXCEED_APPROVAL_LIMIT

```solidity
string ACCOUNT_EXCEED_APPROVAL_LIMIT
```

_Error: 許可額の設定値が上限を超えている_

### ACCOUNT_NOT_ROLE

```solidity
string ACCOUNT_NOT_ROLE
```

_Error: 処理を実行する権限が無い_

### ACCOUNT_OUT_OF_INDEX

```solidity
string ACCOUNT_OUT_OF_INDEX
```

_Error: 範囲外を指定された_

### ACCOUNT_PROV_NOT_ROLE

```solidity
string ACCOUNT_PROV_NOT_ROLE
```

_Error: 処理を実行する権限が無い_

### ACCOUNT_TOO_LARGE_LIMIT

```solidity
string ACCOUNT_TOO_LARGE_LIMIT
```

_Error: 一覧検索時のlimitとして最大取得件数を超える値が指定された_

### ACCOUNT_OFFSET_OUT_OF_INDEX

```solidity
string ACCOUNT_OFFSET_OUT_OF_INDEX
```

_Error: 一覧検索時のoffsetとして範囲外を指定された_

### ACCOUNT_INVALID_VAL

```solidity
string ACCOUNT_INVALID_VAL
```

_Error: 値が不正_

### ACCOUNT_OVERFLOW

```solidity
string ACCOUNT_OVERFLOW
```

_Error: 加算オーバーフロー_

### ACCOUNT_UNDERFLOW

```solidity
string ACCOUNT_UNDERFLOW
```

_Error: 減算オーバーフロー_

### ACCOUNT_EXCEED_REGISTER_LIMIT

```solidity
string ACCOUNT_EXCEED_REGISTER_LIMIT
```

_Error: アカウント登録限界数_

### ACCOUNT_BAD_SIG

```solidity
string ACCOUNT_BAD_SIG
```

_Error: Accountの署名が不正_

### ACCOUNT_INVALID_SIG

```solidity
string ACCOUNT_INVALID_SIG
```

_Error: パラメータが存在しない。_

### TOKEN_ID_EXIST

```solidity
string TOKEN_ID_EXIST
```

_Error: 登録済みトークンID_

### TOKEN_ID_NOT_EXIST

```solidity
string TOKEN_ID_NOT_EXIST
```

_Error: 未登録トークンID_

### TOKEN_ADDR_EXIST

```solidity
string TOKEN_ADDR_EXIST
```

_Error: 登録済みアドレス_

### TOKEN_DISABLED

```solidity
string TOKEN_DISABLED
```

_Error: 無効トークン_

### TOKEN_BALANCE_NOT_ENOUGH

```solidity
string TOKEN_BALANCE_NOT_ENOUGH
```

_Error: balance残高不足_

### TOKEN_ACCOUNT_DISABLED

```solidity
string TOKEN_ACCOUNT_DISABLED
```

_Error: Tokenでの無効ユーザ_

### TOKEN_ACCOUNT_UNIDENTIFIED

```solidity
string TOKEN_ACCOUNT_UNIDENTIFIED
```

_Error: Tokenでの本人未確認ユーザ_

### TOKEN_APPROVE

```solidity
string TOKEN_APPROVE
```

_Error: approve関係の失敗_

### TOKEN_ISSUER_UNKNOWN

```solidity
string TOKEN_ISSUER_UNKNOWN
```

_Error: トークンIDの発行者ではない_

### NOT_TOKEN_ID

```solidity
string NOT_TOKEN_ID
```

_Error: 登録されているトークンIDではない_

### TOKEN_NOT_EXIST

```solidity
string TOKEN_NOT_EXIST
```

_Error: 登録されているトークンIDではない_

### TOKEN_ZERO_AMOUNT

```solidity
string TOKEN_ZERO_AMOUNT
```

_Error: 送金金額が0_

### TOKEN_NOT_ROLE

```solidity
string TOKEN_NOT_ROLE
```

_Error: 処理を実行する権限が無い_

### TOKEN_OUT_OF_INDEX

```solidity
string TOKEN_OUT_OF_INDEX
```

_Error: 範囲外を指定された_

### TOKEN_ISSUER_NOT_ROLE

```solidity
string TOKEN_ISSUER_NOT_ROLE
```

_Error: 処理を実行する権限が無い(issuer)_

### TOKEN_CURR_NOT_ROLE

```solidity
string TOKEN_CURR_NOT_ROLE
```

_Error: 処理を実行する権限が無い(currency)_

### NOT_TOKEN_CONTRACT

```solidity
string NOT_TOKEN_CONTRACT
```

_Error: 呼び出し元がTokenコントラクトではない_

### NOT_ACCOUNT_CONTRACT

```solidity
string NOT_ACCOUNT_CONTRACT
```

_Error: 呼び出し元がAccountコントラクトではない_

### TOKEN_INVALID_VAL

```solidity
string TOKEN_INVALID_VAL
```

_Error: 値が不正_

### TOKEN_OVERFLOW

```solidity
string TOKEN_OVERFLOW
```

_Error: 加算オーバーフロー_

### TOKEN_UNDERFLOW

```solidity
string TOKEN_UNDERFLOW
```

_Error: 減算オーバーフロー_

### VALIDATOR_ID_EXIST

```solidity
string VALIDATOR_ID_EXIST
```

_Error: 登録済み検証者ID_

### VALIDATOR_ID_NOT_EXIST

```solidity
string VALIDATOR_ID_NOT_EXIST
```

_Error: 未登録検証者ID_

### VALIDATOR_ADDR_EXIST

```solidity
string VALIDATOR_ADDR_EXIST
```

_Error: 登録済みアドレス_

### VALIDATOR_DISABLED

```solidity
string VALIDATOR_DISABLED
```

_Error: 無効検証者_

### VALIDATOR_ACCOUNT_NOT_EXIST

```solidity
string VALIDATOR_ACCOUNT_NOT_EXIST
```

_Error: 検証者管理のアカウントIDが未登録_

### VALIDATOR_NOT_ROLE

```solidity
string VALIDATOR_NOT_ROLE
```

_Error: 処理を実行する権限が無い_

### VALIDATOR_OUT_OF_INDEX

```solidity
string VALIDATOR_OUT_OF_INDEX
```

_Error: 範囲外を指定された_

### VALIDATOR_TOO_LARGE_LIMIT

```solidity
string VALIDATOR_TOO_LARGE_LIMIT
```

_Error: 一覧検索時のlimitとして最大取得件数を超える値が指定された_

### VALIDATOR_OFFSET_OUT_OF_INDEX

```solidity
string VALIDATOR_OFFSET_OUT_OF_INDEX
```

_Error: 一覧検索時のoffsetとして範囲外を指定された_

### NOT_VALIDATOR_CONTRACT

```solidity
string NOT_VALIDATOR_CONTRACT
```

_Error: 呼び出し元がValidatorのコントラクトではない_

### VALIDATOR_INVALID_VAL

```solidity
string VALIDATOR_INVALID_VAL
```

_Error: 値が不正_

### VALIDATOR_NOT_ADMIN_ROLE

```solidity
string VALIDATOR_NOT_ADMIN_ROLE
```

_Error: 署名復元したアドレスがAdmin権限を持たない(hash不一致の可能性もある)_

### VALIDATOR_EXCEED_REGISTER_LIMIT

```solidity
string VALIDATOR_EXCEED_REGISTER_LIMIT
```

_Error: 署名復元したアドレスがAdmin権限を持たない(hash不一致の可能性もある)_

### FROM_TO_SAME

```solidity
string FROM_TO_SAME
```

_Error: FromAccountとToAccountが同じ。_

### IBC_SENDER_NOT_AUTH

```solidity
string IBC_SENDER_NOT_AUTH
```

_Error: msg.senderが許可されていない_

### IBC_PACKET_TIMED_OUT

```solidity
string IBC_PACKET_TIMED_OUT
```

_Error: パケットがタイムアウトした_

### IBC_PACKET_ALWAYS_RECV

```solidity
string IBC_PACKET_ALWAYS_RECV
```

_Error: パケットはすでに受信している_

### NOT_IBC_CONTRACT

```solidity
string NOT_IBC_CONTRACT
```

_Error: msg.senderが許可されていない_

### IBC_APP_JPYTT_ESCROW_NOT_REG

```solidity
string IBC_APP_JPYTT_ESCROW_NOT_REG
```

_Error: エスクローアカウントが登録されていない_

### IBC_APP_JPYTT_ESCROW_ALWAYS_REG

```solidity
string IBC_APP_JPYTT_ESCROW_ALWAYS_REG
```

_Error: エスクローアカウントがすでに登録されている_

### IBC_APP_JPYTT_EXTERNAL_CONTRACT_ERR

```solidity
string IBC_APP_JPYTT_EXTERNAL_CONTRACT_ERR
```

_Error: 外部コントラクトのエラー_

### IBC_INVALID_VAL

```solidity
string IBC_INVALID_VAL
```

_Error: 値が不正_

### IBC_NOT_ADMIN_ROLE

```solidity
string IBC_NOT_ADMIN_ROLE
```

_Error: 署名復元したアドレスがAdmin権限を持たない(hash不一致の可能性もある)_

### FIN_ZONE_OP_NOT_ALLOWED

```solidity
string FIN_ZONE_OP_NOT_ALLOWED
```

_Error: 操作ゾーンが不適切(TODO: コード体系見直し時に再度採番)_

### INVALID_ACCOUNT_STATUS

```solidity
string INVALID_ACCOUNT_STATUS
```

_Error: 設定するアカウントステータスが不適切(TODO: コード体系見直し時に再度採番)_

### NOT_ALLOWED_FROM_FIN_ZONE

```solidity
string NOT_ALLOWED_FROM_FIN_ZONE
```

_Error: パケット送信元のゾーンが不適切(TODO: コード体系見直し時に再度採番)_

### CUSTOM_CONTRACT_NOT_EXIST

```solidity
string CUSTOM_CONTRACT_NOT_EXIST
```

_Error: CustomContractのAddressが未登録である_

### CUSTOM_CONTRACT_EXIST

```solidity
string CUSTOM_CONTRACT_EXIST
```

_Error: CustomContractが既に登録されている_

### REGION_IS_EXIST

```solidity
string REGION_IS_EXIST
```

_Error: 登録済みRegion Id_

### REGION_INVALID_VAL

```solidity
string REGION_INVALID_VAL
```

_Error: 値が不正_

### NOT_FINANCIAL_ACCOUNT

```solidity
string NOT_FINANCIAL_ACCOUNT
```

_Error: FinancialAccountコントラクトからの呼び出しではない_

### NOT_FINANCIAL_TOKEN

```solidity
string NOT_FINANCIAL_TOKEN
```

_Error: FinancialTokenコントラクトからの呼び出しではない_

### LIMIT_AMOUNT_INVALID_COUNT

```solidity
string LIMIT_AMOUNT_INVALID_COUNT
```

_Error: limitAmountsが要素数設定されていない_

### ITEM_FLG_INVALID_COUNT

```solidity
string ITEM_FLG_INVALID_COUNT
```

_Error: itemFlgsが要素数設定されていない_

### NOT_INDUSTRY_REGION

```solidity
string NOT_INDUSTRY_REGION
```

_Error: 付加領域からの呼び出しではない_

### NOT_STREAM_ADDRESS

```solidity
string NOT_STREAM_ADDRESS
```

_Error: 登録済みRegion Id_

### EXCEEDED_DAILY_LIMIT

```solidity
string EXCEEDED_DAILY_LIMIT
```

_Error: DAILYの上限を超えている。_

### EXCEEDED_MINT_LIMIT

```solidity
string EXCEEDED_MINT_LIMIT
```

_Error: MINTの上限を超えている。_

### EXCEEDED_BURN_LIMIT

```solidity
string EXCEEDED_BURN_LIMIT
```

_Error: BURNの上限を超えている。_

### EXCEEDED_TRANSFER_LIMIT

```solidity
string EXCEEDED_TRANSFER_LIMIT
```

_Error: TRANSFERの上限を超えている。_

### EXCEEDED_CHARGE_LIMIT

```solidity
string EXCEEDED_CHARGE_LIMIT
```

_Error: TRANSFERの上限を超えている。_

### NOT_FINANCIAL_VALIDATOR

```solidity
string NOT_FINANCIAL_VALIDATOR
```

_Error: FinancialValidatorコントラクトからの呼び出しではない_

### FROM_TO_AMOUNT_NOT_SAME_COUNT

```solidity
string FROM_TO_AMOUNT_NOT_SAME_COUNT
```

_Error: fromAccount, toAccount, amountの配列の要素数不一致_

### RETOKEN_TOO_LARGE_LIMIT

```solidity
string RETOKEN_TOO_LARGE_LIMIT
```

_Error: 一覧検索時のlimitとして最大取得件数を超える値が指定された_

### RETOKEN_OFFSET_OUT_OF_INDEX

```solidity
string RETOKEN_OFFSET_OUT_OF_INDEX
```

_Error: 一覧検索時のoffsetとして範囲外を指定された_

### RETOKEN_EXCEED_REGISTER_LIMIT

```solidity
string RETOKEN_EXCEED_REGISTER_LIMIT
```

_Error: Token登録限界数_

### INVALID_ORGANIZATION_ID

```solidity
string INVALID_ORGANIZATION_ID
```

_Error: organizationId(issuerId/validatorId)が無効な値_

### INVALID_ACCOUNT_ID

```solidity
string INVALID_ACCOUNT_ID
```

_Error: accountIdが無効な値_

