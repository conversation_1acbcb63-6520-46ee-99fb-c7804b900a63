# 設計ドキュメント

このディレクトリには、プロジェクトの設計に関する重要なドキュメントが格納されています。

## ドキュメント一覧

### [Solidityコーディング規約](./solidity-coding-standards.md)
本プロジェクトでのSolidityコーディング規約について規定したドキュメントです。

**主な内容:**
- ディレクトリ・ファイル構成
- コメント記述方法（NatSpec形式）
- 命名規則
- 外部署名の実装方法
- 実装時の方針（外部コントラクト呼び出し、関数修飾子、アクセスコントロール）
- Stack Too Deepエラーの回避方法
- チェック項目（署名、ID値、enabled状態、登録・参照・計算）
- チェック関数一覧

**コードレビュー時の参照ポイント:**
- コーディングスタイルの統一性
- NatSpecコメントの記述
- 命名規則の遵守
- アクセス制御の適切な実装
- チェック関数の使用

### [コントラクトリアーキテクチャ - データ・ロジック分離についての全体設計書](./contract-rearchitecture-design.md)
コントラクトのデータ管理コントラクトとロジック管理コントラクトを分離する上での全体設計書です。

**主な内容:**
- Eternal Storage Patternの採用理由と利点
- 5種類のコントラクト分類（Storage/Logicインターフェース、Storage/Logicコントラクト、ライブラリ）
- 署名検証処理の詳細
- call関数とsend関数の使い分け
- アクセス制限の実装方法
- データモデル固有の注意点

**コードレビュー時の参照ポイント:**
- データとロジックの適切な分離
- アクセス制限の実装
- 署名検証処理の正確性
- ContractManagerを介した外部コントラクト呼び出し
- mappingとindex配列の同時管理

## コードレビュー時の活用方法

### 1. 事前準備
- レビュー対象のコントラクトがどの分類に該当するかを確認
- 該当する設計ドキュメントの関連セクションを事前に確認

### 2. レビュー観点
以下の観点でコードレビューを実施してください：

#### Solidityコーディング規約の遵守
- [ ] ファイル名とコントラクト名の一致
- [ ] SPDX-License-Identifierとpragmaの記述
- [ ] NatSpecコメントの記述（contract、external/public関数）
- [ ] 命名規則（storage変数、internal関数のアンダースコア）
- [ ] 外部署名の引数順序（deadline、signature）

#### アーキテクチャ設計の遵守
- [ ] データとロジックの適切な分離
- [ ] 適切なアクセス制限の実装
- [ ] ContractManagerを介した外部コントラクト呼び出し
- [ ] 署名検証処理の実装
- [ ] mappingとindex配列の同時管理

#### セキュリティ観点
- [ ] 適切な権限チェック（Admin、Role、Account署名）
- [ ] アクセス制限modifier の使用
- [ ] 入力値のバリデーション
- [ ] オーバーフロー・アンダーフローの考慮

### 3. レビューチェックリスト
各コントラクトタイプ別のチェックリストを以下に示します：

#### Storageコントラクト
- [ ] 対応するLogicコントラクトからのみアクセス可能
- [ ] CRUD操作のみを実装（ビジネスロジックなし）
- [ ] バックアップリストア関数にAdmin制限
- [ ] mappingとindex配列の同時管理

#### Logicコントラクト
- [ ] ContractManagerを介した外部コントラクト呼び出し
- [ ] 適切な署名検証処理
- [ ] call関数とsend関数の適切な使い分け
- [ ] contractOnly modifierの適切な使用

#### インターフェース
- [ ] 適切な関数シグネチャの定義
- [ ] 継承関係の正確性

## 関連リンク
- [基本設計]コントラクト 概要設計
- [基本設計]コントラクト データ設計
- [検討]既存のコントラクトのストレージ方式と他の方式を比較し、最適なストレージ方式を検討する

## 更新履歴
- 2025-08-22: 初版作成（Solidityコーディング規約、コントラクトリアーキテクチャ設計書）
