/*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
* http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/


'use strict';

const { WorkloadModuleBase } = require('@hyperledger/caliper-core');
const { ethers } = require("ethers");

/**
 * Workload module for transferring money between accounts.
 */
class Workload extends WorkloadModuleBase {

    /**
     * Initializes the instance.
     */
    constructor() {
        super();
    }

    /**
     * Initialize the workload module with the given parameters.
     * @param {number} workerIndex The 0-based index of the worker instantiating the workload module.
     * @param {number} totalWorkers The total number of workers participating in the round.
     * @param {number} roundIndex The 0-based index of the currently executing round.
     * @param {Object} roundArguments The user-provided arguments for the round from the benchmark configuration file.
     * @param {ConnectorBase} sutAdapter The adapter of the underlying SUT.
     * @param {Object} sutContext The custom context object provided by the SUT adapter.
     * @async
     */
     async initializeWorkloadModule(workerIndex, totalWorkers, roundIndex, roundArguments, sutAdapter, sutContext) {
        await super.initializeWorkloadModule(workerIndex, totalWorkers, roundIndex, roundArguments, sutAdapter, sutContext);
    }

    /**
     * Custom parameters setting
     */
    getParameters() {
        const txs = [...Array(this.roundArguments.count).keys()].map((v) => {
            // 各txに送っているIdが異なる
            // configからのparameter: countは1Roundに送るtxを設定
            // roundIndexは各Roundの設定(configのRoundにより自動設定：0~)
            // countとroundIndexでIdを重複しないように算出
            const accountId = this.roundArguments.accountId + v + (this.roundIndex * this.roundArguments.count);
            const accountIdHexString = ethers.toBeHex(ethers.toBigInt(ethers.toUtf8Bytes(String(accountId)))).padEnd(66, '0');
            const validatorIdHexString = ethers.toBeHex(ethers.toBigInt(ethers.toUtf8Bytes("8888"))).padEnd(66, '0');
            const accountName = "accountName" + v
            const traceId =  "0x74657374";

            const limitValues = {
                mint: 100,
                burn: 200,
                charge: 300,
                discharge: 400,
                transfer: 500,
                cumulative: {
                  total: 600,
                  mint: 700,
                  burn: 800,
                  charge: 900,
                  discharge: 1000,
                  transfer: 1100
                }
              };
            return {
                contract: 'Validator',
                verb: 'addAccount',
                args: [
                    validatorIdHexString,
                    accountIdHexString,
                    accountName,
                    limitValues,
                    traceId
                ]
            }
        })
        return txs
    }

    /**
     * Assemble TXs
     */
    async submitTransaction() {
        const txs = await this.getParameters();
        await this.sutAdapter.sendRequests(txs);
    }
}

/**
 * Create a new instance of the workload module.
 * @return {WorkloadModuleInterface}
 */
function createWorkloadModule() {
    return new Workload();
}

module.exports.createWorkloadModule = createWorkloadModule;
