simpleArgs: &params
  accountId: 7000
  count: 100

test:
  name: account
  description: >-
    add
  workers:
    type: local
    number: 1
  rounds:
    - label: addAccount-round1
      description: Test description for transfering money between accounts.
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: benchmarks/scenario/register/addAccount.js
        arguments:
          <<: *params
    - label: addAccount-round2
      description: Test description for transfering money between accounts.
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: benchmarks/scenario/register/addAccount.js
        arguments:
          <<: *params
    - label: addAccount-round3
      description: Test description for transfering money between accounts.
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: benchmarks/scenario/register/addAccount.js
        arguments:
          <<: *params
    - label: addAccount-round4
      description: Test description for transfering money between accounts.
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: benchmarks/scenario/register/addAccount.js
        arguments:
          <<: *params
    - label: addAccount-round5
      description: Test description for transfering money between accounts.
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: benchmarks/scenario/register/addAccount.js
        arguments:
          <<: *params
          