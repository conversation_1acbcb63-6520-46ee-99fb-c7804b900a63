{"caliper": {"blockchain": "ethereum", "transactionSendTimeout": ********, "command": {}}, "ethereum": {"url": "ws://127.0.0.1:18541", "fromAddressSeed": "0x8d5366123cb560bb606379f90a0bfd4769eecc0557f1b362dcae9012b548b1e5", "transactionConfirmationBlocks": 2, "contracts": {"Token": {"address": "******************************************", "estimateGas": true, "gas": {"transfer": 70000}, "abi": [{"inputs": [{"internalType": "bytes32", "name": "sendAccountId", "type": "bytes32"}, {"internalType": "bytes32", "name": "fromAccountId", "type": "bytes32"}, {"internalType": "bytes32", "name": "toAccountId", "type": "bytes32"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes32", "name": "miscValue1", "type": "bytes32"}, {"internalType": "bytes32", "name": "miscValue2", "type": "string"}, {"internalType": "string", "name": "memo", "type": "string"}, {"internalType": "bytes32", "name": "traceId", "type": "bytes32"}], "name": "transferSingle", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]}}}}