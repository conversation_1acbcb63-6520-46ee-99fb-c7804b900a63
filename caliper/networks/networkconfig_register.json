{"caliper": {"blockchain": "ethereum", "transactionSendTimeout": ********, "command": {}}, "ethereum": {"url": "ws://127.0.0.1:18541", "fromAddressSeed": "0x8d5366123cb560bb606379f90a0bfd4769eecc0557f1b362dcae9012b548b1e5", "transactionConfirmationBlocks": 2, "contracts": {"Issuer": {"address": "******************************************", "estimateGas": true, "gas": {"addIssuer": 1000000}, "abi": [{"inputs": [{"internalType": "bytes32", "name": "issuerId", "type": "bytes32"}, {"internalType": "uint16", "name": "bankCode", "type": "uint16"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "bytes32", "name": "traceId", "type": "bytes32"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "name": "add<PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]}, "Validator": {"address": "******************************************", "estimateGas": true, "gas": {"addValidator": 1000000, "addAccount": 1000000}, "abi": [{"inputs": [{"internalType": "bytes32", "name": "validatorId", "type": "bytes32"}, {"internalType": "bytes32", "name": "issuerId", "type": "bytes32"}, {"internalType": "bytes32", "name": "name", "type": "bytes32"}, {"internalType": "bytes32", "name": "traceId", "type": "bytes32"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "name": "addValidator", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "validatorId", "type": "bytes32"}, {"internalType": "bytes32", "name": "accountId", "type": "bytes32"}, {"internalType": "string", "name": "accountName", "type": "string"}, {"type": "tuple", "name": "limitAmounts", "components": [{"name": "mint", "type": "uint256"}, {"name": "burn", "type": "uint256"}, {"name": "charge", "type": "uint256"}, {"name": "discharge", "type": "uint256"}, {"name": "transfer", "type": "uint256"}, {"name": "cumulative", "type": "tuple", "components": [{"name": "total", "type": "uint256"}, {"name": "mint", "type": "uint256"}, {"name": "burn", "type": "uint256"}, {"name": "charge", "type": "uint256"}, {"name": "discharge", "type": "uint256"}, {"name": "transfer", "type": "uint256"}]}]}, {"internalType": "bytes32", "name": "traceId", "type": "bytes32"}], "name": "addAccount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]}}}}