import '@nomicfoundation/hardhat-chai-matchers'
import { ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ContractManagerInstance, IssuerInstance } from '@test/common/types'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('initialize()', () => {
  let issuer: IssuerInstance
  let contractManager: ContractManagerInstance

  const setupFixture = async () => {
    ;({ issuer, contractManager } = await contractFixture<IssuerContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    it('should revert when initialized', async () => {
      const result = issuer.initialize(await contractManager.getAddress())
      await expect(result).to.be.revertedWith(ERR.INITIALIZER.ALREADY_INIT)
    })
  })
})
