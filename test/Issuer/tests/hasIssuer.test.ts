import { BASE, ERR } from '@test/common/consts'
import { IssuerInstance } from '@test/common/types'
import { assertEqualForEachField, toBytes32 } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { before } from 'mocha'

const NOT_REG_ISSUER_ID = toBytes32('x399')

describe('hasIssuer()', () => {
  let issuer: IssuerInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, issuer } = await contractFixture<IssuerContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('issuerが登録されている状態', () => {
      before(async () => {
        await issuerFuncs.addIssuer({ issuer, accounts })
      })

      it('issuerが存在する場合、trueが取得できること', async () => {
        const result = await issuerFuncs.hasIssuer({ issuer, params: [BASE.ISSUER.ISSUER0.ID] })

        assertEqualForEachField(result, { success: true, err: '' })
      })

      it('空issuerIdを指定した場合、エラーが返されること', async () => {
        const result = await issuerFuncs.hasIssuer({ issuer, params: [BASE.ISSUER.EMPTY.ID] })

        assertEqualForEachField(result, { success: false, err: ERR.ISSUER.ISSUER_INVALID_VAL })
      })

      it('未登録のissuerId指定の場合、エラーが返されること', async () => {
        const result = await issuerFuncs.hasIssuer({ issuer, params: [NOT_REG_ISSUER_ID] })

        assertEqualForEachField(result, { success: false, err: ERR.ISSUER.ISSUER_ID_NOT_EXIST })
      })
    })
  })
})
