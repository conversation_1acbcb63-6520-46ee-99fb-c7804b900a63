import '@nomicfoundation/hardhat-chai-matchers'
import { anyValue } from '@nomicfoundation/hardhat-chai-matchers/withArgs'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ModTokenLimitOption, ProviderInstance, ValidatorInstance } from '@test/common/types'
import { toBytes32 } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { ethers } from 'hardhat'
import { before } from 'mocha'

const NOT_REG_ISSUER_ID = toBytes32('x399')

describe('modTokenLimit()', () => {
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, issuer, validator, provider } = await contractFixture<IssuerContractType>())
  }

  const setupBasicRoles = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: { zoneId: BASE.ZONE_ID.ID1 },
    })
    await providerFuncs.addProviderRole({
      provider,
      accounts,
      options: {
        providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
      },
    })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
    await validatorFuncs.addAccount({ validator, accounts })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('issuerRole, accountが登録されている状態', () => {
      before(async () => {
        await setupBasicRoles()
      })

      it('Accountの限度額が更新できること', async () => {
        const params: ModTokenLimitOption = {
          issuerId: BASE.ISSUER.ISSUER0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          limitUpdates: {
            mint: true,
            burn: true,
            charge: true,
            discharge: true,
            transfer: true,
            cumulative: {
              total: true,
              mint: true,
              burn: true,
              charge: true,
              discharge: true,
              transfer: true,
            },
          },
          limitValues: {
            mint: 1100,
            burn: 1200,
            charge: 1300,
            discharge: 1400,
            transfer: 1500,
            cumulative: {
              total: 12000,
              mint: 1200,
              burn: 1300,
              charge: 1400,
              discharge: 1500,
              transfer: 1000,
            },
          },
        }

        const tx = await issuerFuncs.modTokenLimit({ issuer, accounts, options: params })

        const { issuerId, ...expected } = params
        await expect(tx)
          .to.emit(issuer, 'ModTokenLimit')
          .withArgs(
            anyValue,
            params.accountId,
            [true, true, true, true, true, [true, true, true, true, true, true]],
            [1100, 1200, 1300, 1400, 1500, [12000, 1200, 1300, 1400, 1500, 1000]],
            anyValue,
          )
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, issuer, validator, provider } = await contractFixture<IssuerContractType>())
    })

    describe('issuerRole, accountが登録されている状態', () => {
      before(async () => {
        await setupBasicRoles()
      })

      it('空issuerIdを指定した場合、エラーがスローされること', async () => {
        const result = issuerFuncs.modTokenLimit({
          issuer,
          accounts,
          options: { issuerId: BASE.ISSUER.EMPTY.ID },
        })
        await expect(result).to.be.revertedWith(ERR.ISSUER.ISSUER_INVALID_VAL)
      })

      it('未登録issuerIdを指定した場合、エラーがスローされること', async () => {
        const result = issuerFuncs.modTokenLimit({ issuer, accounts, options: { issuerId: NOT_REG_ISSUER_ID } })
        await expect(result).to.be.revertedWith(ERR.ISSUER.ISSUER_ID_NOT_EXIST)
      })

      it('空accountIdを指定した場合、エラーがスローされること', async () => {
        const result = issuerFuncs.modTokenLimit({
          issuer,
          accounts,
          options: { accountId: BASE.ACCOUNT.EMPTY.ID },
        })
        await expect(result).to.be.revertedWith(ERR.COMMON.INVALID_ACCOUNT_ID)
      })

      it('未登録accountIdを指定した場合、エラーがスローされること', async () => {
        const result = issuerFuncs.modTokenLimit({
          issuer,
          accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT2.ID },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })

      it('transferLimitが999,999,999,999を超える場合、エラーがスローされること', async () => {
        const result = issuerFuncs.modTokenLimit({
          issuer,
          accounts,
          options: {
            limitUpdates: {
              mint: false,
              burn: false,
              charge: false,
              discharge: false,
              transfer: true,
              cumulative: {
                total: false,
                mint: false,
                burn: false,
                charge: false,
                discharge: false,
                transfer: false,
              },
            },
            limitValues: {
              mint: 1100,
              burn: 1200,
              charge: 1300,
              discharge: 1400,
              transfer: *************,
              cumulative: {
                total: 12000,
                mint: 1200,
                burn: 1300,
                charge: 1400,
                discharge: 1500,
                transfer: 1000,
              },
            },
          },
        })
        await expect(result).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_TRANSFER_LIMIT)
      })

      it('chargeLimitが999,999,999,999を超える場合、エラーがスローされること', async () => {
        const result = issuerFuncs.modTokenLimit({
          issuer,
          accounts,
          options: {
            limitUpdates: {
              mint: false,
              burn: false,
              charge: true,
              discharge: false,
              transfer: false,
              cumulative: {
                total: false,
                mint: false,
                burn: false,
                charge: false,
                discharge: false,
                transfer: false,
              },
            },
            limitValues: {
              mint: 1100,
              burn: 1200,
              charge: *************,
              discharge: 1400,
              transfer: 1000,
              cumulative: {
                total: 12000,
                mint: 1200,
                burn: 1300,
                charge: 1400,
                discharge: 1500,
                transfer: 1000,
              },
            },
          },
        })
        await expect(result).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_CHARGE_LIMIT)
      })

      it('mintLimitが999,999,999,999を超える場合、エラーがスローされること', async () => {
        const result = issuerFuncs.modTokenLimit({
          issuer,
          accounts,
          options: {
            limitUpdates: {
              mint: true,
              burn: false,
              charge: false,
              discharge: false,
              transfer: false,
              cumulative: {
                total: false,
                mint: false,
                burn: false,
                charge: false,
                discharge: false,
                transfer: false,
              },
            },
            limitValues: {
              mint: *************,
              burn: 1200,
              charge: 1000,
              discharge: 1400,
              transfer: 1000,
              cumulative: {
                total: 12000,
                mint: 1200,
                burn: 1300,
                charge: 1400,
                discharge: 1500,
                transfer: 1000,
              },
            },
          },
        })
        await expect(result).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_MINT_LIMIT)
      })

      it('burnLimitが999,999,999,999を超える場合、エラーがスローされること', async () => {
        const result = issuerFuncs.modTokenLimit({
          issuer,
          accounts,
          options: {
            limitUpdates: {
              mint: false,
              burn: true,
              charge: false,
              discharge: false,
              transfer: false,
              cumulative: {
                total: false,
                mint: false,
                burn: false,
                charge: false,
                discharge: false,
                transfer: false,
              },
            },
            limitValues: {
              mint: 1000,
              burn: *************,
              charge: 1000,
              discharge: 1400,
              transfer: 1000,
              cumulative: {
                total: 12000,
                mint: 1200,
                burn: 1300,
                charge: 1400,
                discharge: 1500,
                transfer: 1000,
              },
            },
          },
        })
        await expect(result).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_BURN_LIMIT)
      })

      it('dischargeLimitが999,999,999,999を超える場合、エラーがスローされること', async () => {
        const result = issuerFuncs.modTokenLimit({
          issuer,
          accounts,
          options: {
            limitUpdates: {
              mint: false,
              burn: false,
              charge: false,
              discharge: true,
              transfer: false,
              cumulative: {
                total: false,
                mint: false,
                burn: false,
                charge: false,
                discharge: false,
                transfer: false,
              },
            },
            limitValues: {
              mint: 1000,
              burn: 1000,
              charge: 1000,
              discharge: *************,
              transfer: 1000,
              cumulative: {
                total: 12000,
                mint: 1200,
                burn: 1300,
                charge: 1400,
                discharge: 1500,
                transfer: 1000,
              },
            },
          },
        })
        await expect(result).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_DISCHARGE_LIMIT)
      })

      it('cumulativeLimitが999,999,999,999,999を超える場合、エラーがスローされること', async () => {
        const result = issuerFuncs.modTokenLimit({
          issuer,
          accounts,
          options: {
            limitUpdates: {
              mint: false,
              burn: false,
              charge: false,
              discharge: false,
              transfer: false,
              cumulative: {
                total: true,
                mint: false,
                burn: false,
                charge: false,
                discharge: false,
                transfer: false,
              },
            },
            limitValues: {
              mint: 1000,
              burn: 1000,
              charge: 1000,
              discharge: 1400,
              transfer: 1000,
              cumulative: {
                total: ****************,
                mint: 1200,
                burn: 1300,
                charge: 1400,
                discharge: 1500,
                transfer: 1000,
              },
            },
          },
        })
        await expect(result).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_DAILY_LIMIT)
      })

      it('cumulativeMintLimitが999,999,999,999,999を超える場合、エラーがスローされること', async () => {
        const result = issuerFuncs.modTokenLimit({
          issuer,
          accounts,
          options: {
            limitUpdates: {
              mint: false,
              burn: false,
              charge: false,
              discharge: false,
              transfer: false,
              cumulative: {
                total: false,
                mint: true,
                burn: false,
                charge: false,
                discharge: false,
                transfer: false,
              },
            },
            limitValues: {
              mint: 1000,
              burn: 1000,
              charge: 1000,
              discharge: 1400,
              transfer: 1000,
              cumulative: {
                total: 1000,
                mint: ****************,
                burn: 1300,
                charge: 1400,
                discharge: 1500,
                transfer: 1000,
              },
            },
          },
        })
        await expect(result).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_DAILY_MINT_LIMIT)
      })

      it('cumulativeBurnLimitが999,999,999,999,999を超える場合、エラーがスローされること', async () => {
        const result = issuerFuncs.modTokenLimit({
          issuer,
          accounts,
          options: {
            limitUpdates: {
              mint: false,
              burn: false,
              charge: false,
              discharge: false,
              transfer: false,
              cumulative: {
                total: false,
                mint: false,
                burn: true,
                charge: false,
                discharge: false,
                transfer: false,
              },
            },
            limitValues: {
              mint: 1000,
              burn: 1000,
              charge: 1000,
              discharge: 1400,
              transfer: 1000,
              cumulative: {
                total: 1000,
                mint: 1000,
                burn: ****************,
                charge: 1400,
                discharge: 1500,
                transfer: 1000,
              },
            },
          },
        })
        await expect(result).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_DAILY_BURN_LIMIT)
      })

      it('cumulativeChargeLimitが999,999,999,999,999を超える場合、エラーがスローされること', async () => {
        const result = issuerFuncs.modTokenLimit({
          issuer,
          accounts,
          options: {
            limitUpdates: {
              mint: false,
              burn: false,
              charge: false,
              discharge: false,
              transfer: false,
              cumulative: {
                total: false,
                mint: false,
                burn: false,
                charge: true,
                discharge: false,
                transfer: false,
              },
            },
            limitValues: {
              mint: 1000,
              burn: 1000,
              charge: 1000,
              discharge: 1400,
              transfer: 1000,
              cumulative: {
                total: 1000,
                mint: 1000,
                burn: 1000,
                charge: ****************,
                discharge: 1500,
                transfer: 1000,
              },
            },
          },
        })
        await expect(result).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_DAILY_CHARGE_LIMIT)
      })

      it('cumulativeDisChargeLimitが999,999,999,999,999を超える場合、エラーがスローされること', async () => {
        const result = issuerFuncs.modTokenLimit({
          issuer,
          accounts,
          options: {
            limitUpdates: {
              mint: false,
              burn: false,
              charge: false,
              discharge: false,
              transfer: false,
              cumulative: {
                total: false,
                mint: false,
                burn: false,
                charge: false,
                discharge: true,
                transfer: false,
              },
            },
            limitValues: {
              mint: 1000,
              burn: 1000,
              charge: 1000,
              discharge: 1400,
              transfer: 1000,
              cumulative: {
                total: 1000,
                mint: 1000,
                burn: 1000,
                charge: 1000,
                discharge: ****************,
                transfer: 1000,
              },
            },
          },
        })
        await expect(result).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_DAILY_DISCHARGE_LIMIT)
      })

      it('cumulativeTransferLimitが999,999,999,999,999を超える場合、エラーがスローされること', async () => {
        const result = issuerFuncs.modTokenLimit({
          issuer,
          accounts,
          options: {
            limitUpdates: {
              mint: false,
              burn: false,
              charge: false,
              discharge: false,
              transfer: false,
              cumulative: {
                total: false,
                mint: false,
                burn: false,
                charge: false,
                discharge: false,
                transfer: true,
              },
            },
            limitValues: {
              mint: 1000,
              burn: 1000,
              charge: 1000,
              discharge: 1400,
              transfer: 1000,
              cumulative: {
                total: 1000,
                mint: 1000,
                burn: 1000,
                charge: 1000,
                discharge: 1000,
                transfer: ****************,
              },
            },
          },
        })
        await expect(result).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_DAILY_TRANSFER_LIMIT)
      })

      it('署名が無効の場合、エラーがスローされること', async () => {
        const result = issuerFuncs.modTokenLimit({ issuer, accounts, options: { sig: ['0x1234', ''] } })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('issuer権限がない場合、エラーをスローすること', async () => {
        const result = issuerFuncs.modTokenLimit({ issuer, accounts, options: { eoaKey: BASE.EOA.ADMIN } })
        await expect(result).to.be.revertedWith(ERR.ISSUER.ISSUER_NOT_ROLE)
      })
    })

    describe('issuerが有効の状態, accountが解約状態', () => {
      before(async () => {
        await validatorFuncs.setTerminated({ validator, accounts })
      })

      it('解約状態のaccountIdを指定した場合、エラーがスローされること', async () => {
        const result = issuerFuncs.modTokenLimit({ issuer, accounts })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_TERMINATED)
      })
      // account.isTerminatedの戻り値のerrがあるケースは、事前準備ができないため未実施
    })

    describe('accountId exist on Issuer but not Account', () => {
      it('should revert when isTerminated returns error when accountId exist on Issuer but not Account', async () => {
        // UNREACHABLE: accountId already checked in the previous require,
        // this case happen when accountId exist on Issuer but not Account

        // Hack: fake call from validator to add accountId on Issuer only (bypass Account)
        await helpers.impersonateAccount(await validator.getAddress())
        await helpers.setBalance(await validator.getAddress(), 100n ** 18n)
        const fakeValidator = await ethers.getSigner(await validator.getAddress())
        await issuer
          .connect(fakeValidator)
          .addAccountId(BASE.ISSUER.ISSUER0.ID, BASE.ACCOUNT.ACCOUNT5.ID, BASE.TRACE_ID)
        await helpers.stopImpersonatingAccount(await validator.getAddress())

        const params: ModTokenLimitOption = {
          issuerId: BASE.ISSUER.ISSUER0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT5.ID,
          limitUpdates: {
            mint: true,
            burn: true,
            charge: true,
            discharge: true,
            transfer: true,
            cumulative: {
              total: true,
              mint: true,
              burn: true,
              charge: true,
              discharge: true,
              transfer: true,
            },
          },
          limitValues: {
            mint: 1000,
            burn: 1000,
            charge: 1000,
            discharge: 1400,
            transfer: 1000,
            cumulative: {
              total: 1000,
              mint: 1000,
              burn: 1000,
              charge: 1000,
              discharge: 1000,
              transfer: 1000,
            },
          },
        }

        const result = issuerFuncs.modTokenLimit({ issuer, accounts, options: params })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })
    })
  })
})
