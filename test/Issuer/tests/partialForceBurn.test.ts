import { businessZoneAccountFuncs } from '@test/BusinessZoneAccount/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { ibcTokenFuncs } from '@test/IBCToken/helpers/function'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { expect } from 'chai'
import { before } from 'mocha'

describe('partialForceBurn()', () => {
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let provider: ProviderInstance
  let token: TokenInstance
  let contractManager: ContractManagerInstance
  let ibcToken: IBCTokenInstance
  let accounts: SignerWithAddress[]
  let businessZoneAccount: BusinessZoneAccountInstance

  const setupFixture = async () => {
    ;({ accounts, issuer, validator, provider, token, ibcToken, contractManager, businessZoneAccount } =
      await contractFixture<IssuerContractType>())
  }

  const setupBasicRoles = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({ provider, accounts })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await providerFuncs.addToken({ provider, accounts })
  }

  const setupAccountsAndToken = async () => {
    for (const accountId of [BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT1.ID]) {
      await validatorFuncs.addAccount({ validator, accounts, options: { accountId } })
    }

    await tokenFuncs.mint({
      token,
      accounts,
      amount: 600,
      options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
    })
  }

  const setupIbcApps = async (ibcAddressString: string) => {
    await contractManagerFuncs.setIbcApp({
      contractManager,
      accounts,
      ibcAddress: ibcAddressString,
      ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
    })
    await contractManagerFuncs.setIbcApp({
      contractManager,
      accounts,
      ibcAddress: ibcAddressString,
      ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
    })
  }

  const setupBusinessZone = async () => {
    await providerFuncs.addBizZone({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID1,
        zoneName: BASE.ZONE_NAME.NAME1,
      },
    })

    await businessZoneAccountFuncs.syncBusinessZoneStatus({
      businessZoneAccount,
      accounts,
      options: {
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
        zoneId: BASE.ZONE_ID.ID1,
      },
    })

    await validatorFuncs.setActiveBusinessAccountWithZone({
      validator,
      accounts,
      options: {
        validatorId: BASE.VALID.VALID0.ID,
        zoneId: BASE.ZONE_ID.ID1,
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
      },
    })
  }

  const setupFreezeAndTransfer = async (ibcAddress: any) => {
    await issuerFuncs.setAccountStatus({
      issuer,
      accounts,
      accountStatus: BASE.STATUS.FROZEN,
      options: {
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
      },
    })

    await ibcTokenFuncs.transferToEscrow({
      ibcToken,
      from: ibcAddress,
      amount: 300,
      options: {
        fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
        toAccountId: BASE.ACCOUNT.ESCROW.ID,
      },
    })
  }

  describe('正常系', () => {
    let ibcAddress

    before(async () => {
      await setupFixture()
    })

    describe('issuerRole, accountが登録されている状態', () => {
      before(async () => {
        ibcAddress = await accounts[0]
        const ibcAddressString = await ibcAddress.getAddress()
        await setupBasicRoles()
        await setupAccountsAndToken()
        await setupIbcApps(ibcAddressString)
        await setupBusinessZone()
        await setupFreezeAndTransfer(ibcAddress)
      })

      it('Accountの残高を部分的に強制償却できること', async () => {
        const beforeBurn = await validatorFuncs.getAccountAll({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        expect(beforeBurn.accountDataAll.balance).to.equal('300')
        expect(beforeBurn.accountDataAll.accountStatus).to.equal(BASE.STATUS.FROZEN)

        await issuerFuncs.partialForceBurn({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER0.ID,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            burnedAmount: 100,
            burnedBalance: 200,
          },
        })

        const afterBurn = await validatorFuncs.getAccountAll({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        expect(afterBurn.accountDataAll.balance).to.equal('200')
        expect(afterBurn.accountDataAll.accountStatus).to.equal(BASE.STATUS.FROZEN)

        const expectedState = {
          accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          accountStatus: BASE.STATUS.FROZEN,
          balance: '200',
          reasonCode: BASE.REASON_CODE1,
          zoneId: BASE.ZONE_ID.ID0,
          zoneName: BASE.ZONE_NAME.NAME0,
          appliedAt: '0',
          registeredAt: String(await utils.getLatestBlockTimestamp()),
          terminatingAt: '0',
          terminatedAt: '0',
          mintLimit: '3000',
          burnLimit: '4000',
          chargeLimit: '2000',
          transferLimit: '1000',
          cumulativeLimit: '5000',
          cumulativeAmount: '900',
          cumulativeDate: String(await utils.getJSTDay()),
        }

        const expectedBizState = [
          {
            accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
            balance: '300',
            accountStatus: BASE.STATUS.ACTIVE,
            appliedAt: String(await utils.getLatestBlockTimestamp()),
            registeredAt: String(await utils.getLatestBlockTimestamp()),
            terminatingAt: '0',
            terminatedAt: '0',
          },
        ]

        utils.assertEqualForEachField(afterBurn.accountDataAll, expectedState)
        utils.assertEqualForEachField(
          afterBurn.accountDataAll.businessZoneAccounts.map((v) => ({
            accountName: v.accountName,
            zoneId: v.zoneId,
            zoneName: v.zoneName,
            balance: v.balance,
            accountStatus: v.accountStatus,
            appliedAt: v.appliedAt,
            registeredAt: v.registeredAt,
            terminatingAt: v.terminatingAt,
            terminatedAt: v.terminatedAt,
          })),
          expectedBizState,
        )
        expect(afterBurn.err).to.equal('')
      })

      describe('burnedAmountがfin Accountより多い場合', () => {
        it('totalAmountがburnedAmountより多い場合', async () => {
          const beforeBurn = await validatorFuncs.getAccountAll({
            validator,
            prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
          })

          expect(beforeBurn.accountDataAll.balance).to.equal('200')
          expect(beforeBurn.accountDataAll.accountStatus).to.equal(BASE.STATUS.FROZEN)

          await issuerFuncs.partialForceBurn({
            issuer,
            accounts,
            options: {
              issuerId: BASE.ISSUER.ISSUER0.ID,
              accountId: BASE.ACCOUNT.ACCOUNT1.ID,
              burnedAmount: 300,
              burnedBalance: 200,
            },
          })

          const afterBurn = await validatorFuncs.getAccountAll({
            validator,
            prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
          })

          expect(afterBurn.accountDataAll.balance).to.equal('200')
          expect(afterBurn.accountDataAll.accountStatus).to.equal(BASE.STATUS.FROZEN)

          const expectedBizState = [
            {
              accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
              zoneId: BASE.ZONE_ID.ID1,
              zoneName: BASE.ZONE_NAME.NAME1,
              balance: '0',
              accountStatus: BASE.STATUS.FORCE_BURNED,
              appliedAt: String(await utils.getLatestBlockTimestamp()),
              registeredAt: String(await utils.getLatestBlockTimestamp()),
              terminatingAt: '0',
              terminatedAt: '0',
            },
          ]

          utils.assertEqualForEachField(
            afterBurn.accountDataAll.businessZoneAccounts.map((v) => ({
              accountName: v.accountName,
              zoneId: v.zoneId,
              zoneName: v.zoneName,
              balance: v.balance,
              accountStatus: v.accountStatus,
              appliedAt: v.appliedAt,
              registeredAt: v.registeredAt,
              terminatingAt: v.terminatingAt,
              terminatedAt: v.terminatedAt,
            })),
            expectedBizState,
          )
        })

        it('burnedAmountが残高より大きい場合、エラーがスローされること', async () => {
          await expect(
            issuerFuncs.partialForceBurn({
              issuer,
              accounts,
              options: {
                issuerId: BASE.ISSUER.ISSUER0.ID,
                accountId: BASE.ACCOUNT.ACCOUNT1.ID,
                burnedAmount: 1000,
                burnedBalance: 100,
              },
            }),
          ).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_INVALID_BURNED_AMOUNT)
        })

        it('expectedBalanceがburnedBalanceと一致しない場合、エラーがスローされること', async () => {
          await expect(
            issuerFuncs.partialForceBurn({
              issuer,
              accounts,
              options: {
                issuerId: BASE.ISSUER.ISSUER0.ID,
                accountId: BASE.ACCOUNT.ACCOUNT1.ID,
                burnedAmount: 150,
                burnedBalance: 150,
              },
            }),
          ).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_INVALID_BURNED_BALANCE)
        })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('issuerRole, accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator, accounts })
      })

      it('Accountが存在しない場合、部分的強制償却に失敗すること', async () => {
        await expect(
          issuerFuncs.partialForceBurn({
            issuer,
            accounts,
            options: {
              issuerId: BASE.ISSUER.ISSUER0.ID,
              accountId: BASE.ACCOUNT.ACCOUNT10.ID,
              burnedAmount: 100,
              burnedBalance: 200,
            },
          }),
        ).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })

      it('未登録issuerIdを指定した場合、エラーがスローされること', async () => {
        await expect(
          issuerFuncs.partialForceBurn({
            issuer,
            accounts,
            options: {
              issuerId: BASE.ISSUER.ISSUER10.ID,
              accountId: BASE.ACCOUNT.ACCOUNT0.ID,
              burnedAmount: 100,
              burnedBalance: 200,
            },
          }),
        ).to.be.revertedWith(ERR.ISSUER.ISSUER_ID_NOT_EXIST)
      })

      it('空accountIdを指定した場合、エラーがスローされること', async () => {
        await expect(
          issuerFuncs.partialForceBurn({
            issuer,
            accounts,
            options: {
              issuerId: BASE.ISSUER.ISSUER0.ID,
              accountId: BASE.ACCOUNT.EMPTY.ID,
              burnedAmount: 100,
              burnedBalance: 200,
            },
          }),
        ).to.be.revertedWith(ERR.COMMON.INVALID_ACCOUNT_ID)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const exceededDeadline = await utils.getExceededDeadline()
        await expect(
          issuerFuncs.partialForceBurn({
            issuer,
            accounts,
            options: {
              deadline: exceededDeadline,
              burnedAmount: 100,
              burnedBalance: 200,
            },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('署名が不正である場合、エラーがスローされること', async () => {
        await expect(
          issuerFuncs.partialForceBurn({
            issuer,
            accounts,
            options: {
              sig: ['0x1234', ''],
              burnedAmount: 100,
              burnedBalance: 200,
            },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('issuer権限がない場合、エラーをスローすること', async () => {
        await expect(
          issuerFuncs.partialForceBurn({
            issuer,
            accounts,
            options: {
              eoaKey: BASE.EOA.ADMIN,
              burnedAmount: 100,
              burnedBalance: 200,
            },
          }),
        ).to.be.revertedWith(ERR.ISSUER.ISSUER_NOT_ROLE)
      })
    })
  })
})
