import { AccountLimitUpdates, AccountLimitValues } from '@test/common/types'

export const DEFAULT_LIMIT_UPDATES: AccountLimitUpdates = {
  mint: true,
  burn: true,
  charge: true,
  discharge: true,
  transfer: true,
  cumulative: {
    total: true,
    mint: true,
    burn: true,
    charge: true,
    discharge: true,
    transfer: true,
  },
}

export const AccountLimitUpdatesStruct = {
  type: 'tuple',
  components: [
    { type: 'bool', name: 'mint' },
    { type: 'bool', name: 'burn' },
    { type: 'bool', name: 'charge' },
    { type: 'bool', name: 'discharge' },
    { type: 'bool', name: 'transfer' },
    // Nested struct CumulativeLimitUpdates
    {
      type: 'tuple',
      name: 'cumulative',
      components: [
        { type: 'bool', name: 'total' },
        { type: 'bool', name: 'mint' },
        { type: 'bool', name: 'burn' },
        { type: 'bool', name: 'charge' },
        { type: 'bool', name: 'discharge' },
        { type: 'bool', name: 'transfer' },
      ],
    },
  ],
}

export const DEFAULT_LIMIT_VALUES: AccountLimitValues = {
  mint: 100,
  burn: 100,
  charge: 100,
  discharge: 100,
  transfer: 100,
  cumulative: {
    total: 10,
    mint: 10,
    burn: 10,
    charge: 10,
    discharge: 10,
    transfer: 10,
  },
}

export const AccountLimitValuesStruct = {
  type: 'tuple',
  components: [
    { type: 'uint256', name: 'mint' },
    { type: 'uint256', name: 'burn' },
    { type: 'uint256', name: 'charge' },
    { type: 'uint256', name: 'discharge' },
    { type: 'uint256', name: 'transfer' },
    // Nested struct CumulativeLimitValues
    {
      type: 'tuple',
      name: 'cumulative',
      components: [
        { type: 'uint256', name: 'total' },
        { type: 'uint256', name: 'mint' },
        { type: 'uint256', name: 'burn' },
        { type: 'uint256', name: 'charge' },
        { type: 'uint256', name: 'discharge' },
        { type: 'uint256', name: 'transfer' },
      ],
    },
  ],
}
