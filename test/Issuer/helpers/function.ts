import { BASE } from '@test/common/consts'
import { castReturnType, getDeadline } from '@test/common/utils'
import { IssuerFunctionType } from './types'
import {
  AccountLimitUpdatesStruct,
  AccountLimitValuesStruct,
  DEFAULT_LIMIT_UPDATES,
  DEFAULT_LIMIT_VALUES,
} from './utils'
import privateKey from '@/privateKey'

/**
 * issuerのイベントを呼ぶ関数を持つobject
 */
export const issuerFuncs: IssuerFunctionType = {
  version: ({ issuer }) => {
    return castReturnType(issuer.version())
  },
  getIssuerList: ({ issuer, params }) => {
    return castReturnType(issuer.getIssuerList(...params))
  },
  getIssuer: ({ issuer, params }) => {
    return castReturnType(issuer.getIssuer(...params))
  },
  getIssuerId: ({ issuer, params }) => {
    return castReturnType(issuer.getIssuerId(...params))
  },
  getIssuerCount: ({ issuer }) => {
    return castReturnType(issuer.getIssuerCount())
  },
  hasIssuer: ({ issuer, params }) => {
    return castReturnType(issuer.hasIssuer(...params))
  },
  checkRole: async ({ issuer, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ISSUER1, issuerId = BASE.ISSUER.ISSUER0.ID, hash = 'morning' } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ?? privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'string', 'uint256'], [issuerId, hash, _deadline])

    return castReturnType(issuer.checkRole(issuerId, _sig[1], _deadline, _sig[0]))
  },
  checkMint: async ({ issuer, amount, options = {} }) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ISSUER1,
      issuerId = BASE.ISSUER.ISSUER0.ID,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
    } = options
    const _deadline = await getDeadline(deadline)
    const signer = privateKey.key[eoaKey]
    const _sig =
      sig ??
      privateKey.sig(signer, ['bytes32', 'bytes32', 'uint256', 'uint256'], [issuerId, accountId, amount, _deadline])
    return castReturnType(issuer.checkMint(issuerId, accountId, amount, _deadline, _sig[0]))
  },
  checkBurn: async ({ issuer, amount, options = {} }) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ISSUER1,
      issuerId = BASE.ISSUER.ISSUER0.ID,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
    } = options
    const _deadline = await getDeadline(deadline)
    const signer = privateKey.key[eoaKey]
    const _sig =
      sig ??
      privateKey.sig(signer, ['bytes32', 'bytes32', 'uint256', 'uint256'], [issuerId, accountId, amount, _deadline])
    return castReturnType(issuer.checkBurn(issuerId, accountId, amount, _deadline, _sig[0]))
  },
  addIssuer: async ({ issuer, accounts, options = {} }) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ADMIN,
      issuerId = BASE.ISSUER.ISSUER0.ID,
      bankCode = BASE.ISSUER.ISSUER0.BANK_CODE,
      name = BASE.ISSUER.ISSUER0.NAME,
    } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ??
      privateKey.sig(
        privateKey.key[eoaKey],
        ['bytes32', 'uint16', 'string', 'uint256'],
        [issuerId, bankCode, name, _deadline],
      )

    return castReturnType(
      issuer.connect(accounts[9]).addIssuer(issuerId, bankCode, name, BASE.TRACE_ID, _deadline, _sig[0]),
    )
  },
  addAccountId: async ({ issuer, accounts, options = {} }) => {
    const { issuerId = BASE.ISSUER.ISSUER0.ID, accountId = BASE.ACCOUNT.ACCOUNT1.ID } = options
    return castReturnType(issuer.connect(accounts[9]).addAccountId(issuerId, accountId, BASE.TRACE_ID))
  },
  modIssuer: async ({ issuer, accounts, options = {} }) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ADMIN,
      issuerId = BASE.ISSUER.ISSUER0.ID,
      name = BASE.ISSUER.ISSUER0.NAME,
    } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ?? privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'string', 'uint256'], [issuerId, name, _deadline])

    return castReturnType(issuer.connect(accounts[9]).modIssuer(issuerId, name, BASE.TRACE_ID, _deadline, _sig[0]))
  },
  addIssuerRole: async ({ issuer, accounts, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, issuerId = BASE.ISSUER.ISSUER0.ID, issuerEoa } = options
    const _issuerEoa = issuerEoa ?? (await accounts[BASE.EOA.ISSUER1].getAddress())
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ??
      privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'address', 'uint256'], [issuerId, _issuerEoa, _deadline])

    return castReturnType(
      issuer.connect(accounts[9]).addIssuerRole(issuerId, _issuerEoa, BASE.TRACE_ID, _deadline, _sig[0]),
    )
  },
  modTokenLimit: async ({ issuer, accounts, options = {} }) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ISSUER1,
      issuerId = BASE.ISSUER.ISSUER0.ID,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
      limitUpdates = DEFAULT_LIMIT_UPDATES,
      limitValues = DEFAULT_LIMIT_VALUES,
    } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ??
      privateKey.sig(
        privateKey.key[eoaKey],
        [
          'bytes32', // issuerId
          'uint256', // accountId
          AccountLimitUpdatesStruct, // Struct AccountLimitUpdates
          AccountLimitValuesStruct, // Struct AccountLimitValues
          'uint256', // deadline
        ],
        [issuerId, accountId, limitUpdates, limitValues, _deadline],
      )

    return castReturnType(
      issuer
        .connect(accounts[0])
        .modTokenLimit(issuerId, accountId, limitUpdates, limitValues, BASE.TRACE_ID, _deadline, _sig[0]),
    )
  },
  cumulativeReset: async ({ issuer, accounts, options = {} }) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ISSUER1,
      issuerId = BASE.ISSUER.ISSUER0.ID,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
    } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ?? privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'uint256', 'uint256'], [issuerId, accountId, _deadline])

    return castReturnType(
      issuer.connect(accounts[0]).cumulativeReset(issuerId, accountId, BASE.TRACE_ID, _deadline, _sig[0]),
    )
  },
  setAccountStatus: async ({ issuer, accounts, accountStatus, options = {} }) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ISSUER1,
      issuerId = BASE.ISSUER.ISSUER0.ID,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
    } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ??
      privateKey.sig(
        privateKey.key[eoaKey],
        ['bytes32', 'uint256', 'bytes32', 'uint256'],
        [issuerId, accountId, BASE.REASON_CODE1, _deadline],
      )

    return castReturnType(
      issuer
        .connect(accounts[0])
        .setAccountStatus(issuerId, accountId, accountStatus, BASE.REASON_CODE1, BASE.TRACE_ID, _deadline, _sig[0]),
    )
  },
  getAccountList: ({ issuer, params }) => {
    return castReturnType(issuer.getAccountList(...params))
  },
  getAccount: ({ issuer, params }) => {
    return castReturnType(issuer.getAccount(...params))
  },
  hasAccount: ({ issuer, params }) => {
    return castReturnType(issuer.hasAccount(...params))
  },
  addAccountRole: async ({ issuer, accounts, options = {} }) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ISSUER1,
      issuerId = BASE.ISSUER.ISSUER0.ID,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
      accountEoa = BASE.ACCOUNT.EOA_ADDRESS,
    } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ??
      privateKey.sig(
        privateKey.key[eoaKey],
        ['bytes32', 'bytes32', 'address', 'uint256'],
        [issuerId, accountId, accountEoa, _deadline],
      )

    return castReturnType(
      issuer.connect(accounts[0]).addAccountRole(issuerId, accountId, accountEoa, BASE.TRACE_ID, _deadline, _sig[0]),
    )
  },
  forceBurn: async ({ issuer, accounts, options = {} }) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ISSUER1,
      issuerId = BASE.ISSUER.ISSUER0.ID,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
    } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ?? privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'bytes32', 'uint256'], [issuerId, accountId, _deadline])

    return castReturnType(issuer.connect(accounts[9]).forceBurn(issuerId, accountId, BASE.TRACE_ID, _deadline, _sig[0]))
  },
  addBizZoneToIssuer: async ({ issuer, accounts, options = {} }) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ADMIN,
      issuerId = BASE.ISSUER.ISSUER0.ID,
      zoneId = BASE.ZONE_ID.ID0,
    } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ?? privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'uint16', 'uint256'], [issuerId, zoneId, _deadline])

    return castReturnType(
      issuer.connect(accounts[9]).addBizZoneToIssuer(issuerId, zoneId, BASE.TRACE_ID, _deadline, _sig[0]),
    )
  },
  deleteBizZoneToIssuer: async ({ issuer, accounts, options = {} }) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ADMIN,
      issuerId = BASE.ISSUER.ISSUER0.ID,
      zoneId = BASE.ZONE_ID.ID0,
    } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ?? privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'uint16', 'uint256'], [issuerId, zoneId, _deadline])

    return castReturnType(
      issuer.connect(accounts[9]).deleteBizZoneToIssuer(issuerId, zoneId, BASE.TRACE_ID, _deadline, _sig[0]),
    )
  },
  partialForceBurn: async ({ issuer, accounts, options = {} }) => {
    const {
      issuerId = BASE.ISSUER.ISSUER0.ID,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
      eoaKey = BASE.EOA.ISSUER1,
      burnedAmount = 0,
      burnedBalance = 0,
      deadline,
      sig,
    } = options

    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ??
      privateKey.sig(
        privateKey.key[eoaKey],
        ['bytes32', 'bytes32', 'uint256', 'uint256', 'uint256'],
        [issuerId, accountId, burnedAmount, burnedBalance, _deadline],
      )

    return castReturnType(
      issuer
        .connect(accounts[9])
        .partialForceBurn(issuerId, accountId, burnedAmount, burnedBalance, BASE.TRACE_ID, _deadline, _sig[0]),
    )
  },
  // getIssuersAll: async (
  //   issuer: IssuerInstance,
  //   offset: number,
  //   limit: number,
  //   {
  //     sig,
  //     deadline,
  //     eoaKey = BASE.EOA.ADMIN,
  //     hash = BASE.SALTS.GET_ISSUERS_ALL,
  //   }: Partial<GetIssuersAllOption & ContractCallOption> = {},
  // ) => {
  //   const _deadline = deadline ?? (await utils.getDeadline());
  //   const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline]);

  //   return issuer.getIssuersAll(offset, limit, _deadline, _sig[0]) as unknown as Promise<
  //     EventReturnType['Issuer']['GetIssuersAll']
  //   >;
  // },
  // setIssuersAll: async (
  //   issuer: IssuerInstance,
  //   issuers: any,
  //   accounts: SignerWithAddress[],
  //   {
  //     sig,
  //     deadline,
  //     eoaKey = BASE.EOA.ADMIN,
  //     hash = BASE.SALTS.SET_ISSUERS_ALL,
  //   }: Partial<SetIssuersAllOption & ContractCallOption> = {},
  // ) => {
  //   const _deadline = deadline ?? (await utils.getDeadline());
  //   const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline]);

  //   return issuer.setIssuersAll(issuers, _deadline, _sig[0], { from: accounts[9] });
  // },
}
