import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccountInstance,
  AddAccountIdOption,
  AddAccountRoleOption,
  AddBizZoneToIssuerOption,
  AddIssuerOption,
  AddIssuerRoleOption,
  BusinessZoneAccountInstance,
  ContractCallOption,
  ContractManagerInstance,
  CumulativeResetOption,
  EventReturnType,
  FinancialCheckInstance,
  FinancialZoneAccountInstance,
  ForceBurnOption,
  IBCTokenInstance,
  IssuerCheckOption,
  IssuerInstance,
  ModIssuerOption,
  ModTokenLimitOption,
  PartialForceBurnOption,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { ContractTransactionResponse } from 'ethers'

export type IssuerContractType = {
  accounts: SignerWithAddress[]
  issuer: IssuerInstance
  validator: ValidatorInstance
  provider: ProviderInstance
  account: AccountInstance
  token: TokenInstance
  ibcToken: IBCTokenInstance
  contractManager: ContractManagerInstance
  financialZoneAccount: FinancialZoneAccountInstance
  businessZoneAccount: BusinessZoneAccountInstance
  financialCheck: FinancialCheckInstance
}

type IssuerType = { issuer: IssuerInstance }

type BaseAmountType = IssuerType & {
  amount: number
}

type BaseAccountsType = IssuerType & {
  accounts: SignerWithAddress[]
}

export type FuncParamsType = {
  version: IssuerType
  getIssuerList: IssuerType & {
    params: Parameters<IssuerInstance['getIssuerList']>
  }
  getIssuer: IssuerType & { params: Parameters<IssuerInstance['getIssuer']> }
  getIssuerId: IssuerType & {
    params: Parameters<IssuerInstance['getIssuerId']>
  }
  getIssuerCount: IssuerType
  hasIssuer: IssuerType & { params: Parameters<IssuerInstance['hasIssuer']> }
  checkRole: IssuerType & {
    options?: Partial<IssuerCheckOption & ContractCallOption>
  }
  checkMint: BaseAmountType & {
    options?: Partial<IssuerCheckOption & ContractCallOption>
  }
  checkBurn: BaseAmountType & {
    options?: Partial<IssuerCheckOption & ContractCallOption>
  }
  addIssuer: BaseAccountsType & {
    options?: Partial<AddIssuerOption & ContractCallOption>
  }
  addAccountId: BaseAccountsType & {
    options?: Partial<AddAccountIdOption & ContractCallOption>
  }
  modIssuer: BaseAccountsType & {
    options?: Partial<ModIssuerOption & ContractCallOption>
  }
  addIssuerRole: BaseAccountsType & {
    options?: Partial<AddIssuerRoleOption & ContractCallOption>
  }
  modTokenLimit: BaseAccountsType & {
    options?: Partial<ModTokenLimitOption & ContractCallOption>
  }
  cumulativeReset: BaseAccountsType & {
    options?: Partial<CumulativeResetOption & ContractCallOption>
  }
  addBizZoneToIssuer: BaseAccountsType & {
    options?: Partial<AddBizZoneToIssuerOption & ContractCallOption>
  }
  deleteBizZoneToIssuer: BaseAccountsType & {
    options?: Partial<AddBizZoneToIssuerOption & ContractCallOption>
  }
  setAccountStatus: BaseAccountsType & {
    accountStatus: string
    options?: Partial<CumulativeResetOption & ContractCallOption>
  }
  getAccountList: IssuerType & {
    params: Parameters<IssuerInstance['getAccountList']>
  }
  getAccount: IssuerType & { params: Parameters<IssuerInstance['getAccount']> }
  hasAccount: IssuerType & { params: Parameters<IssuerInstance['hasAccount']> }
  addAccountRole: BaseAccountsType & {
    options?: Partial<AddAccountRoleOption & ContractCallOption>
  }
  forceBurn: BaseAccountsType & {
    options: Partial<ForceBurnOption & ContractCallOption>
  }
  partialForceBurn: BaseAccountsType & {
    options: Partial<PartialForceBurnOption & ContractCallOption>
  }
}

type FuncReturnType = {
  version: string
  getIssuerList: EventReturnType['Issuer']['GetIssuerList']
  getIssuer: EventReturnType['Issuer']['GetIssuer']
  getIssuerId: EventReturnType['Issuer']['GetIssuerId']
  getIssuerCount: EventReturnType['Issuer']['GetIssuerCount']
  hasIssuer: EventReturnType['Issuer']['HasIssuer']
  checkRole: EventReturnType['Issuer']['CheckRole']
  checkMint: EventReturnType['Issuer']['CheckMint']
  checkBurn: EventReturnType['Issuer']['CheckBurn']
  addIssuer: ContractTransactionResponse
  addAccountId: ContractTransactionResponse
  modIssuer: ContractTransactionResponse
  addIssuerRole: ContractTransactionResponse
  modTokenLimit: ContractTransactionResponse
  cumulativeReset: ContractTransactionResponse
  addBizZoneToIssuer: ContractTransactionResponse
  deleteBizZoneToIssuer: ContractTransactionResponse
  setAccountStatus: ContractTransactionResponse
  getAccountList: EventReturnType['Issuer']['GetAccountList']
  getAccount: EventReturnType['Issuer']['GetAccount']
  hasAccount: EventReturnType['Issuer']['HasAccount']
  addAccountRole: ContractTransactionResponse
  forceBurn: ContractTransactionResponse
  partialForceBurn: ContractTransactionResponse
}

export type IssuerFunctionType = {
  [K in keyof FuncParamsType]: (args: FuncParamsType[K]) => Promise<FuncReturnType[K]>
}
