import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ProviderInstance, RemigrationBackupInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { providerFuncs } from '@test/Provider/helpers/function'
import { remigrationFuncs } from '@test/RemigrationBackup/helpers/function'
import { RemigrationBackupContractType } from '@test/RemigrationBackup/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('backupProvider()', () => {
  let accounts: SignerWithAddress[]
  let provider: ProviderInstance
  let remigrationBackup: RemigrationBackupInstance

  const setupFixture = async () => {
    ;({ accounts, provider, remigrationBackup } = await contractFixture<RemigrationBackupContractType>())
  }

  const setupProviderData = async () => {
    await providerFuncs.addProvider({ provider, accounts })
    await providerFuncs.addProviderRole({ provider, accounts })
    await providerFuncs.modProvider({ provider, accounts, providerName: BASE.PROV.PROV0.NAME })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('providerが登録されていない状態', () => {
      it('取得したprovider情報が初期値であること', async () => {
        const result = await remigrationFuncs.backupProvider({ remigration: remigrationBackup })
        utils.assertEqualForEachField(result, {
          providers: [],
          totalCount: 0,
          err: '',
        })
        utils.assertEqualForEachField(result, { err: '' })
      })
    })

    describe('provider情報が登録されている状態', () => {
      before(async () => {
        await setupProviderData()
      })

      it('provider情報が取得できること', async () => {
        const result = await remigrationFuncs.backupProvider({ remigration: remigrationBackup })
        // utils.assertEqualForEachField(result.providers[0], {
        //   providerId: BASE.PROV.PROV0.ID,
        //   providerData: {
        //     role: '0x8edf85e2d94cd8e4ad77c5b0aa98c9c7200b6f1bc68c11b67db960b27cb0f5d4',
        //     name: BASE.PROV.PROV0.NAME,
        //     zoneId: String(BASE.ZONE_ID.ID0),
        //     enabled: true,
        //   },
        //   providerEoa: accounts[BASE.EOA.PROV1],
        // });
        await expect(result.providers[0].providerId).to.be.equal(BASE.PROV.PROV0.ID)
        await expect(result.providers[0].providerData).to.be.deep.equal(
          Object.values({
            role: '0x8edf85e2d94cd8e4ad77c5b0aa98c9c7200b6f1bc68c11b67db960b27cb0f5d4',
            name: BASE.PROV.PROV0.NAME,
            zoneId: String(BASE.ZONE_ID.ID0),
            enabled: true,
          }),
        )
        await expect(result.providers[0].providerEoa).to.be.equal(accounts[BASE.EOA.PROV1])

        utils.assertEqualForEachField(result.providers[0].zoneData[0], {
          zoneId: String(BASE.ZONE_ID.ID0),
          zoneName: BASE.ZONE_NAME.NAME0,
        })
        utils.assertEqualForEachField(result, { err: '' })
      })

      it('署名期限切れの場合、取得したprovider情報が初期値であること', async () => {
        const exceededDeadline = await utils.getExceededDeadline()
        const result = await remigrationFuncs.backupProvider({
          remigration: remigrationBackup,
          options: { deadline: exceededDeadline },
        })
        utils.assertEqualForEachField(result, { err: ERR.ACTRL.ACTRL_SIG_TIMEOUT })
      })

      it('Admin権限ではない署名の場合、取得したprovider情報が初期値であること', async () => {
        const result = await remigrationFuncs.backupProvider({
          remigration: remigrationBackup,
          options: { eoaKey: BASE.EOA.PROV1 },
        })
        utils.assertEqualForEachField(result, { err: ERR.ACTRL.ACTRL_BAD_ROLE })
      })
    })
  })
})
