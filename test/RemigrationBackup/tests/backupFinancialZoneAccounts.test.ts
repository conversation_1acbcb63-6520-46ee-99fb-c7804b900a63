import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  IssuerInstance,
  ProviderInstance,
  RemigrationBackupInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { remigrationFuncs } from '@test/RemigrationBackup/helpers/function'
import { RemigrationBackupContractType } from '@test/RemigrationBackup/helpers/types'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'
import { PromiseType } from 'utility-types'

// Chai global vars
declare let assert: Chai.Assert

describe('backupFinancialZoneAccounts()', () => {
  let accounts: SignerWithAddress[]
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let token: TokenInstance
  let remigrationBackup: RemigrationBackupInstance

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, token, remigrationBackup } =
      await contractFixture<RemigrationBackupContractType>())
  }

  const setupBasicRoles = async () => {
    await providerFuncs.addProvider({ provider, accounts })
    await providerFuncs.addProviderRole({ provider, accounts })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await validatorFuncs.addValidatorRole({ validator, accounts })
    await providerFuncs.addToken({ provider, accounts })
  }

  const setupAccountsData = async (accountParams, deadline: number) => {
    for (let i = 0; i < accountParams.length; i++) {
      await validatorFuncs.addAccount({
        validator,
        accounts,
        options: {
          validatorId: BASE.VALID.VALID0.ID,
          accountId: accountParams[i].accountId,
          accountName: accountParams[i].accountName,
        },
      })
      await issuerFuncs.addAccountRole({
        issuer,
        accounts,
        options: {
          issuerId: BASE.ISSUER.ISSUER0.ID,
          accountId: accountParams[i].accountId,
          accountEoa: accountParams[i].accountEoa,
          deadline: deadline + 60,
        },
      })
      await tokenFuncs.approve({
        token,
        accounts,
        spenderId: accountParams[i].accountApprovalAll[0].spenderId,
        amount: accountParams[i].accountApprovalAll[0].amount,
        options: {
          validatorId: BASE.VALID.VALID0.ID,
          ownerId: accountParams[i].accountId,
          deadline: deadline + 60,
        },
      })
    }
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('accountが登録されていない状態', () => {
      it('空リストが取得できること', async () => {
        const result = await remigrationFuncs.backupFinancialZoneAccounts({
          remigration: remigrationBackup,
          offset: 0,
          limit: 1000,
        })
        utils.assertEqualForEachField(result, {
          financialZoneAccounts: [],
          totalCount: 0,
          err: '',
        })
      })
    })

    describe('Accountsが登録されている状態', () => {
      const params = Object.values(BASE.ACCOUNT)
        .filter((v) => typeof v === 'object')
        .slice(0, 20)
      let createParams
      let accountParams

      const assertList = (
        result: PromiseType<ReturnType<typeof remigrationFuncs.backupFinancialZoneAccounts>>,
        expected: typeof accountParams,
      ) => {
        assert.strictEqual(result.financialZoneAccounts.length, expected.length, 'account count')
        expected.forEach((v, i) => {
          const account = result.financialZoneAccounts[i]
          const financialZoneAccountData = account.financialZoneAccountData
          const cumulativeTransactionLimits = financialZoneAccountData.cumulativeTransactionLimits
          assert.equal(account.accountId, v.accountId)
          assert.equal(financialZoneAccountData.mintLimit, BASE.LIMIT_VALUES.mint)
          assert.equal(financialZoneAccountData.burnLimit, BASE.LIMIT_VALUES.burn)
          assert.equal(financialZoneAccountData.chargeLimit, BASE.LIMIT_VALUES.charge)
          assert.equal(financialZoneAccountData.dischargeLimit, BASE.LIMIT_VALUES.discharge)
          assert.equal(financialZoneAccountData.transferLimit, BASE.LIMIT_VALUES.transfer)
          assert.equal(financialZoneAccountData.cumulativeLimit, BASE.LIMIT_VALUES.cumulative.total)
          assert.equal(financialZoneAccountData.cumulativeAmount, 0)
          assert.equal(financialZoneAccountData.cumulativeDate, 0)
          assert.equal(cumulativeTransactionLimits.cumulativeMintLimit, BASE.LIMIT_VALUES.cumulative.mint)
          assert.equal(cumulativeTransactionLimits.cumulativeMintAmount, 0)
          assert.equal(cumulativeTransactionLimits.cumulativeBurnLimit, BASE.LIMIT_VALUES.cumulative.burn)
          assert.equal(cumulativeTransactionLimits.cumulativeBurnAmount, 0)
          assert.equal(cumulativeTransactionLimits.cumulativeChargeLimit, BASE.LIMIT_VALUES.cumulative.charge)
          assert.equal(cumulativeTransactionLimits.cumulativeChargeAmount, 0)
          assert.equal(cumulativeTransactionLimits.cumulativeDischargeLimit, BASE.LIMIT_VALUES.cumulative.discharge)
          assert.equal(cumulativeTransactionLimits.cumulativeDischargeAmount, 0)
          assert.equal(cumulativeTransactionLimits.cumulativeTransferLimit, BASE.LIMIT_VALUES.cumulative.transfer)
          assert.equal(cumulativeTransactionLimits.cumulativeTransferAmount, 0)
        })
      }

      before(async () => {
        createParams = async (num: number) => {
          const createAccParams = Promise.all(
            [...Array(num).keys()].map(async (index) => {
              return {
                accountId: params[index].ID,
                accountName: params[index].NAME,
                accountEoa: await accounts[index].getAddress(),
                accountApprovalAll: [{ spenderId: params[index].ID, amount: 100 }],
              }
            }),
          )
          return createAccParams
        }
        accountParams = await createParams(params.length)
        const deadline = await utils.getDeadline()
        await setupBasicRoles()
        await setupAccountsData(accountParams, deadline)
      })

      it('offset0, limit20を指定した場合、1ページ目1項目目から20件取得できること', async () => {
        const offset = 0
        const limit = 20
        const result = await remigrationFuncs.backupFinancialZoneAccounts({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: params.length, err: '' })
        assertList(result, accountParams.slice(0, 20))
      })

      it('offset1, limit10を指定した場合、2項目目から10件取得できること', async () => {
        const offset = 1
        const limit = 10
        const result = await remigrationFuncs.backupFinancialZoneAccounts({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: params.length, err: '' })
        assertList(result, accountParams.slice(1, 11))
      })

      it('offset1, limit2を指定した場合、3項目目から2件取得できること', async () => {
        const offset = 2
        const limit = 2
        const result = await remigrationFuncs.backupFinancialZoneAccounts({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: params.length, err: '' })
        assertList(result, [accountParams[2], accountParams[3]])
      })

      it('最後の1件が取得できること', async () => {
        const offset = 19
        const limit = 1
        const result = await remigrationFuncs.backupFinancialZoneAccounts({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: params.length, err: '' })
        assertList(result, [accountParams[accountParams.length - 1]])
      })

      it('limitが取得上限(1000件)以下の場合、データが取得ができること', async () => {
        const offset = 0
        const limit = 1000
        const result = await remigrationFuncs.backupFinancialZoneAccounts({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: params.length, err: '' })
        assertList(result, accountParams)
      })

      it('limitが0の場合、空リストが取得できること', async () => {
        const offset = 2
        const limit = 0
        const result = await remigrationFuncs.backupFinancialZoneAccounts({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: '' })
        assertList(result, [])
      })

      it('limitが取得上限(1000件)より大きい場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 1001
        const result = await remigrationFuncs.backupFinancialZoneAccounts({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, {
          totalCount: 0,
          err: ERR.ACCOUNT.ACCOUNT_TOO_LARGE_LIMIT,
        })
      })

      it('offsetが登録されている件数以上の場合、エラーが返されること', async () => {
        const offset = 20
        const limit = 20
        const result = await remigrationFuncs.backupFinancialZoneAccounts({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, {
          totalCount: 0,
          err: ERR.ACCOUNT.ACCOUNT_OFFSET_OUT_OF_INDEX,
        })
      })

      it('Admin権限がない場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 20
        const result = await remigrationFuncs.backupFinancialZoneAccounts({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
          options: {
            eoaKey: 9,
          },
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACCOUNT.ACCOUNT_NOT_ADMIN })
      })

      it('署名無効の場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 20
        const result = await remigrationFuncs.backupFinancialZoneAccounts({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
          options: {
            sig: ['0x1234', ''],
          },
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACTRL.ACTRL_BAD_SIG })
      })

      it('署名期限切れの場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 20
        const now = await utils.getExceededDeadline()
        const result = await remigrationFuncs.backupFinancialZoneAccounts({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
          options: {
            deadline: now,
          },
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACTRL.ACTRL_SIG_TIMEOUT })
      })
    })
  })
})
