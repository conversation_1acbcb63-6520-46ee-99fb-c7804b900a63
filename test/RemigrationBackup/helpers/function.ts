import { BASE } from '@test/common/consts'
import { castReturnType, getDeadline } from '@test/common/utils'
import { RemigrationBackupFunctionType } from './types'
import privateKey from '@/privateKey'

export const remigrationFuncs: RemigrationBackupFunctionType = {
  version: ({ remigration }) => {
    return castReturnType(remigration.version())
  },
  backupValidators: async ({ remigration, offset, limit, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.GET_VALIDATORS_ALL } = options
    const _deadline = await getDeadline(deadline)
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return castReturnType(remigration.backupValidators(offset, limit, _deadline, _sig[0]))
  },
  restoreValidators: async ({ remigration, validators, accounts, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.SET_VALIDATORS_ALL } = options
    const _deadline = await getDeadline(deadline)
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return castReturnType(remigration.connect(accounts[9]).restoreValidators(validators, _deadline, _sig[0]))
  },
  backupProvider: async ({ remigration, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.GET_PROVIDER_ALL } = options
    const _deadline = await getDeadline(deadline)
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return castReturnType(remigration.backupProviders(_deadline, _sig[0]))
  },
  backupAccounts: async ({ remigration, offset, limit, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.GET_ACCOUNTS_ALL } = options
    const _deadline = await getDeadline(deadline)
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return castReturnType(remigration.backupAccounts(offset, limit, _deadline, _sig[0]))
  },
  backupFinancialZoneAccounts: async ({ remigration, offset, limit, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.GET_FINACCOUNTS_ALL } = options
    const _deadline = await getDeadline(deadline)
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return castReturnType(remigration.backupFinancialZoneAccounts(offset, limit, _deadline, _sig[0]))
  },
  backupIssuers: async ({ remigration, offset, limit, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.GET_ISSUERS_ALL } = options
    const _deadline = await getDeadline(deadline)
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return castReturnType(remigration.backupIssuers(offset, limit, _deadline, _sig[0]))
  },
  restoreFinancialZoneAccounts: async ({ remigration, finAccounts, accounts, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.SET_FINACCOUNTS_ALL } = options
    const _deadline = await getDeadline(deadline)
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return castReturnType(
      remigration.connect(accounts[9]).restoreFinancialZoneAccounts(finAccounts, _deadline, _sig[0]),
    )
  },
  restoreIssuers: async ({ remigration, issuers, accounts, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.SET_ISSUERS_ALL } = options
    const _deadline = await getDeadline(deadline)
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return castReturnType(remigration.connect(accounts[9]).restoreIssuers(issuers, _deadline, _sig[0]))
  },
  backupToken: async ({ remigration, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.GET_TOKEN_ALL } = options
    const _deadline = await getDeadline(deadline)
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return castReturnType(remigration.backupToken(_deadline, _sig[0]))
  },
  restoreToken: async ({ remigration, token, accounts, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.SET_TOKEN_ALL } = options
    const _deadline = await getDeadline(deadline)
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return castReturnType(remigration.connect(accounts[9]).restoreToken(token, _deadline, _sig[0]))
  },
  backupBusinessZoneAccounts: async ({ remigration, offset, limit, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.GET_BIZACCOUNTS_ALL } = options
    const _deadline = await getDeadline(deadline)
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return castReturnType(remigration.backupBusinessZoneAccounts(offset, limit, _deadline, _sig[0]))
  },
  restoreBusinessZoneAccounts: async ({ remigration, bizAccounts, accounts, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.SET_BIZACCOUNTS_ALL } = options
    const _deadline = await getDeadline(deadline)
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])
    return castReturnType(remigration.connect(accounts[9]).restoreBusinessZoneAccounts(bizAccounts, _deadline, _sig[0]))
  },
}
