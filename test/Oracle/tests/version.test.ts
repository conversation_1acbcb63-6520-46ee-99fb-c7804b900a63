import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { OracleInstance } from '@test/common/types'
import { OracleContractType } from '@test/Oracle/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('version()', () => {
  let oracle: OracleInstance

  const setupFixture = async () => {
    ;({ oracle } = await contractFixture<OracleContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('versionが返却されること', async () => {
        assert.equal(await oracle.version(), BASE.APP.VERSION)
      })
    })
  })
})
