import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { EventReturnType, OracleInstance } from '@test/common/types'

export type OracleContractType = {
  accounts: SignerWithAddress[]
  oracle: OracleInstance
}

type FuncParamsType = {
  get: { oracle: OracleInstance; prams: Parameters<OracleInstance['get']> }
  getBatch: { oracle: OracleInstance; prams: Parameters<OracleInstance['getBatch']> }
}

type FuncReturnType = {
  get: EventReturnType['Oracle']['Get']
  getBatch: EventReturnType['Oracle']['GetBatch']
}

export type OracleFunctionType = {
  [K in keyof FuncParamsType]: (args: FuncParamsType[K]) => Promise<FuncReturnType[K]>
}
