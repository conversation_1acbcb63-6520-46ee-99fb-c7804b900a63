import { castReturnType } from '@test/common/utils'
import { OracleFunctionType } from './types'

/**
 * oracleのイベントを呼ぶ関数を持つobject
 */
export const oracleFuncs: OracleFunctionType = {
  get: ({ oracle, prams }) => {
    return castReturnType(oracle.get(...prams))
  },
  getBatch: ({ oracle, prams }) => {
    return castReturnType(oracle.getBatch(...prams))
  },
  // 下記の関数はsignature等の生成が不要であり、共通化するメリットがないため未定義
  // version
  // addOracle
  // deleteOracle
  // set
  // setBatch
}
