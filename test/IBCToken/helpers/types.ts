import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccessCtrlInstance,
  AccountInstance,
  BusinessZoneAccountInstance,
  CheckAdminRoleOption,
  ContractCallOption,
  ContractManagerInstance,
  EventReturnType,
  IBCTokenInstance,
  IssuerInstance,
  IssueVoucherOption,
  ProviderInstance,
  RedeemVoucherOption,
  TokenInstance,
  TransferableMock1Instance,
  TransferableMock2Instance,
  TransferableMock3Instance,
  TransferFromEscrowOption,
  TransferProxyInstance,
  ValidatorInstance,
  SyncBusinessZoneBalanceOption,
} from '@test/common/types'
import { ContractTransactionResponse } from 'ethers'

export type IBCTokenContractType = {
  accounts: SignerWithAddress[]
  provider: ProviderInstance
  issuer: IssuerInstance
  validator: ValidatorInstance
  account: AccountInstance
  token: TokenInstance
  ibcToken: IBCTokenInstance
  transferProxy: TransferProxyInstance
  contractManager: ContractManagerInstance
  customTransfer1: TransferableMock1Instance
  customTransfer2: TransferableMock2Instance
  customTransfer3: TransferableMock3Instance
  accessCtrl: AccessCtrlInstance
  businessZoneAccount: BusinessZoneAccountInstance
}

type IBCTokenType = { ibcToken: IBCTokenInstance }

type BaseFromType = IBCTokenType & {
  from: SignerWithAddress
}

type BaseAmountType = BaseFromType & {
  amount: number
}

export type FuncParamsType = {
  version: IBCTokenType
  checkAdminRole: IBCTokenType & {
    accounts: SignerWithAddress[]
    options?: Partial<CheckAdminRoleOption & ContractCallOption>
  }
  transferFromEscrow: BaseAmountType & {
    options?: Partial<TransferFromEscrowOption>
  }
  transferToEscrow: BaseAmountType & {
    options: Partial<TransferFromEscrowOption>
  }
  syncBusinessZoneBalance: BaseFromType & {
    prams: SyncBusinessZoneBalanceOption
  }
  initAccountBalance: BaseFromType & {
    accountId: string
  }
  redeemVoucher: BaseAmountType & {
    options?: Partial<RedeemVoucherOption>
  }
  issueVoucher: BaseAmountType & {
    options?: Partial<IssueVoucherOption>
  }
}

export type IbcTokenFunctionType = {
  version: (args: FuncParamsType['version']) => Promise<string>
  checkAdminRole: (args: FuncParamsType['checkAdminRole']) => Promise<EventReturnType['IBCToken']['CheckAdminRole']>
  transferFromEscrow: (args: FuncParamsType['transferFromEscrow']) => Promise<ContractTransactionResponse>
  transferToEscrow: (args: FuncParamsType['transferToEscrow']) => Promise<ContractTransactionResponse>
  syncBusinessZoneBalance: (args: FuncParamsType['syncBusinessZoneBalance']) => Promise<ContractTransactionResponse>
  initAccountBalance: (args: FuncParamsType['initAccountBalance']) => Promise<ContractTransactionResponse>
  redeemVoucher: (args: FuncParamsType['redeemVoucher']) => Promise<ContractTransactionResponse>
  issueVoucher: (args: FuncParamsType['issueVoucher']) => Promise<ContractTransactionResponse>
}
