import privateKey from '@/privateKey'
import { BASE } from '@test/common/consts'
import { castReturnType, getDeadline } from '@test/common/utils'
import Web3 from 'web3'

declare let web3: Web3

import { IbcTokenFunctionType } from './types'

/**
 * ibcTokenのイベントを呼ぶ関数を持つobject
 */
export const ibcTokenFuncs: IbcTokenFunctionType = {
  version: ({ ibcToken }) => {
    return castReturnType(ibcToken.version())
  },
  checkAdminRole: async ({ ibcToken, accounts, options = {} }) => {
    const { deadline, sig, eoaKey = BASE.EOA.ADMIN, tokenId } = options
    const _tokenId = tokenId ?? '0x3000' + web3.utils.randomHex(16).slice(2)
    const dstZoneID = _tokenId.slice(0, 6)
    const userID = 300
    const _deadline = await getDeadline(deadline)
    const signer = privateKey.key[eoaKey]
    const _sig =
      sig ??
      privateKey.sig(signer, ['bytes32', 'uint256', 'uint256', 'uint256'], [_tokenId, dstZoneID, userID, _deadline])

    return castReturnType(ibcToken.connect(accounts[0]).checkAdminRole(_sig[1], _deadline, _sig[0]))
  },
  transferFromEscrow: async ({ ibcToken, from, amount, options = {} }) => {
    const {
      sendAccountId = BASE.ACCOUNT.ACCOUNT0.ID,
      fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID,
      toAccountId = BASE.ACCOUNT.ACCOUNT1.ID,
    } = options
    return castReturnType(
      ibcToken
        .connect(from)
        .transferFromEscrow(BASE.ZONE_ID.ID1, sendAccountId, fromAccountId, toAccountId, amount, BASE.TRACE_ID),
    )
  },
  transferToEscrow: async ({ ibcToken, from, amount, options = {} }) => {
    const {
      fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID,
      toAccountId = BASE.ACCOUNT.ACCOUNT1.ID,
      zoneId = BASE.ZONE_ID.ID1,
    } = options
    return castReturnType(
      ibcToken.connect(from).transferToEscrow(zoneId, fromAccountId, toAccountId, amount, BASE.TRACE_ID),
    )
  },
  syncBusinessZoneBalance: async ({ ibcToken, from, prams }) => {
    return castReturnType(ibcToken.connect(from).syncBusinessZoneBalance(prams))
  },
  initAccountBalance: async ({ ibcToken, from, accountId }) => {
    return castReturnType(ibcToken.connect(from).initAccountBalance(accountId))
  },
  redeemVoucher: async ({ ibcToken, from, amount, options = {} }) => {
    const { accountId = BASE.ACCOUNT.ACCOUNT0.ID } = options
    return castReturnType(ibcToken.connect(from).redeemVoucher(accountId, amount, BASE.TRACE_ID))
  },
  issueVoucher: async ({ ibcToken, from, amount, options = {} }) => {
    const { accountId = BASE.ACCOUNT.ACCOUNT0.ID } = options
    return castReturnType(ibcToken.connect(from).issueVoucher(accountId, amount, BASE.TRACE_ID))
  },
}
