import { BASE } from '@test/common/consts'
import { IBCTokenInstance } from '@test/common/types'
import { ibcTokenFuncs } from '@test/IBCToken/helpers/function'
import '@nomicfoundation/hardhat-chai-matchers'
import { contractFixture } from '@test/common/contractFixture'
import { IBCTokenContractType } from '@test/IBCToken/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('version()', () => {
  let ibcToken: IBCTokenInstance

  const setupFixture = async () => {
    ;({ ibcToken } = await contractFixture<IBCTokenContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    it('versionが返されること', async () => {
      assert.equal(await ibcTokenFuncs.version({ ibcToken }), BASE.APP.VERSION, 'version')
    })
  })
})
