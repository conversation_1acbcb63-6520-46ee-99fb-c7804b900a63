import '@nomicfoundation/hardhat-chai-matchers'

describe('Account', () => {
  require('./tests/version.test')
  require('./tests/initialize.test')
  require('./tests/addAccount.test')
  require('./tests/modAccount.test')
  require('./tests/addAccountRole.test')
  require('./tests/setAccountStatus.test')
  require('./tests/setTerminated.test')
  require('./tests/addZone.test')
  require('./tests/approve.test')
  require('./tests/getAccountLimit.test')
  require('./tests/getAllowance.test')
  require('./tests/getAllowanceList.test')
  require('./tests/mint.test')
  require('./tests/burn.test')
  require('./tests/calcBalance.test')
  require('./tests/calcAllowance.test')
  require('./tests/editBalance.test')
  require('./tests/hasAccount.test')
  require('./tests/getAccount.test')
  require('./tests/getAccountCount.test')
  require('./tests/getDestinationAccount.test')
  require('./tests/getAccountAll.test')
  require('./tests/getAccountId.test')
  require('./tests/getZoneByAccountId.test')
  require('./tests/isFrozen.test')
  require('./tests/isTerminated.test')
  require('./tests/balanceOf.test')
  require('./tests/forceBurn.test')
  require('./tests/partialForceBurn.test')
  require('./tests/emitAfterBalance.test')

  // describe('getAccountsAll()', () => {
  //   describe('正常系', () => {
  //     before(async () => {
  //       [provider, issuer, validator, account, token] = await contractFixture<AccountContractType>();
  //     });

  //     describe('accountが登録されていない状態', () => {
  //       it('空リストが取得できること', async () => {
  //         const result = await accountFuncs.getAccountsAll(account, 0, 1000);
  //         utils.assertEqualForEachField(result, {
  //           accounts: [],
  //           totalCount: 0,
  //           err: '',
  //         });
  //       });
  //     });

  //     describe('Accountsが登録されている状態', () => {
  //       const pramsAccounts = Object.values(BASE.ACCOUNT).slice(0, 20);
  //       const prams = pramsAccounts.map((v, i) => {
  //         return {
  //           accountId: v.ID,
  //           accountName: v.NAME,
  //           accountEoa: accounts[i],
  //           accountApprovalAll: [{ spenderId: pramsAccounts[i].ID, amount: 100 }],
  //         };
  //       });

  //       const assertList = (
  //         result: PromiseType<ReturnType<typeof accountFuncs.getAccountsAll>>,
  //         expected: typeof prams,
  //       ) => {
  //         assert.strictEqual(result.accounts.length, expected.length, 'account count');
  //         expected.forEach((v, i) => {
  //           utils.assertEqualForEachField(result.accounts[i], {
  //             accountId: v.accountId,
  //             accountName: v.accountName,
  //             accountStatus: BASE.STATUS.ACTIVE,
  //             zoneIds: [],
  //             balance: '0',
  //             reasonCode: BASE.REASON_CODE1,
  //             appliedAt: '0',
  //             terminatingAt: '0',
  //             terminatedAt: '0',
  //             accountIdExistence: true,
  //             accountEoa: v.accountEoa,
  //           });
  //           v.accountApprovalAll.forEach((v2, i2) => {
  //             utils.assertEqualForEachField(result.accounts[i].accountApprovalAll[i2], {
  //               spanderId: v2.spenderId,
  //               allowanceAmount: '100',
  //             });
  //           });
  //         });
  //       };

  //       before(async () => {
  //         const deadline = await utils.getDeadline();
  //         await providerFuncs.addProvider(provider, accounts);
  //         await providerFuncs.addProviderRole(provider, accounts);
  //         await issuerFuncs.addIssuer(issuer, accounts);
  //         await issuerFuncs.addIssuerRole(issuer, accounts);
  //         await validatorFuncs.addValidator(validator, accounts);
  //         await validatorFuncs.addValidatorRole(validator, accounts);
  //         await providerFuncs.addToken(provider, accounts);

  //         await Promise.all(
  //           prams.map(async (v, i) => {
  //             await validatorFuncs.addAccount(validator, accounts, {
  //               validatorId: BASE.VALID.VALID0.ID,
  //               accountId: v.accountId,
  //               accountName: v.accountName,
  //             });
  //             await issuerFuncs.addAccountRole(issuer, accounts, {
  //               issuerId: BASE.ISSUER.ISSUER0.ID,
  //               accountId: v.accountId,
  //               accountEoa: v.accountEoa,
  //               deadline: deadline + 60,
  //             });
  //             await tokenFuncs.approve(
  //               token,
  //               accounts,
  //               v.accountApprovalAll[0].spenderId,
  //               v.accountApprovalAll[0].amount,
  //               {
  //                 validatorId: BASE.VALID.VALID0.ID,
  //                 ownerId: v.accountId,
  //                 deadline: deadline + 60,
  //               },
  //             );
  //           }),
  //         );
  //       });

  //       it('offset0, limit10を指定した場合、1ページ目1項目目から10件取得できること', async () => {
  //         const offset = 0;
  //         const limit = 10;
  //         const result = await accountFuncs.getAccountsAll(account, offset, limit);
  //         utils.assertEqualForEachField(result, { totalCount: prams.length, err: '' });
  //         assertList(result, prams.slice(0, 10));
  //       });

  //       it('offset1, limit10を指定した場合、2ページ目1項目目から10件取得できること', async () => {
  //         const offset = 1;
  //         const limit = 10;
  //         const result = await accountFuncs.getAccountsAll(account, offset, limit);
  //         utils.assertEqualForEachField(result, { totalCount: prams.length, err: '' });
  //         assertList(result, prams.slice(10, 20));
  //       });

  //       it('offset1, limit2を指定した場合、2ページ目2項目目から2件取得できること', async () => {
  //         const offset = 2;
  //         const limit = 2;
  //         const result = await accountFuncs.getAccountsAll(account, offset, limit);
  //         utils.assertEqualForEachField(result, { totalCount: prams.length, err: '' });
  //         assertList(result, [prams[4], prams[5]]);
  //       });

  //       it('最後の1件が取得できること', async () => {
  //         const offset = 19;
  //         const limit = 1;
  //         const result = await accountFuncs.getAccountsAll(account, offset, limit);
  //         utils.assertEqualForEachField(result, { totalCount: prams.length, err: '' });
  //         assertList(result, [prams[prams.length - 1]]);
  //       });

  //       it('limitが取得上限(1000件)以下の場合、データが取得ができること', async () => {
  //         const offset = 0;
  //         const limit = 1000;
  //         const result = await accountFuncs.getAccountsAll(account, offset, limit);
  //         utils.assertEqualForEachField(result, { totalCount: prams.length, err: '' });
  //         assertList(result, prams);
  //       });

  //       it('limitが0の場合、空リストが取得できること', async () => {
  //         const offset = 2;
  //         const limit = 0;
  //         const result = await accountFuncs.getAccountsAll(account, offset, limit);
  //         utils.assertEqualForEachField(result, { totalCount: prams.length, err: '' });
  //         assertList(result, []);
  //       });

  //       it('limitが取得上限(1000件)より大きい場合、エラーが返されること', async () => {
  //         const offset = 0;
  //         const limit = 1001;
  //         const result = await accountFuncs.getAccountsAll(account, offset, limit);
  //         utils.assertEqualForEachField(result, {
  //           totalCount: prams.length,
  //           err: ERR.ACCOUNT.ACCOUNT_TOO_LARGE_LIMIT,
  //         });
  //       });

  //       it('offsetが登録されている件数以上の場合、エラーが返されること', async () => {
  //         const offset = 20;
  //         const limit = 20;
  //         const result = await accountFuncs.getAccountsAll(account, offset, limit);
  //         utils.assertEqualForEachField(result, {
  //           totalCount: prams.length,
  //           err: ERR.ACCOUNT.ACCOUNT_OFFSET_OUT_OF_INDEX,
  //         });
  //       });

  //       it('Admin権限がない場合、エラーが返されること', async () => {
  //         const offset = 0;
  //         const limit = 20;
  //         const result = await accountFuncs.getAccountsAll(account, offset, limit, { eoaKey: 9 });
  //         utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACTRL.ACTRL_BAD_ROLE });
  //       });

  //       it('署名無効の場合、エラーが返されること', async () => {
  //         const offset = 0;
  //         const limit = 20;
  //         const result = await accountFuncs.getAccountsAll(account, offset, limit, { sig: ['0x1234', ''] });
  //         utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACTRL.ACTRL_BAD_SIG });
  //       });

  //       it('署名期限切れの場合、エラーが返されること', async () => {
  //         const offset = 0;
  //         const limit = 20;
  //         const now = await utils.getExceededDeadline();
  //         const result = await accountFuncs.getAccountsAll(account, offset, limit, { deadline: now });
  //         utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACTRL.ACTRL_SIG_TIMEOUT });
  //       });
  //     });
  //   });
  // });

  // describe('setAccountsAll()', () => {
  //   const createParams = (accounts: any, num: number) =>
  //     [...Array(num).keys()].map((index) => {
  //       return {
  //         accountId: toBytes32(`x${index}`),
  //         accountName: toBytes32(`NAME${index}`),
  //         accountStatus: BASE.STATUS.ACTIVE,
  //         zoneIds: ['3000', '4000'],
  //         balance: 100,
  //         reasonCode: BASE.REASON_CODE2,
  //         appliedAt: ********,
  //         registeredAt: ********,
  //         terminatingAt: ********,
  //         terminatedAt: ********,
  //         accountIdExistence: true,
  //         accountEoa: accounts[index],
  //         accountApprovalAll: [
  //           {
  //             spanderId: toBytes32(`SPANDER${index}_ID1`),
  //             spenderAccountName: toBytes32(`SPENDER${index}_NAME1`),
  //             allowanceAmount: 300,
  //             approvedAt: ********,
  //           },
  //           {
  //             spanderId: toBytes32(`SPANDER${index}_ID2`),
  //             spenderAccountName: toBytes32(`SPANDER${index}_NAME2`),
  //             allowanceAmount: 400,
  //             approvedAt: ********,
  //           },
  //         ],
  //       };
  //     });
  //   describe('正常系', () => {
  //     before(async () => {
  //       [, , , account] = await contractFixture<AccountContractType>();
  //     });

  //     describe('初期状態', () => {
  //       const accountsPram = createParams(accounts, 20);
  //       const assertList = (
  //         result: PromiseType<ReturnType<typeof accountFuncs.getAccountsAll>>,
  //         expected: typeof accountsPram,
  //       ) => {
  //         assert.strictEqual(result.accounts.length, expected.length, 'accounts count');
  //         expected.forEach((v, i) => {
  //           utils.assertEqualForEachField(result.accounts[i], {
  //             accountId: v.accountId,
  //             accountName: v.accountName,
  //             accountStatus: v.accountStatus,
  //             balance: String(v.balance),
  //             reasonCode: String(v.reasonCode),
  //             appliedAt: v.appliedAt,
  //             registeredAt: v.registeredAt,
  //             terminatingAt: v.terminatingAt,
  //             terminatedAt: v.terminatedAt,
  //             accountIdExistence: v.accountIdExistence,
  //             accountEoa: v.accountEoa,
  //           });
  //           v.zoneIds.forEach((v2, i2) => {
  //             assert.strictEqual(result.accounts[i].zoneIds[i2], v2);
  //           });
  //           v.accountApprovalAll.forEach((v2, i2) => {
  //             utils.assertEqualForEachField(result.accounts[i].accountApprovalAll[i2], {
  //               spanderId: v2.spanderId,
  //               spenderAccountName: v2.spenderAccountName,
  //               allowanceAmount: String(v2.allowanceAmount),
  //               approvedAt: v2.approvedAt,
  //             });
  //           });
  //         });
  //       };
  //       it('全てのissuers(20件)が登録できること', async () => {
  //         await accountFuncs.setAccountsAll(account, accountsPram);
  //         const result = await accountFuncs.getAccountsAll(account, 0, 1000);
  //         assertList(result, accountsPram);
  //       });

  //       // 「一括登録の上限数を超えた場合、エラーがスローされること」のテストは
  //       // gasの上限に達してエラーになるため未実施(Error: Returned error: base fee exceeds gas limit)
  //     });
  //   });

  //   describe('準正常系', () => {
  //     const accountsPram = createParams(accounts, 20);

  //     before(async () => {
  //       [, , , account] = await contractFixture<AccountContractType>();
  //     });

  //     describe('初期状態', () => {
  //       it('Admin権限がない場合、エラーがスローされること', async () => {
  //         await truffleAssert.reverts(
  //           accountFuncs.setAccountsAll(account, accountsPram, { eoaKey: BASE.EOA.ISSUER1 }),
  //           ERR.ACTRL.ACTRL_BAD_ROLE,
  //         );
  //       });

  //       it('署名無効の場合、エラーがスローされること', async () => {
  //         await truffleAssert.reverts(
  //           accountFuncs.setAccountsAll(account, accountsPram, { sig: ['0x1234', ''] }),
  //           ERR.ACTRL.ACTRL_BAD_SIG,
  //         );
  //       });

  //       it('署名期限切れの場合、エラーがスローされること', async () => {
  //         const now = await utils.getExceededDeadline();
  //         await truffleAssert.reverts(
  //           accountFuncs.setAccountsAll(account, accountsPram, { deadline: now }),
  //           ERR.ACTRL.ACTRL_SIG_TIMEOUT,
  //         );
  //       });

  //       it('異常な値が入力された時にfails', async () => {
  //         const accountsIvalid = [
  //           {
  //             accountId: '123',
  //             accountName: 'name',
  //             accountStatus: '123',
  //             zoneIds: [],
  //             balance: '456',
  //             reasonCode: true,
  //             appliedAt: ********,
  //             registeredAt: ********,
  //             terminatingAt: ********,
  //             terminatedAt: ********,
  //             accountIdExistence: true,
  //             accountEoa: '',
  //             accountApprovalAll: [],
  //           },
  //         ];
  //         await truffleAssert.fails(accountFuncs.setAccountsAll(account, accountsIvalid));
  //       });
  //     });
  //   });
  // });
})
