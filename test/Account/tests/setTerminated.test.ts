import { AccountContractType } from '@/test/Account/helpers/types'
import { contractFixture } from '@/test/common/contractFixture'
import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { BASE, ERR } from '@test/common/consts'
import { AccountInstance, ValidatorInstance } from '@test/common/types'
import { expect } from 'chai'
import { ethers } from 'hardhat'
import { before } from 'mocha'

describe('setTerminated()', () => {
  let validator: ValidatorInstance
  let account: AccountInstance
  let accounts: SignerWithAddress[]
  let fakeValidator

  const setupFixture = async () => {
    ;({ accounts, validator, account } = await contractFixture<AccountContractType>())
  }

  const initData = async () => {
    await helpers.impersonateAccount(await validator.getAddress())
    await helpers.setBalance(await validator.getAddress(), 100n ** 18n)
    fakeValidator = await ethers.getSigner(await validator.getAddress())
  }

  // setTerminated のテストは validator.setTerminated から呼ばれるため validator のテストで実施する, ここでは呼び出し元検証のみ行う
  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('呼び出し元がValidatorではない場合、エラーがスローされること', async () => {
        await expect(
          account.connect(accounts[1]).setTerminated(BASE.ACCOUNT.ACCOUNT0.ID, BASE.REASON_CODE1, BASE.TRACE_ID), // validatorコントラクト以外をfromに設定する
        ).to.be.revertedWith(ERR.VALID.NOT_VALIDATOR_CONTRACT)
      })
    })

    describe('fake call from validator to setTerminated', () => {
      before(async () => {
        await initData()
      })

      it('should revert when accountId is not registered and call from fake validator', async () => {
        // Hack: Fake validator can call setTerminated
        // UNREACHABLE false case: This test only for coverage, not for real case
        await expect(
          account.connect(fakeValidator).setTerminated(BASE.ACCOUNT.ACCOUNT1.ID, BASE.REASON_CODE1, BASE.TRACE_ID), // Fake validator call
        ).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })

      it('should revert when accountId is not valid and call from fake validator', async () => {
        // Hack: Fake validator can call setTerminated
        // UNREACHABLE false case: This test only for coverage, not for real case
        await expect(
          account.connect(fakeValidator).setTerminated(BASE.ACCOUNT.EMPTY.ID, BASE.REASON_CODE1, BASE.TRACE_ID), // Fake validator call
        ).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_INVALID_VAL)
      })
    })
  })
})
