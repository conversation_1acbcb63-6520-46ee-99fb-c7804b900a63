import { AccountContractType } from '@/test/Account/helpers/types'
import { contractFixture } from '@/test/common/contractFixture'
import '@nomicfoundation/hardhat-chai-matchers'
import { BASE, ERR } from '@test/common/consts'
import { AccountInstance } from '@test/common/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('getAccountLimit()', () => {
  let account: AccountInstance

  const setupFixture = async () => {
    ;({ account } = await contractFixture<AccountContractType>())
  }

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('directly call getAccountLimit when hasValidator false', () => {
      // #TODO: main contract commented require call from validator
      // This test should be updated when main contract is updated
      // UNREACHABLE: cannot reach case success=false from validator contract
      // This test commented required call and don't need permission but this test is only for coverage
      it('should return zero data and error when hasValidator false', async () => {
        const result = await account.getAccountLimit(BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT0.ID)
        assert.equal(result.err, ERR.VALID.VALIDATOR_ID_NOT_EXIST, 'err')
      })

      it('should return zero data and error data when invalid validator', async () => {
        const result = await account.getAccountLimit(BASE.VALID.EMPTY.ID, BASE.ACCOUNT.ACCOUNT0.ID)
        assert.equal(result.err, ERR.VALID.VALIDATOR_INVALID_VAL, 'err')
      })
    })
  })
})
