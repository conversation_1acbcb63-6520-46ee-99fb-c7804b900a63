import { AccountContractType } from '@/test/Account/helpers/types'
import { contractFixture } from '@/test/common/contractFixture'
import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { AccountInstance } from '@test/common/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('addZone()', () => {
  let account: AccountInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, account } = await contractFixture<AccountContractType>())
  }

  // addZone のテストは provider.addZone から呼ばれるため provider のテストで実施する, ここでは呼び出し元検証のみ行う
  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('should revert when account is not registered', async () => {
        const result = account.connect(accounts[1]).addZone(BASE.ACCOUNT.ACCOUNT1.ID, BASE.ZONE_ID.ID0, BASE.TRACE_ID)
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })
    })
  })
})
