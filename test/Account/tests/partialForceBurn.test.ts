import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { AccountContractType } from '@test/Account/helpers/types'
import { businessZoneAccountFuncs } from '@test/BusinessZoneAccount/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  AccountInstance,
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { assertEqualForEachField } from '@test/common/utils'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { ibcTokenFuncs } from '@test/IBCToken/helpers/function'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { ethers } from 'hardhat'
import { before } from 'mocha'

describe('partialForceBurn()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let account: AccountInstance
  let token: TokenInstance
  let contractManager: ContractManagerInstance
  let ibcToken: IBCTokenInstance
  let accounts: SignerWithAddress[]
  let businessZoneAccount: BusinessZoneAccountInstance
  let fakeIssuer: SignerWithAddress
  let ibcAddress: SignerWithAddress

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, account, token, ibcToken, contractManager, businessZoneAccount } =
      await contractFixture<AccountContractType>())
  }

  const setupFakeIssuer = async () => {
    await helpers.impersonateAccount(await issuer.getAddress())
    await helpers.setBalance(await issuer.getAddress(), 100n ** 18n)
    fakeIssuer = await ethers.getSigner(await issuer.getAddress())
    ibcAddress = accounts[0]
  }

  const setupBasicRoles = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({ provider, accounts })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await providerFuncs.addToken({ provider, accounts })
  }

  const setupAccounts = async () => {
    await validatorFuncs.addAccount({
      validator,
      accounts,
      options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
    })

    await tokenFuncs.mint({
      token,
      accounts,
      amount: 450,
      options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
    })
  }

  const setupIbcApps = async () => {
    const ibcAddressString = await ibcAddress.getAddress()
    await contractManagerFuncs.setIbcApp({
      contractManager,
      accounts,
      ibcAddress: ibcAddressString,
      ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
    })
    await contractManagerFuncs.setIbcApp({
      contractManager,
      accounts,
      ibcAddress: ibcAddressString,
      ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
    })
  }

  const setupBusinessZone = async () => {
    await providerFuncs.addBizZone({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID1,
        zoneName: BASE.ZONE_NAME.NAME1,
      },
    })

    await businessZoneAccountFuncs.syncBusinessZoneStatus({
      businessZoneAccount,
      accounts,
      options: {
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
        zoneId: BASE.ZONE_ID.ID1,
      },
    })

    await validatorFuncs.setActiveBusinessAccountWithZone({
      validator,
      accounts,
      options: {
        validatorId: BASE.VALID.VALID0.ID,
        zoneId: BASE.ZONE_ID.ID1,
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
      },
    })
  }

  const setupAccountStatus = async () => {
    await issuerFuncs.setAccountStatus({
      issuer,
      accounts,
      accountStatus: BASE.STATUS.FROZEN,
      options: {
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
      },
    })
  }

  const setupEscrowBalance = async () => {
    await ibcTokenFuncs.transferToEscrow({
      ibcToken,
      from: ibcAddress,
      amount: 150,
      options: {
        fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
        toAccountId: BASE.ACCOUNT.ESCROW.ID,
      },
    })
  }

  const setupNormalData = async () => {
    await setupFakeIssuer()
    await setupBasicRoles()
    await setupAccounts()
    await setupIbcApps()
    await setupBusinessZone()
    await setupAccountStatus()
    await setupEscrowBalance()
  }

  const setupNotNormalData = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: { zoneId: BASE.ZONE_ID.ID1 },
    })
    await providerFuncs.addProviderRole({
      provider,
      accounts,
      options: {
        providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
      },
    })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
    await validatorFuncs.addAccount({ validator, accounts })

    await tokenFuncs.mint({
      token,
      accounts,
      amount: 300,
      options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
    })

    await issuerFuncs.setAccountStatus({
      issuer,
      accounts,
      accountStatus: BASE.STATUS.FROZEN,
      options: {
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
      },
    })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
      await setupNormalData()
    })

    describe('fin Account残高が300で、biz Account残高が150の場合', () => {
      it('fin Account残高が指定したburnedAmountより大きい場合、fin Account残高のみが更新されること,totalSupplyが減算されること', async () => {
        // 強制償却前のTotalSupplyを取得
        const tokenBefore = await providerFuncs.getToken({ provider, options: [BASE.PROV.PROV0.ID] })

        const beforeBurn = await validatorFuncs.getAccountAll({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        expect(beforeBurn.accountDataAll.balance).to.equal('300')
        expect(beforeBurn.accountDataAll.accountStatus).to.equal(BASE.STATUS.FROZEN)
        expect(beforeBurn.accountDataAll.businessZoneAccounts[0].balance).to.equal('150')

        const tx = await account
          .connect(fakeIssuer)
          .partialForceBurn(BASE.ACCOUNT.ACCOUNT1.ID, BASE.TEST_BURN.AMOUNT_100, 200, BASE.TRACE_ID)

        const totalBalance = BASE.TEST_BALANCES.BALANCE_450 // 300 (FinZone) + 150 (BizZone)
        await expect(tx)
          .to.emit(account, 'ForceBurn')
          .withArgs(
            BASE.VALID.VALID0.ID,
            BASE.ACCOUNT.ACCOUNT1.ID,
            BASE.TRACE_ID,
            totalBalance,
            BASE.TEST_BALANCES.AMOUNT_100,
            BASE.TEST_BALANCES.AMOUNT_200,
            (forceDischarge) => {
              expect(forceDischarge).to.be.an('array')
              return true
            },
          )
          .to.emit(account, 'AfterBalance')
          .withArgs(
            [
              [BASE.ZONE_ID.ID0, BigInt(BASE.TEST_BALANCES.AMOUNT_200)], // FinZone balance after burn
              [BASE.ZONE_ID.ID1, BigInt(BASE.TEST_BALANCES.BALANCE_150)], // BizZone balance remains
            ],
            [[BASE.ZONE_ID.ID0, 0n]], // toBalance (bytes32(0) returns zone balance of 0)
            BASE.TRACE_ID,
          )

        const afterBurn = await validatorFuncs.getAccountAll({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        expect(afterBurn.accountDataAll.balance).to.equal('200')
        expect(afterBurn.accountDataAll.accountStatus).to.equal(BASE.STATUS.FROZEN)
        expect(afterBurn.accountDataAll.businessZoneAccounts[0].balance).to.equal('150')

        const tokenAfter = await providerFuncs.getToken({ provider, options: [BASE.PROV.PROV0.ID] })

        // 強制償却した額の分だけtotalSupplyが減算されていること
        assertEqualForEachField(tokenAfter, {
          totalSupply: Number(tokenBefore.totalSupply) - BASE.TEST_BURN.AMOUNT_100,
        })
      })

      it('fin Account残高が指定したburnedAmountより小さく、total残高が大きい場合、biz Account残高が全て消却されること,totalSupplyが減算されること', async () => {
        const tokenBefore = await providerFuncs.getToken({ provider, options: [BASE.PROV.PROV0.ID] })

        const beforeBurn = await validatorFuncs.getAccountAll({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        expect(beforeBurn.accountDataAll.balance).to.equal('200')
        expect(beforeBurn.accountDataAll.businessZoneAccounts[0].balance).to.equal('150')

        const tx = await account
          .connect(fakeIssuer)
          .partialForceBurn(BASE.ACCOUNT.ACCOUNT1.ID, BASE.TEST_BURN.AMOUNT_250, 100, BASE.TRACE_ID)

        const totalBalance = BASE.TEST_BALANCES.AMOUNT_200 + BASE.TEST_BALANCES.BALANCE_150 // 200 (FinZone) + 150 (BizZone)
        await expect(tx)
          .to.emit(account, 'ForceBurn')
          .withArgs(
            BASE.VALID.VALID0.ID,
            BASE.ACCOUNT.ACCOUNT1.ID,
            BASE.TRACE_ID,
            totalBalance,
            BASE.TEST_BALANCES.AMOUNT_250,
            BASE.TEST_BALANCES.AMOUNT_100,
            (forceDischarge) => {
              const data = forceDischarge.map((data) => ({
                zoneId: data[0],
                dischargeAmount: data[1],
              }))
              expect(data).to.deep.equal([
                {
                  zoneId: BASE.ZONE_ID.ID1,
                  dischargeAmount: BASE.TEST_BALANCES.BALANCE_150,
                },
              ])
              return true
            },
          )
          .to.emit(account, 'AfterBalance')
          .withArgs(
            [
              [BASE.ZONE_ID.ID0, BigInt(BASE.TEST_BALANCES.AMOUNT_100)], // FinZone balance after burn
              [BASE.ZONE_ID.ID1, 0n], // BizZone balance fully discharged
            ],
            [[BASE.ZONE_ID.ID0, 0n]], // toBalance (bytes32(0) returns zone balance of 0)
            BASE.TRACE_ID,
          )

        const afterBurn = await validatorFuncs.getAccountAll({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        expect(afterBurn.accountDataAll.balance).to.equal('100')
        expect(afterBurn.accountDataAll.businessZoneAccounts[0].balance).to.equal('0')
        expect(afterBurn.accountDataAll.businessZoneAccounts[0].accountStatus).to.equal(BASE.STATUS.FORCE_BURNED)

        const tokenAfter = await providerFuncs.getToken({ provider, options: [BASE.PROV.PROV0.ID] })

        // FinZone側の強制償却額分だけtotalSupplyが減算されていること
        assertEqualForEachField(tokenAfter, {
          totalSupply: Number(tokenBefore.totalSupply) - BASE.TEST_BURN.AMOUNT_250,
        })
      })

      it('partialForceBurn実行後にAfterBalanceイベントが複数ゾーンで正しく発行されること', async () => {
        // セットアップ: 複数ゾーンに残高を持つ新しいアカウントを作成
        // 競合を避けるため異なるアカウントIDを使用
        await validatorFuncs.addAccount({ validator, accounts, options: { accountId: BASE.ACCOUNT.ACCOUNT3.ID } })

        // FinZoneにミント
        await tokenFuncs.mint({
          token,
          accounts,
          amount: BASE.TEST_BALANCES.BALANCE_800,
          options: { accountId: BASE.ACCOUNT.ACCOUNT3.ID },
        })

        // BizZone1をセットアップ
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT3.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT3.ID,
          },
        })

        // BizZone1に転送
        await ibcTokenFuncs.transferToEscrow({
          ibcToken,
          from: ibcAddress,
          amount: BASE.TEST_BALANCES.AMOUNT_200,
          options: {
            fromAccountId: BASE.ACCOUNT.ACCOUNT3.ID,
            toAccountId: BASE.ACCOUNT.ESCROW.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })

        // BizZone2をセットアップ
        await providerFuncs.addBizZone({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID2,
            zoneName: BASE.ZONE_NAME.NAME2,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT3.ID,
            zoneId: BASE.ZONE_ID.ID2,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID2,
            accountId: BASE.ACCOUNT.ACCOUNT3.ID,
          },
        })

        // BizZone2に転送
        await ibcTokenFuncs.transferToEscrow({
          ibcToken,
          from: ibcAddress,
          amount: BASE.TEST_BALANCES.AMOUNT_100,
          options: {
            fromAccountId: BASE.ACCOUNT.ACCOUNT3.ID,
            toAccountId: BASE.ACCOUNT.ESCROW.ID,
            zoneId: BASE.ZONE_ID.ID2,
          },
        })

        // アカウントを凍結
        await issuerFuncs.setAccountStatus({
          issuer,
          accounts,
          accountStatus: BASE.STATUS.FROZEN,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT3.ID,
          },
        })

        // partialForceBurnを実行 - 総額800から650をバーン
        // FinZone: 500 (self-transfer doesn't deduct from FinZone)
        // BizZone1: 200
        // BizZone2: 100
        const totalBalanceBefore = BASE.TEST_BALANCES.BALANCE_800 // 500 (FinZone) + 200 (BizZone1) + 100 (BizZone2)
        const burnAmount = BASE.TEST_BALANCES.AMOUNT_650
        const burnedBalance = BASE.TEST_BALANCES.BALANCE_150 // Expected remaining balance (800 - 650)

        const tx = await account
          .connect(fakeIssuer)
          .partialForceBurn(BASE.ACCOUNT.ACCOUNT3.ID, burnAmount, burnedBalance, BASE.TRACE_ID)

        // イベントを検証
        await expect(tx)
          .to.emit(account, 'ForceBurn')
          .withArgs(
            BASE.VALID.VALID0.ID,
            BASE.ACCOUNT.ACCOUNT3.ID,
            BASE.TRACE_ID,
            totalBalanceBefore,
            burnAmount,
            burnedBalance,
            (forceDischarge: any) => {
              const data = forceDischarge.map((d: any) => ({ zoneId: d[0], dischargeAmount: d[1] }))
              // Should discharge all from BizZone1 (200) and BizZone2 (100)
              // FinZone: 500 -> 150 (350 burned)
              // BizZone1 + BizZone2: 300 (all discharged)
              expect(data).to.deep.equal([
                { zoneId: BigInt(BASE.ZONE_ID.ID1), dischargeAmount: BigInt(BASE.TEST_BALANCES.AMOUNT_200) },
                { zoneId: BigInt(BASE.ZONE_ID.ID2), dischargeAmount: BigInt(BASE.TEST_BALANCES.AMOUNT_100) },
              ])
              return true
            },
          )
          .to.emit(account, 'AfterBalance')
          .withArgs(
            [
              [BASE.ZONE_ID.ID0, BigInt(BASE.TEST_BALANCES.BALANCE_150)], // FinZone balance after burn
              [BASE.ZONE_ID.ID1, 0n], // BizZone1 balance after full discharge
              [BASE.ZONE_ID.ID2, 0n], // BizZone2 balance after full discharge
            ],
            [[BASE.ZONE_ID.ID0, 0n]], // toBalance (bytes32(0) returns zone balance of 0)
            BASE.TRACE_ID,
          )
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
      await setupNotNormalData()
    })

    describe('issuerRole, accountが登録されている状態', () => {
      it('Issuerコントラクト以外から呼び出した場合、エラーがスローされること', async () => {
        const result = account.partialForceBurn(BASE.ISSUER.ISSUER1.ID, BASE.ACCOUNT.ACCOUNT1.ID, 100, BASE.TRACE_ID)
        await expect(result).to.be.revertedWith(ERR.ISSUER.NOT_ISSUER_CONTRACT)
      })

      it('accountが存在しない場合、エラーがスローされること', async () => {
        const result = issuerFuncs.partialForceBurn({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER0.ID,
            accountId: BASE.ACCOUNT.ACCOUNT10.ID,
            burnedAmount: 100,
            burnedBalance: 200,
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })

      it('空accountIdを指定した場合、エラーがスローされること', async () => {
        const result = issuerFuncs.partialForceBurn({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER0.ID,
            accountId: BASE.ACCOUNT.EMPTY.ID,
            burnedAmount: 100,
            burnedBalance: 200,
          },
        })
        await expect(result).to.be.revertedWith(ERR.COMMON.INVALID_ACCOUNT_ID)
      })

      it('burnedAmountが残高より大きい場合、ACCOUNT_INVALID_BURNED_AMOUNTエラーがスローされること', async () => {
        const result = issuerFuncs.partialForceBurn({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER0.ID,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            burnedAmount: 1000,
            burnedBalance: 0,
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_INVALID_BURNED_AMOUNT)
      })

      it('burnedBalanceが実際の残高と一致しない場合、ACCOUNT_INVALID_BURNED_BALANCEエラーがスローされること', async () => {
        const result = issuerFuncs.partialForceBurn({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER0.ID,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            burnedAmount: 100,
            burnedBalance: 150,
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_INVALID_BURNED_BALANCE)
      })

      it('Accountが凍結状態でない場合、エラーがスローされること', async () => {
        await issuerFuncs.setAccountStatus({
          issuer,
          accounts,
          accountStatus: BASE.STATUS.ACTIVE,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })

        const result = issuerFuncs.partialForceBurn({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER0.ID,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            burnedAmount: 100,
            burnedBalance: 200,
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_NOT_FROZEN)
      })
    })
  })
})
