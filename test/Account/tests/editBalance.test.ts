import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { AccountContractType } from '@test/Account/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountInstance, TokenInstance } from '@test/common/types'
import { expect } from 'chai'
import { ethers } from 'hardhat'

describe('editBalance()', () => {
  let account: AccountInstance
  let token: TokenInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, account, token } = await contractFixture<AccountContractType>())
  }

  // editBalance のテストは token.editBalance から呼ばれるため token のテストで実施する, ここでは呼び出し元検証のみ行う
  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('呼び出し元がTokenではない場合、エラーがスローされること', async () => {
        await expect(
          account.connect(accounts[1]).editBalance(BASE.ACCOUNT.ACCOUNT0.ID, 1000, BASE.CALC_PATTERN.ADD),
        ).to.be.revertedWith(ERR.TOKEN.NOT_TOKEN_CONTRACT)
      })

      it('calcPattern is not 2 or 3, this function should return and pass test', async () => {
        // Get accountId balance
        const beforeAccount = await account.getAccount(BASE.ACCOUNT.ACCOUNT0.ID)

        // Impersonate token contract to reach final return case
        const tokenAddress = await token.getAddress()
        await helpers.setBalance(tokenAddress, 100n ** 18n)
        await helpers.impersonateAccount(tokenAddress)
        const fakeToken = await ethers.getSigner(tokenAddress)

        // Can not get return from non-view function, just need it to success
        await account.connect(fakeToken).editBalance(BASE.ACCOUNT.ACCOUNT0.ID, 1000, 3)

        // Balance should be the same
        const afterAccount = await account.getAccount(BASE.ACCOUNT.ACCOUNT0.ID)
        await expect(beforeAccount.accountData.balance).to.be.equal(afterAccount.accountData.balance)
        await helpers.stopImpersonatingAccount(tokenAddress)
      })
    })
  })
})
