import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { accountFuncs } from '@test/Account/helpers/function'
import { AccountContractType } from '@test/Account/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountInstance, IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import { assertEqualForEachField, toBytes32 } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'

describe('hasAccount()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let account: AccountInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, account } = await contractFixture<AccountContractType>())
  }

  const initData = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: { zoneId: BASE.ZONE_ID.ID1 },
    })
    await providerFuncs.addProviderRole({
      provider,
      accounts,
      options: {
        providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
      },
    })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
    await validatorFuncs.addAccount({ validator, accounts })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
      await initData()
    })

    describe('accountが登録されている状態', () => {
      it('アカウント存在がチェックする', async () => {
        const result = await accountFuncs.hasAccount({ account, params: [BASE.ACCOUNT.ACCOUNT1.ID] })
        assertEqualForEachField(result, { success: true, err: '' })
      })

      it('存在しないアカウントがチェックする場合エラー', async () => {
        const result = await accountFuncs.hasAccount({ account, params: [toBytes32('x110')] })
        assertEqualForEachField(result, { success: false, err: ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST })
      })

      it('空アカウントがチェックする場合エラー', async () => {
        const result = await accountFuncs.hasAccount({ account, params: [BASE.ACCOUNT.EMPTY.ID] })
        assertEqualForEachField(result, { success: false, err: ERR.ACCOUNT.ACCOUNT_INVALID_VAL })
      })
    })
  })
})
