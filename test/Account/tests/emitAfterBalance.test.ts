import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { AccountContractType } from '@test/Account/helpers/types'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountInstance, ProviderInstance, ValidatorInstance, IssuerInstance, TokenInstance } from '@test/common/types'
import { toBytes32 } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import type { AllBalanceDataStructOutput } from 'types/contracts/Account'

describe('emitAfterBalance()', () => {
  let account: AccountInstance
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let token: TokenInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ account, provider, issuer, validator, token, accounts } = await contractFixture<AccountContractType>())

      // テスト環境のセットアップ
      await providerFuncs.addProvider({
        provider,
        accounts,
        options: { providerId: BASE.PROV.PROV0.ID, zoneId: BASE.ZONE_ID.ID0, zoneName: BASE.ZONE_NAME.NAME0 },
      })
      await providerFuncs.addProviderRole({
        provider,
        accounts,
        options: { providerId: BASE.PROV.PROV0.ID, providerEoa: accounts[0].address },
      })
      await issuerFuncs.addIssuer({
        issuer,
        accounts,
        options: {
          issuerId: BASE.ISSUER.ISSUER0.ID,
          bankCode: BASE.ISSUER.ISSUER0.BANK_CODE,
          name: BASE.ISSUER.ISSUER0.NAME,
        },
      })
      await issuerFuncs.addIssuerRole({
        issuer,
        accounts,
        options: { issuerId: BASE.ISSUER.ISSUER0.ID, issuerEoa: accounts[0].address },
      })
      await validatorFuncs.addValidator({
        validator,
        accounts,
        options: { validatorId: BASE.VALID.VALID0.ID, name: BASE.VALID.VALID0.NAME, issuerId: BASE.ISSUER.ISSUER0.ID },
      })
      await validatorFuncs.addValidatorRole({
        validator,
        accounts,
        options: { validatorId: BASE.VALID.VALID0.ID, validatorEoa: accounts[0].address },
      })

      // addBizZoneToIssuerを先に呼び出す
      await issuerFuncs.addBizZoneToIssuer({
        issuer,
        accounts,
        options: { issuerId: BASE.ISSUER.ISSUER0.ID, zoneId: BASE.ZONE_ID.ID0 },
      })
      await providerFuncs.addToken({
        provider,
        accounts,
        options: {
          tokenId: BASE.TOKEN.TOKEN1.ID,
          name: BASE.TOKEN.TOKEN1.NAME,
          symbol: BASE.TOKEN.TOKEN1.SYMBOL,
          eoaKey: BASE.EOA.ADMIN,
        },
      })
    })

    describe('登録済みアカウントの場合', () => {
      before(async () => {
        // アカウントを作成
        await validatorFuncs.addAccount({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
            accountName: BASE.ACCOUNT.ACCOUNT0.NAME,
          },
        })
        await validatorFuncs.addAccount({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          },
        })

        // 残高を設定
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 1000,
          options: { issuerId: BASE.ISSUER.ISSUER0.ID, accountId: BASE.ACCOUNT.ACCOUNT0.ID, from: accounts[0] },
        })
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 500,
          options: { issuerId: BASE.ISSUER.ISSUER0.ID, accountId: BASE.ACCOUNT.ACCOUNT1.ID, from: accounts[0] },
        })
      })

      it('正常にイベントが発行されること', async () => {
        const fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const toAccountId = BASE.ACCOUNT.ACCOUNT1.ID
        const traceId = toBytes32('trace123')

        await expect(account.emitAfterBalance(fromAccountId, toAccountId, traceId))
          .to.emit(account, 'AfterBalance')
          .withArgs(
            (fromBalance: unknown) => {
              // fromBalanceは配列であることを確認
              return Array.isArray(fromBalance) && fromBalance.length > 0
            },
            (toBalance: unknown) => {
              // toBalanceは配列であることを確認
              return Array.isArray(toBalance) && toBalance.length > 0
            },
            traceId,
          )
      })

      it('同一アカウントでもイベントが発行されること', async () => {
        const accountId = BASE.ACCOUNT.ACCOUNT0.ID
        const traceId = toBytes32('trace_same')

        await expect(account.emitAfterBalance(accountId, accountId, traceId)).to.emit(account, 'AfterBalance')
      })
    })

    describe('未登録アカウントの場合', () => {
      it('存在しないアカウントでもエラーにならずイベントが発行されること', async () => {
        const nonExistentFromId = toBytes32('nonexistent_from')
        const nonExistentToId = toBytes32('nonexistent_to')
        const traceId = toBytes32('trace_nonexistent')

        await expect(account.emitAfterBalance(nonExistentFromId, nonExistentToId, traceId))
          .to.emit(account, 'AfterBalance')
          .withArgs(
            (fromBalance: unknown) => {
              // 未登録アカウントの場合、空配列またはFinZoneのみの配列
              return Array.isArray(fromBalance)
            },
            (toBalance: unknown) => {
              // 未登録アカウントの場合、空配列またはFinZoneのみの配列
              return Array.isArray(toBalance)
            },
            traceId,
          )
      })
    })
  })

  describe('アクセス制御', () => {
    before(async () => {
      ;({ account, accounts } = await contractFixture<AccountContractType>())
    })

    it('どのアドレスからでも呼び出し可能であること', async () => {
      const fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID
      const toAccountId = BASE.ACCOUNT.ACCOUNT1.ID
      const traceId = toBytes32('trace_any_caller')

      // 複数の異なるアドレスから呼び出し
      for (let i = 0; i < 3; i++) {
        await expect(account.connect(accounts[i]).emitAfterBalance(fromAccountId, toAccountId, traceId)).to.emit(
          account,
          'AfterBalance',
        )
      }
    })
  })

  describe('イベントデータの検証', () => {
    before(async () => {
      ;({ account, provider, issuer, validator, token, accounts } = await contractFixture<AccountContractType>())

      // 環境セットアップ
      await providerFuncs.addProvider({
        provider,
        accounts,
        options: { providerId: BASE.PROV.PROV0.ID, zoneId: BASE.ZONE_ID.ID0, zoneName: BASE.ZONE_NAME.NAME0 },
      })
      await providerFuncs.addProviderRole({
        provider,
        accounts,
        options: { providerId: BASE.PROV.PROV0.ID, providerEoa: accounts[0].address },
      })
      await issuerFuncs.addIssuer({
        issuer,
        accounts,
        options: {
          issuerId: BASE.ISSUER.ISSUER0.ID,
          bankCode: BASE.ISSUER.ISSUER0.BANK_CODE,
          name: BASE.ISSUER.ISSUER0.NAME,
        },
      })
      await issuerFuncs.addIssuerRole({
        issuer,
        accounts,
        options: { issuerId: BASE.ISSUER.ISSUER0.ID, issuerEoa: accounts[0].address },
      })
      await validatorFuncs.addValidator({
        validator,
        accounts,
        options: { validatorId: BASE.VALID.VALID0.ID, name: BASE.VALID.VALID0.NAME, issuerId: BASE.ISSUER.ISSUER0.ID },
      })
      await validatorFuncs.addValidatorRole({
        validator,
        accounts,
        options: { validatorId: BASE.VALID.VALID0.ID, validatorEoa: accounts[0].address },
      })
      await providerFuncs.addToken({
        provider,
        accounts,
        options: {
          tokenId: BASE.TOKEN.TOKEN1.ID,
          name: BASE.TOKEN.TOKEN1.NAME,
          symbol: BASE.TOKEN.TOKEN1.SYMBOL,
          eoaKey: BASE.EOA.ADMIN,
        },
      })

      // アカウント作成と残高設定
      await validatorFuncs.addAccount({
        validator,
        accounts,
        options: {
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT0.ID,
          accountName: BASE.ACCOUNT.ACCOUNT0.NAME,
        },
      })
      await tokenFuncs.mint({
        token,
        accounts,
        amount: 1500,
        options: { issuerId: BASE.ISSUER.ISSUER0.ID, accountId: BASE.ACCOUNT.ACCOUNT0.ID, from: accounts[0] },
      })
    })

    it('イベントにtraceIdが正しく設定されること', async () => {
      const fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID
      const toAccountId = BASE.ACCOUNT.ACCOUNT0.ID
      const traceId = toBytes32('trace_test')

      await expect(account.emitAfterBalance(fromAccountId, toAccountId, traceId))
        .to.emit(account, 'AfterBalance')
        .withArgs(
          (fromBalance: AllBalanceDataStructOutput[]) => {
            expect(fromBalance).to.be.an('array')
            expect(fromBalance.length).to.be.greaterThan(0)
            return true
          },
          (toBalance: AllBalanceDataStructOutput[]) => {
            expect(toBalance).to.be.an('array')
            expect(toBalance.length).to.be.greaterThan(0)
            return true
          },
          traceId,
        )
    })

    it('複数のBizZoneを持つアカウントでFinZone→BizZone（ZoneID昇順）の順に残高が返されること', async () => {
      const EXPECTED_BALANCE_ARRAY_LENGTH = 4 // FinZone(1) + BizZone(3)

      // アカウント作成
      await validatorFuncs.addAccount({
        validator,
        accounts,
        options: {
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT2.ID,
          accountName: BASE.ACCOUNT.ACCOUNT2.NAME,
        },
      })

      // FinZoneに残高設定
      await tokenFuncs.mint({
        token,
        accounts,
        amount: 2000,
        options: { issuerId: BASE.ISSUER.ISSUER0.ID, accountId: BASE.ACCOUNT.ACCOUNT2.ID, from: accounts[0] },
      })

      // 複数のBizZoneを異なる順番で追加（ZoneID: 3005, 3001, 3003）
      await account.addZone(BASE.ACCOUNT.ACCOUNT2.ID, 3005, BASE.TRACE_ID)
      await account.addZone(BASE.ACCOUNT.ACCOUNT2.ID, 3001, BASE.TRACE_ID)
      await account.addZone(BASE.ACCOUNT.ACCOUNT2.ID, 3003, BASE.TRACE_ID)

      const traceId = toBytes32('zone_order_test')

      // emitAfterBalanceを実行してイベントを取得
      await expect(account.emitAfterBalance(BASE.ACCOUNT.ACCOUNT2.ID, BASE.ACCOUNT.ACCOUNT2.ID, traceId))
        .to.emit(account, 'AfterBalance')
        .withArgs(
          (fromBalance: AllBalanceDataStructOutput[]) => {
            expect(fromBalance).to.be.an('array')
            expect(fromBalance.length).to.equal(EXPECTED_BALANCE_ARRAY_LENGTH)

            // 1つ目はFinZone（zoneId = 3000）
            expect(Number(fromBalance[0].zoneId)).to.equal(3000)
            expect(Number(fromBalance[0].balance)).to.equal(2000)

            // 2つ目以降はBizZone（ZoneID昇順: 3001, 3003, 3005）
            const bizZoneIds = [
              Number(fromBalance[1].zoneId),
              Number(fromBalance[2].zoneId),
              Number(fromBalance[3].zoneId),
            ]
            const expectedOrder = [3001, 3003, 3005]

            expect(bizZoneIds).to.deep.equal(expectedOrder)
            return true
          },
          (toBalance: AllBalanceDataStructOutput[]) => {
            // fromとtoが同じアカウントなので、同じ構造であることを確認
            expect(toBalance).to.be.an('array')
            expect(toBalance.length).to.equal(EXPECTED_BALANCE_ARRAY_LENGTH)
            return true
          },
          traceId,
        )
    })
  })

  describe('再入可能性の確認', () => {
    before(async () => {
      ;({ account } = await contractFixture<AccountContractType>())
    })

    it('同じトランザクション内で複数回呼び出し可能であること', async () => {
      const fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID
      const toAccountId = BASE.ACCOUNT.ACCOUNT1.ID
      const traceId1 = toBytes32('trace_reenter_1')
      const traceId2 = toBytes32('trace_reenter_2')

      // 連続して呼び出してもエラーにならないこと
      await expect(account.emitAfterBalance(fromAccountId, toAccountId, traceId1)).to.not.be.reverted

      await expect(account.emitAfterBalance(fromAccountId, toAccountId, traceId2)).to.not.be.reverted
    })
  })
})
