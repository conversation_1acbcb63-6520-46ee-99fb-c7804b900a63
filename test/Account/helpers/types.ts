import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccountInstance,
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  EventReturnType,
  FinancialCheckInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'

export type AccountContractType = {
  accounts: SignerWithAddress[]
  provider: ProviderInstance
  issuer: IssuerInstance
  validator: ValidatorInstance
  account: AccountInstance
  token: TokenInstance
  ibcToken: IBCTokenInstance
  contractManager: ContractManagerInstance
  financialCheck: FinancialCheckInstance
  businessZoneAccount: BusinessZoneAccountInstance
}

type AccountType = { account: AccountInstance }

// type ApproveType = {
//   account: AccountInstance
//   accounts: SignerWithAddress[]
//   spenderId: string
//   amount: number
//   option?: Partial<ApproveOption>
// }

// type GetAccountsAllType = AccountType & { offset: number }

export type FuncParamsType = {
  version: AccountType
  hasAccount: AccountType & { params: Parameters<AccountInstance['hasAccount']> }
  isTerminated: AccountType & { params: Parameters<AccountInstance['isTerminated']> }
  balanceOf: AccountType & { params: Parameters<AccountInstance['balanceOf']> }
  getAccountId: AccountType & { params: Parameters<AccountInstance['getAccountId']> }
  getAccount: AccountType & { params: Parameters<AccountInstance['getAccount']> }
  getDestinationAccount: AccountType & { params: Parameters<AccountInstance['getDestinationAccount']> }
  getAccountCount: AccountType & { params?: Parameters<AccountInstance['getAccountCount']> }
  getValidatorIdByAccountId: AccountType & {
    params: Parameters<AccountInstance['getValidatorIdByAccountId']>
  }
  emitAfterBalance: AccountType & { params: Parameters<AccountInstance['emitAfterBalance']> }
}

type FuncReturnType = {
  version: string
  hasAccount: EventReturnType['Account']['HasAccount']
  isTerminated: EventReturnType['Account']['IsTerminated']
  balanceOf: EventReturnType['Account']['BalanceOf']
  getAccountId: EventReturnType['Account']['GetAccountId']
  getAccount: EventReturnType['Account']['GetAccount']
  getDestinationAccount: EventReturnType['Account']['GetDestinationAccount']
  getAccountCount: EventReturnType['Account']['GetAccountCount']
  getValidatorIdByAccountId: EventReturnType['Account']['GetValidatorIdByAccountId']
  emitAfterBalance: EventReturnType['Account']['EmitAfterBalance']
}

export type FunctionType = {
  [K in keyof FuncParamsType]: (args: FuncParamsType[K]) => Promise<FuncReturnType[K]>
}
