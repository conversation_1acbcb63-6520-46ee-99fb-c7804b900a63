import { StringUtilsMockInstance } from '@test/common/types'

export type StringUtilsContractType = {
  stringUtilsMock: StringUtilsMockInstance
}

export type StringUtilsFunctionType = {
  stringToBytes32: (stringUtilsMock: StringUtilsMockInstance, source: string) => Promise<string>
  slice: (stringUtilsMock: StringUtilsMockInstance, source: string, delimiter: string) => Promise<string[]>
  substring: (
    stringUtilsMock: StringUtilsMockInstance,
    str: string,
    startIndex: number,
    endIndex: number,
  ) => Promise<string>
}
