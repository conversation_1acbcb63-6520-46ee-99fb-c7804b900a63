import { castReturnType } from '@test/common/utils'
import { StringUtilsFunctionType } from './types'

/**
 * StringUtilsの関数を間接的に呼び出すUtil関数
 * Libraryを直接呼び出しができない関係上、StringUtilsMockを経由して呼び出す
 */
export const stringUtilsFuncs: StringUtilsFunctionType = {
  stringToBytes32: async (stringUtilsMock, source) => {
    return castReturnType(await stringUtilsMock.stringToBytes32(source))
  },
  slice: async (stringUtilsMock, source, delimiter) => {
    return castReturnType(await stringUtilsMock.slice(source, delimiter))
  },
  substring: async (stringUtilsMock, str: string, startIndex, endIndex) => {
    return castReturnType(await stringUtilsMock.substring(str, startIndex, endIndex))
  },
}
