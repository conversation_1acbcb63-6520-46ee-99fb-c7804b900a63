import '@nomicfoundation/hardhat-chai-matchers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { StringUtilsMockInstance } from '@test/common/types'
import { stringUtilsFuncs } from '@test/StringUtils/helpers/function'
import { StringUtilsContractType } from '@test/StringUtils/helpers/types'
import { expect } from 'chai'

describe('slice()', () => {
  let stringUtilsMock: StringUtilsMockInstance

  const setupFixture = async () => {
    ;({ stringUtilsMock } = await contractFixture<StringUtilsContractType>())
  }

  before(async () => {
    await setupFixture()
  })

  describe('正常系', () => {
    it('文字列をデリミタで分割すること', async () => {
      const source = BASE.STRING_UTILS.STRING_THREE_WORDS
      const delimiter = BASE.STRING_UTILS.DELIMITER_COMMA
      const res = await stringUtilsFuncs.slice(stringUtilsMock, source, delimiter)
      expect(res.length).to.equal(3)
      expect(res[0]).to.equal('decurret')
      expect(res[1]).to.equal('dcjpy')
      expect(res[2]).to.equal('amic')
    })

    it('デリミタが文字列の先頭にある場合', async () => {
      const source = BASE.STRING_UTILS.STRING_LEADING_DELIMITER
      const delimiter = BASE.STRING_UTILS.DELIMITER_COMMA
      const res = await stringUtilsFuncs.slice(stringUtilsMock, source, delimiter)
      expect(res.length).to.equal(4)
      expect(res[0]).to.equal('')
      expect(res[1]).to.equal('decurret')
      expect(res[2]).to.equal('dcjpy')
      expect(res[3]).to.equal('amic')
    })

    it('デリミタが文字列の末尾にある場合', async () => {
      const source = BASE.STRING_UTILS.STRING_TRAILING_DELIMITER
      const delimiter = BASE.STRING_UTILS.DELIMITER_COMMA
      const res = await stringUtilsFuncs.slice(stringUtilsMock, source, delimiter)
      expect(res.length).to.equal(4)
      expect(res[0]).to.equal('decurret')
      expect(res[1]).to.equal('dcjpy')
      expect(res[2]).to.equal('amic')
      expect(res[3]).to.equal('')
    })

    it('デリミタが連続している場合', async () => {
      const source = BASE.STRING_UTILS.STRING_CONSECUTIVE_DELIMITERS
      const delimiter = BASE.STRING_UTILS.DELIMITER_COMMA
      const res = await stringUtilsFuncs.slice(stringUtilsMock, source, delimiter)
      expect(res.length).to.equal(6)
      expect(res[0]).to.equal('decurret')
      expect(res[1]).to.equal('')
      expect(res[2]).to.equal('dcjpy')
      expect(res[3]).to.equal('')
      expect(res[4]).to.equal('')
      expect(res[5]).to.equal('amic')
    })

    it('デリミタがソース文字列と同じ場合', async () => {
      const source = BASE.STRING_UTILS.STRING_SAMPLE
      const delimiter = BASE.STRING_UTILS.STRING_SAMPLE
      const res = await stringUtilsFuncs.slice(stringUtilsMock, source, delimiter)
      expect(res.length).to.equal(2)
      expect(res[0]).to.equal('')
      expect(res[1]).to.equal('')
    })

    it('デリミタがマルチバイト文字の場合', async () => {
      const source = BASE.STRING_UTILS.STRING_THREE_WORDS_JAPANESE
      const delimiter = BASE.STRING_UTILS.DELIMITER_JAPANESE
      const res = await stringUtilsFuncs.slice(stringUtilsMock, source, delimiter)
      expect(res.length).to.equal(3)
      expect(res[0]).to.equal('ディーカレット')
      expect(res[1]).to.equal('ディーシージェーピーワイ')
      expect(res[2]).to.equal('アミック')
    })
  })

  describe('準正常系', () => {
    it('空の文字列を分割する場合、リバートすること', async () => {
      const source = BASE.STRING_UTILS.STRING_EMPTY
      const delimiter = BASE.STRING_UTILS.DELIMITER_COMMA
      const result = stringUtilsFuncs.slice(stringUtilsMock, source, delimiter)
      await expect(result).to.be.revertedWithPanic(0x11)
    })

    it('デリミタが空文字列の場合、リバートすること', async () => {
      const source = BASE.STRING_UTILS.STRING_SAMPLE
      const delimiter = BASE.STRING_UTILS.DELIMITER_EMPTY
      const result = stringUtilsFuncs.slice(stringUtilsMock, source, delimiter)
      await expect(result).to.be.revertedWithPanic(0x11)
    })

    it('デリミタがソース文字列より長い場合、リバートすること', async () => {
      const source = BASE.STRING_UTILS.STRING_SAMPLE
      const delimiter = BASE.STRING_UTILS.DELIMITER_LONG
      const result = stringUtilsFuncs.slice(stringUtilsMock, source, delimiter)
      await expect(result).to.be.revertedWithPanic(0x11)
    })

    it('ソース文字列とデリミタが空の場合、リバートすること', async () => {
      const source = BASE.STRING_UTILS.STRING_EMPTY
      const delimiter = BASE.STRING_UTILS.DELIMITER_EMPTY
      const result = stringUtilsFuncs.slice(stringUtilsMock, source, delimiter)
      await expect(result).to.be.revertedWithPanic(0x11)
    })
  })
})
