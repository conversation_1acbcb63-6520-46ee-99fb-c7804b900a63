import '@nomicfoundation/hardhat-chai-matchers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { StringUtilsMockInstance } from '@test/common/types'
import { toBytes32 } from '@test/common/utils'
import { stringUtilsFuncs } from '@test/StringUtils/helpers/function'
import { StringUtilsContractType } from '@test/StringUtils/helpers/types'
import { expect } from 'chai'

describe('stringToBytes32()', () => {
  let stringUtilsMock: StringUtilsMockInstance

  const setupFixture = async () => {
    ;({ stringUtilsMock } = await contractFixture<StringUtilsContractType>())
  }

  before(async () => {
    await setupFixture()
  })

  describe('正常系', () => {
    it('stringの文字列をbytes32型の文字列に変換すること', async () => {
      const res = await stringUtilsFuncs.stringToBytes32(stringUtilsMock, BASE.STRING_UTILS.STRING_SAMPLE)
      expect(res).to.equal(toBytes32(BASE.STRING_UTILS.STRING_SAMPLE))
    })

    it('32文字のstringをbytes32型の文字列に変換すること', async () => {
      const testString = BASE.STRING_UTILS.STRING_LONG_32
      const res = await stringUtilsFuncs.stringToBytes32(stringUtilsMock, testString)
      expect(res).to.equal(toBytes32(testString))
    })

    it('空のstringをbytes32型の文字列に変換できること', async () => {
      const testString = BASE.STRING_UTILS.STRING_EMPTY
      const res = await stringUtilsFuncs.stringToBytes32(stringUtilsMock, testString)
      expect(res).to.equal(toBytes32(testString))
    })
  })

  describe('準正常系', () => {
    it('33文字のstringをbytes32型に変換しようとするとリバートすること', async () => {
      const testString = BASE.STRING_UTILS.STRING_LONG_33
      const result = stringUtilsFuncs.stringToBytes32(stringUtilsMock, testString)
      await expect(result).to.be.revertedWith(ERR.COMMON.TOO_LONG_TO_CONVERT_BYTE32)
    })
  })
})
