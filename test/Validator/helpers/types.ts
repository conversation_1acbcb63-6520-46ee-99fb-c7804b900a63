import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccountInstance,
  AddAccountOption,
  AddValidatorAccountIdOption,
  AddValidatorOption,
  AddValidatorRoleOption,
  BusinessZoneAccountInstance,
  ContractCallOption,
  ContractManagerInstance,
  FinancialZoneAccountInstance,
  EventReturnType,
  IssuerInstance,
  ModAccountOption,
  ModValidatorOption,
  ProviderInstance,
  SetActiveBusinessAccountWithZoneOption,
  SetTerminatedOption,
  SetValidatorAllOption,
  SyncAccountOption,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { ContractTransactionResponse } from 'ethers'

export type ValidatorContractType = {
  accounts: SignerWithAddress[]
  provider: ProviderInstance
  issuer: IssuerInstance
  validator: ValidatorInstance
  account: AccountInstance
  token: TokenInstance
  businessZoneAccount: BusinessZoneAccountInstance
  contractManager: ContractManagerInstance
  financialZoneAccount: FinancialZoneAccountInstance
}

type ValidatorType = { validator: ValidatorInstance }

type BaseAccountsType = ValidatorType & {
  accounts: SignerWithAddress[]
}

export type FuncParamsType = {
  version: ValidatorType
  hasValidator: ValidatorType & {
    prams: Parameters<ValidatorInstance['hasValidator']>
  }
  getValidator: ValidatorType & {
    prams: Parameters<ValidatorInstance['getValidator']>
  }
  getValidatorCount: ValidatorType
  getValidatorId: ValidatorType & {
    prams: Parameters<ValidatorInstance['getValidatorId']>
  }
  hasAccount: ValidatorType & {
    prams: Parameters<ValidatorInstance['hasAccount']>
  }
  getValidatorList: ValidatorType & {
    prams: Parameters<ValidatorInstance['getValidatorList']>
  }
  getAccountList: ValidatorType & {
    prams: Parameters<ValidatorInstance['getAccountList']>
  }
  getAccount: ValidatorType & {
    prams: Parameters<ValidatorInstance['getAccount']>
  }
  getAccountAll: ValidatorType & {
    prams: Parameters<ValidatorInstance['getAccountAll']>
  }
  getAccountAllList: ValidatorType & {
    prams: Parameters<ValidatorInstance['getAccountAllList']>
  }
  getZoneByAccountId: ValidatorType & {
    prams: Parameters<ValidatorInstance['getZoneByAccountId']>
  }
  getDestinationAccount: ValidatorType & {
    prams: Parameters<ValidatorInstance['getDestinationAccount']>
  }
  getValidatorAccountId: ValidatorType & {
    prams: Parameters<ValidatorInstance['getValidatorAccountId']>
  }
  addValidator: BaseAccountsType & {
    options?: Partial<AddValidatorOption & ContractCallOption>
  }
  addValidatorRole: BaseAccountsType & {
    options?: Partial<AddValidatorRoleOption & ContractCallOption>
  }
  addAccount: BaseAccountsType & {
    options?: Partial<AddAccountOption>
  }
  setActiveBusinessAccountWithZone: BaseAccountsType & {
    options?: Partial<SetActiveBusinessAccountWithZoneOption>
  }
  setBizZoneTerminated: BaseAccountsType & {
    options?: Partial<SetActiveBusinessAccountWithZoneOption>
  }
  addValidatorAccountId: BaseAccountsType & {
    options?: Partial<AddValidatorAccountIdOption>
  }
  syncAccount: BaseAccountsType & {
    options?: Partial<SyncAccountOption>
  }
  modValidator: BaseAccountsType & {
    name: string
    options?: Partial<ModValidatorOption & ContractCallOption>
  }
  modAccount: BaseAccountsType & {
    options?: Partial<ModAccountOption & ContractCallOption>
  }
  setTerminated: BaseAccountsType & {
    options?: Partial<SetTerminatedOption>
  }
  hasValidatorRole: ValidatorType & {
    options?: Partial<ValidatorInstance & ContractCallOption>
  }
  getValidatorAll: ValidatorType & {
    index: number
  }
  setValidatorAll: {
    valid: ValidatorInstance
    validator: any
    accounts: SignerWithAddress[]
    options?: Partial<SetValidatorAllOption & ContractCallOption>
  }
}

export type FuncReturnType = {
  version: string
  hasValidator: EventReturnType['Validator']['HasValidator']
  getValidator: EventReturnType['Validator']['GetValidator']
  getValidatorCount: EventReturnType['Validator']['GetValidatorCount']
  getValidatorId: EventReturnType['Validator']['GetValidatorId']
  hasAccount: EventReturnType['Validator']['HasAccount']
  getValidatorList: EventReturnType['Validator']['GetValidatorList']
  getAccountList: EventReturnType['Validator']['GetAccountList']
  getAccount: EventReturnType['Validator']['GetAccount']
  getAccountAll: EventReturnType['Validator']['GetAccountAll']
  getAccountAllList: EventReturnType['Validator']['GetAccountAllList']
  getZoneByAccountId: EventReturnType['Validator']['GetZoneByAccountId']
  getDestinationAccount: EventReturnType['Validator']['GetDestinationAccount']
  getValidatorAccountId: EventReturnType['Validator']['GetValidatorAccountId']
  addValidator: ContractTransactionResponse
  addValidatorRole: ContractTransactionResponse
  addAccount: ContractTransactionResponse
  setActiveBusinessAccountWithZone: ContractTransactionResponse
  setBizZoneTerminated: ContractTransactionResponse
  addValidatorAccountId: ContractTransactionResponse
  syncAccount: ContractTransactionResponse
  modValidator: ContractTransactionResponse
  modAccount: ContractTransactionResponse
  setTerminated: ContractTransactionResponse
  hasValidatorRole: EventReturnType['Validator']['HasValidatorRole']
  getValidatorAll: EventReturnType['Validator']['GetValidatorAll']
  setValidatorAll: ContractTransactionResponse
}

export type ValidatorFunctionType = {
  [K in keyof FuncParamsType]: (args: FuncParamsType[K]) => Promise<FuncReturnType[K]>
}
