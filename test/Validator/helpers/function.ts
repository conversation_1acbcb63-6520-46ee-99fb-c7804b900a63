/* eslint-disable @typescript-eslint/no-explicit-any */
import { BASE } from '@test/common/consts'
import { castReturnType, getDeadline } from '@test/common/utils'
import { ValidatorFunctionType } from './types'
import privateKey from '@/privateKey'

export const validatorFuncs: ValidatorFunctionType = {
  version: ({ validator }) => {
    return castReturnType(validator.version())
  },
  hasValidator: ({ validator, prams }) => {
    return castReturnType(validator.hasValidator(...prams))
  },
  getValidator: ({ validator, prams }) => {
    return castReturnType(validator.getValidator(...prams))
  },
  getValidatorCount: ({ validator }) => {
    return castReturnType(validator.getValidatorCount())
  },
  getValidatorId: ({ validator, prams }) => {
    return castReturnType(validator.getValidatorId(...prams))
  },
  hasAccount: ({ validator, prams }) => {
    return castReturnType(validator.hasAccount(...prams))
  },
  getValidatorList: ({ validator, prams }) => {
    return castReturnType(validator.getValidatorList(...prams))
  },
  getAccountList: ({ validator, prams }) => {
    return castReturnType(validator.getAccountList(...prams))
  },
  getAccount: ({ validator, prams }) => {
    return castReturnType(validator.getAccount(...prams))
  },
  getAccountAll: ({ validator, prams }) => {
    return castReturnType(validator.getAccountAll(...prams))
  },
  getAccountAllList: ({ validator, prams }) => {
    return castReturnType(validator.getAccountAllList(...prams))
  },
  getZoneByAccountId: ({ validator, prams }) => {
    return castReturnType(validator.getZoneByAccountId(...prams))
  },
  getDestinationAccount: ({ validator, prams }) => {
    return castReturnType(validator.getDestinationAccount(...prams))
  },
  getValidatorAccountId: ({ validator, prams }) => {
    return castReturnType(validator.getValidatorAccountId(...prams))
  },
  addValidator: async ({ validator, accounts, options = {} }) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ADMIN,
      validatorId = BASE.VALID.VALID0.ID,
      name = BASE.VALID.VALID0.NAME,
      issuerId = BASE.ISSUER.ISSUER0.ID,
    } = options
    const _deadline = await getDeadline(deadline)
    const signer = privateKey.key[eoaKey]
    const _sig =
      sig ??
      privateKey.sig(signer, ['bytes32', 'bytes32', 'bytes32', 'uint256'], [validatorId, issuerId, name, _deadline])

    return castReturnType(
      validator.connect(accounts[9]).addValidator(validatorId, issuerId, name, BASE.TRACE_ID, _deadline, _sig[0]),
    )
  },
  addValidatorRole: async ({ validator, accounts, options = {} }) => {
    let { sig, deadline, eoaKey = BASE.EOA.ADMIN, validatorId = BASE.VALID.VALID0.ID, validatorEoa } = options
    validatorEoa = validatorEoa ?? (await accounts[BASE.EOA.VALID1].getAddress())
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ??
      privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'address', 'uint256'], [validatorId, validatorEoa, _deadline])

    return castReturnType(
      validator.connect(accounts[9]).addValidatorRole(validatorId, validatorEoa, BASE.TRACE_ID, _deadline, _sig[0]),
    )
  },
  addAccount: ({ validator, accounts, options = {} }) => {
    const {
      validatorId = BASE.VALID.VALID0.ID,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
      accountName = BASE.ACCOUNT.ACCOUNT1.NAME,
      limitValues = BASE.LIMIT_VALUES,
    } = options
    return castReturnType(
      validator.connect(accounts[0]).addAccount(validatorId, accountId, accountName, limitValues, BASE.TRACE_ID),
    )
  },
  setActiveBusinessAccountWithZone: ({ validator, accounts, options = {} }) => {
    const {
      validatorId = BASE.VALID.VALID0.ID,
      zoneId = BASE.ZONE_ID.ID0,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
    } = options
    return castReturnType(
      validator.connect(accounts[0]).setActiveBusinessAccountWithZone(validatorId, zoneId, accountId, BASE.TRACE_ID),
    )
  },
  setBizZoneTerminated: ({ validator, accounts, options = {} }) => {
    const { zoneId = BASE.ZONE_ID.ID0, accountId = BASE.ACCOUNT.ACCOUNT1.ID } = options
    return castReturnType(validator.connect(accounts[0]).setBizZoneTerminated(zoneId, accountId, BASE.TRACE_ID))
  },
  addValidatorAccountId: ({ validator, accounts, options = {} }) => {
    const { validatorId = BASE.VALID.VALID0.ID, accountId = BASE.ACCOUNT.ACCOUNT1.ID } = options
    return castReturnType(validator.connect(accounts[0]).addValidatorAccountId(validatorId, accountId, BASE.TRACE_ID))
  },
  syncAccount: async ({ validator, accounts, options = {} }) => {
    const {
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
      accountName = BASE.ACCOUNT.ACCOUNT1.NAME,
      validatorId = BASE.VALID.VALID0.ID,
      zoneId = BASE.ZONE_ID.ID0,
      zoneName = BASE.ZONE_NAME.NAME0,
      accountStatus = BASE.STATUS.APPLYING,
      reasonCode = BASE.REASON_CODE1,
      approvalAmount = BASE.ZERO_APPROVAL_VALUE,
    } = options
    return castReturnType(
      validator.syncAccount(
        validatorId,
        accountId,
        accountName,
        zoneId,
        zoneName,
        accountStatus,
        reasonCode,
        approvalAmount,
        BASE.TRACE_ID,
        {
          from: accounts[0],
        },
      ),
    )
  },
  modValidator: async ({ validator, accounts, name, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, validatorId = BASE.VALID.VALID0.ID } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ?? privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'bytes32', 'uint256'], [validatorId, name, _deadline])
    return castReturnType(
      validator.connect(accounts[9]).modValidator(validatorId, name, BASE.TRACE_ID, _deadline, _sig[0]),
    )
  },
  modAccount: async ({ validator, accounts, options = {} }) => {
    const {
      validatorId = BASE.VALID.VALID0.ID,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
      accountName = BASE.ACCOUNT.ACCOUNT1.NAME,
      traceId = BASE.TRACE_ID,
      deadline,
      sig,
    } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ??
      privateKey.sig(
        privateKey.key[BASE.EOA.VALID1],
        ['bytes32', 'bytes32', 'uint256'],
        [validatorId, accountId, _deadline],
      )
    return castReturnType(
      validator.connect(accounts[9]).modAccount(validatorId, accountId, accountName, traceId, _deadline, _sig[0]),
    )
  },
  setTerminated: ({ validator, accounts, options = {} }) => {
    const { validatorId = BASE.VALID.VALID0.ID, accountId = BASE.ACCOUNT.ACCOUNT1.ID } = options
    //TODO reasonCodeのテストの実施
    const reasonCode = BASE.REASON_CODE2
    return castReturnType(
      validator.connect(accounts[0]).setTerminated(validatorId, accountId, reasonCode, BASE.TRACE_ID),
    )
  },
  hasValidatorRole: async ({ validator, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.VALID1, validatorId = BASE.VALID.VALID0.ID, hash = 'morning' } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ?? privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'string', 'uint256'], [validatorId, hash, _deadline])

    return castReturnType(validator.hasValidatorRole(validatorId, _sig[1], _deadline, _sig[0]))
  },
  getValidatorAll: async ({ validator, index }) => {
    return validator.getValidatorAll(index) as any
  },
  setValidatorAll: async ({ valid, validator, accounts, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.SET_VALIDATORS_ALL } = options
    const _deadline = await getDeadline(deadline)
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return castReturnType(valid.connect(accounts[9]).setValidatorAll(validator, _deadline, _sig[0]))
  },
  // syncBusinessZoneAccountStatus: ({validator,accounts,options}:{
  //   validator: ValidatorInstance,
  //   accounts: any,
  //   options: any,
  // }
  // ) => {
  //   const {
  //     zoneId = BASE.ZONE_ID.ID0,
  //     zoneName = BASE.ZONE_NAME.NAME0,
  //     accountId = BASE.ACCOUNT.ACCOUNT1.ID,
  //     accountName = BASE.ACCOUNT.ACCOUNT1.NAME,
  //     accountStatus = BASE.STATUS.APPLYING,
  //   } = options;
  //   return validator.syncBusinessZoneAccountStatus(
  //     zoneId,
  //     zoneName,
  //     accountId,
  //     accountName,
  //     accountStatus,
  //     BASE.TRACE_ID,
  //     {
  //       from: accounts[0],
  //     },
  //   )
  // },
}
