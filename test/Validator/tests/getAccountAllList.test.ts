import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { FinancialZoneAccountInstance, IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { financialZoneAccountFuncs } from '@test/FinancialZoneAccount/helpers/function'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('getAccountAllList()', () => {
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let financialZoneAccount: FinancialZoneAccountInstance
  let accounts: SignerWithAddress[]

  const offset = 0
  const limit = 10

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, financialZoneAccount } = await contractFixture<ValidatorContractType>())
  }

  const setupBasicRoles = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({
      provider,
      accounts,
      options: {
        providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
      },
    })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await issuerFuncs.addIssuer({
      issuer,
      accounts,
      options: {
        issuerId: BASE.ISSUER.ISSUER1.ID,
        bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
      },
    })
    await validatorFuncs.addValidator({
      validator,
      accounts,
      options: {
        validatorId: BASE.VALID.VALID1.ID,
        issuerId: BASE.ISSUER.ISSUER1.ID,
      },
    })
    await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
  }

  const setupAccountData = async () => {
    const appliedTime = '0'
    const registeredTime = '0'
    const terminatingTime = '0'
    const terminatedTime = '0'
    const addAccountParams = [
      {
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
        accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
        balance: 0,
        accountStatus: 'active',
        reasonCode: BASE.REASON_CODE1,
        appliedAt: appliedTime,
        registeredAt: registeredTime,
        terminatingAt: terminatingTime,
        terminatedAt: terminatedTime,
      },
      {
        accountId: BASE.ACCOUNT.ACCOUNT2.ID,
        accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
        balance: 0,
        accountStatus: 'active',
        reasonCode: BASE.REASON_CODE1,
        appliedAt: appliedTime,
        registeredAt: registeredTime,
        terminatingAt: terminatingTime,
        terminatedAt: terminatedTime,
      },
      {
        accountId: BASE.ACCOUNT.ACCOUNT3.ID,
        accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
        balance: 0,
        accountStatus: 'active',
        reasonCode: BASE.REASON_CODE1,
        appliedAt: appliedTime,
        registeredAt: registeredTime,
        terminatingAt: terminatingTime,
        terminatedAt: terminatedTime,
      },
      {
        accountId: BASE.ACCOUNT.ACCOUNT4.ID,
        accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
        balance: 0,
        accountStatus: 'active',
        reasonCode: BASE.REASON_CODE1,
        appliedAt: appliedTime,
        registeredAt: registeredTime,
        terminatingAt: terminatingTime,
        terminatedAt: terminatedTime,
      },
      {
        accountId: BASE.ACCOUNT.ACCOUNT5.ID,
        accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
        balance: 0,
        accountStatus: 'active',
        reasonCode: BASE.REASON_CODE1,
        appliedAt: appliedTime,
        registeredAt: registeredTime,
        terminatingAt: terminatingTime,
        terminatedAt: terminatedTime,
      },
      {
        accountId: BASE.ACCOUNT.ACCOUNT6.ID,
        accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
        balance: 0,
        accountStatus: 'active',
        reasonCode: BASE.REASON_CODE1,
        appliedAt: appliedTime,
        registeredAt: registeredTime,
        terminatingAt: terminatingTime,
        terminatedAt: terminatedTime,
      },
      {
        accountId: BASE.ACCOUNT.ACCOUNT7.ID,
        accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
        balance: 0,
        accountStatus: 'active',
        reasonCode: BASE.REASON_CODE1,
        appliedAt: appliedTime,
        registeredAt: registeredTime,
        terminatingAt: terminatingTime,
        terminatedAt: terminatedTime,
      },
      {
        accountId: BASE.ACCOUNT.ACCOUNT8.ID,
        accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
        balance: 0,
        accountStatus: 'active',
        reasonCode: BASE.REASON_CODE1,
        appliedAt: appliedTime,
        registeredAt: registeredTime,
        terminatingAt: terminatingTime,
        terminatedAt: terminatedTime,
      },
      {
        accountId: BASE.ACCOUNT.ACCOUNT9.ID,
        accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
        balance: 0,
        accountStatus: 'active',
        reasonCode: BASE.REASON_CODE1,
        appliedAt: appliedTime,
        registeredAt: registeredTime,
        terminatingAt: terminatingTime,
        terminatedAt: terminatedTime,
      },
      {
        accountId: BASE.ACCOUNT.ACCOUNT10.ID,
        accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
        balance: 0,
        accountStatus: 'active',
        reasonCode: BASE.REASON_CODE1,
        appliedAt: appliedTime,
        registeredAt: registeredTime,
        terminatingAt: terminatingTime,
        terminatedAt: terminatedTime,
      },
      {
        accountId: BASE.ACCOUNT.ACCOUNT11.ID,
        accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
        balance: 0,
        accountStatus: 'active',
        reasonCode: BASE.REASON_CODE1,
        appliedAt: appliedTime,
        registeredAt: registeredTime,
        terminatingAt: terminatingTime,
        terminatedAt: terminatedTime,
      },
    ]

    const limitValues = {
      mint: 300,
      burn: 400,
      charge: 800,
      discharge: 100,
      transfer: 100,
      cumulative: {
        total: 500,
        mint: 100,
        burn: 100,
        charge: 100,
        discharge: 100,
        transfer: 100,
      },
    }

    for (let i = 0; i < addAccountParams.length; i++) {
      const currentTimestamp = await helpers.time.latest()
      addAccountParams[i].registeredAt = currentTimestamp.toString() // DCPF-25563 Set true timestamp of registeredAt
      await validatorFuncs.addAccount({
        validator,
        accounts,
        options: {
          accountId: addAccountParams[i].accountId,
          accountName: addAccountParams[i].accountName,
        },
      })
      await financialZoneAccountFuncs.addAccountLimit({
        finAccount: financialZoneAccount,
        params: [addAccountParams[i].accountId, limitValues, BASE.TRACE_ID],
      })
    }

    return addAccountParams
  }

  describe('正常系', () => {
    describe('accountが登録されていない状態', () => {
      before(async () => {
        await setupFixture()
        await setupBasicRoles()
      })

      it('Accountが登録されていない場合、空リストが取得できること', async () => {
        const result = await validatorFuncs.getAccountAllList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit],
        })

        utils.assertEqualForEachField(result, {
          accounts: [],
          totalCount: 0,
          err: '',
        })
      })
    })

    describe('provider, providerRole, issuer, validator, token, accountが登録されている状態', () => {
      const sortOrder = 'asc'
      let addAccountParams: any[]

      before(async () => {
        await setupFixture()
        await setupBasicRoles()
        addAccountParams = await setupAccountData()
      })

      it('複数のgetIssuerList呼び出しが同時に行われた場合、それぞれ正しい結果を返すこと', async () => {
        // 同時に異なるoffsetとlimitでリクエストを発行
        const requests = [
          validatorFuncs.getAccountAllList({ validator, prams: [BASE.VALID.VALID0.ID, 0, 3] }), // 最初の3件
          validatorFuncs.getAccountAllList({ validator, prams: [BASE.VALID.VALID0.ID, 5, 5] }), // 6番目から次の5件
        ]

        const results = await Promise.all(requests)

        // 各リクエストの結果を検証
        utils.assertEqualForEachField(results[0], { totalCount: addAccountParams.length, err: '' })
        for (let i = 0; i < results[0].accounts.length; i++) {
          assert.isString(results[0].accounts[i].accountId, 'accountId')
          assert.strictEqual(
            results[0].accounts[i].accountId,
            addAccountParams[addAccountParams.length - i - 1].accountId,
            'account id',
          )
          utils.assertEqualForEachField(results[0].accounts[i].accountDataAll, {
            accountName: addAccountParams[addAccountParams.length - i - 1].accountName,
            balance: addAccountParams[addAccountParams.length - i - 1].balance.toString(), // DCPF-25563 Hardhat result returning string
            accountStatus: utils.toBytes32(addAccountParams[addAccountParams.length - i - 1].accountStatus), // DCPF-25563 Hardhat result returning hex of accountStatus
            reasonCode: addAccountParams[addAccountParams.length - i - 1].reasonCode,
            appliedAt: addAccountParams[addAccountParams.length - i - 1].appliedAt,
            registeredAt: addAccountParams[addAccountParams.length - i - 1].registeredAt,
            terminatingAt: addAccountParams[addAccountParams.length - i - 1].terminatingAt,
            terminatedAt: addAccountParams[addAccountParams.length - i - 1].terminatedAt,
            mintLimit: 300,
            burnLimit: 400,
            chargeLimit: 800,
            dischargeLimit: 100,
            transferLimit: 100,
            cumulativeLimit: 500,
            cumulativeAmount: 0,
            cumulativeDate: 0,
          })
          utils.assertEqualForEachField(results[0].accounts[i].accountDataAll.cumulativeTransactionLimits, {
            cumulativeMintLimit: 100,
            cumulativeMintAmount: 0,
            cumulativeBurnLimit: 100,
            cumulativeBurnAmount: 0,
            cumulativeChargeLimit: 100,
            cumulativeChargeAmount: 0,
            cumulativeDischargeLimit: 100,
            cumulativeDischargeAmount: 0,
            cumulativeTransferLimit: 100,
            cumulativeTransferAmount: 0,
          })
        }

        utils.assertEqualForEachField(results[1], { totalCount: addAccountParams.length, err: '' })
        for (let i = 0; i < results[1].accounts.length; i++) {
          assert.isString(results[1].accounts[i].accountId, 'accountId')
          assert.strictEqual(results[1].accounts[i].accountId, addAccountParams[6 - i - 1].accountId, 'account id')
          utils.assertEqualForEachField(results[1].accounts[i].accountDataAll, {
            accountName: addAccountParams[6 - i - 1].accountName,
            balance: addAccountParams[6 - i - 1].balance.toString(), // DCPF-25563 Hardhat result returning string
            accountStatus: utils.toBytes32(addAccountParams[6 - i - 1].accountStatus), // DCPF-25563 Hardhat result returning hex of accountStatus
            reasonCode: addAccountParams[6 - i - 1].reasonCode,
            appliedAt: addAccountParams[6 - i - 1].appliedAt,
            registeredAt: addAccountParams[6 - i - 1].registeredAt,
            terminatingAt: addAccountParams[6 - i - 1].terminatingAt,
            terminatedAt: addAccountParams[6 - i - 1].terminatedAt,
            mintLimit: 300,
            burnLimit: 400,
            chargeLimit: 800,
            dischargeLimit: 100,
            transferLimit: 100,
            cumulativeLimit: 500,
            cumulativeAmount: 0,
            cumulativeDate: 0,
          })
          utils.assertEqualForEachField(results[1].accounts[i].accountDataAll.cumulativeTransactionLimits, {
            cumulativeMintLimit: 100,
            cumulativeMintAmount: 0,
            cumulativeBurnLimit: 100,
            cumulativeBurnAmount: 0,
            cumulativeChargeLimit: 100,
            cumulativeChargeAmount: 0,
            cumulativeDischargeLimit: 100,
            cumulativeDischargeAmount: 0,
            cumulativeTransferLimit: 100,
            cumulativeTransferAmount: 0,
          })
        }
      })

      it('sortOrderにDESCを指定し、offset0, limit20を指定した場合、20要素目から逆順に20件取得できること', async () => {
        const offset = 0
        const limit = 20
        const result = await validatorFuncs.getAccountAllList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit],
        })
        utils.assertEqualForEachField(result, { totalCount: addAccountParams.length, err: '' })
        for (let i = 0; i < addAccountParams.length; i++) {
          assert.isString(result.accounts[i].accountId, 'accountId')
          assert.strictEqual(
            result.accounts[i].accountId,
            addAccountParams[addAccountParams.length - i - 1].accountId,
            'account id',
          )
          utils.assertEqualForEachField(result.accounts[i].accountDataAll, {
            accountName: addAccountParams[addAccountParams.length - i - 1].accountName,
            balance: addAccountParams[addAccountParams.length - i - 1].balance.toString(), // DCPF-25563 Hardhat result returning string
            accountStatus: utils.toBytes32(addAccountParams[addAccountParams.length - i - 1].accountStatus), // DCPF-25563 Hardhat result returning hex of accountStatus
            reasonCode: addAccountParams[addAccountParams.length - i - 1].reasonCode,
            appliedAt: addAccountParams[addAccountParams.length - i - 1].appliedAt,
            registeredAt: addAccountParams[addAccountParams.length - i - 1].registeredAt,
            terminatingAt: addAccountParams[addAccountParams.length - i - 1].terminatingAt,
            terminatedAt: addAccountParams[addAccountParams.length - i - 1].terminatedAt,
            mintLimit: 300,
            burnLimit: 400,
            chargeLimit: 800,
            dischargeLimit: 100,
            transferLimit: 100,
            cumulativeLimit: 500,
            cumulativeAmount: 0,
            cumulativeDate: 0,
          })
          utils.assertEqualForEachField(result.accounts[i].accountDataAll.cumulativeTransactionLimits, {
            cumulativeMintLimit: 100,
            cumulativeMintAmount: 0,
            cumulativeBurnLimit: 100,
            cumulativeBurnAmount: 0,
            cumulativeChargeLimit: 100,
            cumulativeChargeAmount: 0,
            cumulativeDischargeLimit: 100,
            cumulativeDischargeAmount: 0,
            cumulativeTransferLimit: 100,
            cumulativeTransferAmount: 0,
          })
        }
      })

      it('offset0, limit10を指定した場合、1要素目から10件取得できること', async () => {
        const offset = 0
        const limit = 10
        const result = await validatorFuncs.getAccountAllList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit],
        })
        utils.assertEqualForEachField(result, { totalCount: addAccountParams.length, err: '' })
        for (let i = 0; i < result.accounts.length; i++) {
          assert.isString(result.accounts[i].accountId, 'accountId')
          assert.strictEqual(result.accounts[i].accountId, addAccountParams[10 - i].accountId, 'account id')
          utils.assertEqualForEachField(result.accounts[i].accountDataAll, {
            accountName: addAccountParams[10 - i].accountName,
            balance: addAccountParams[10 - i].balance.toString(), // DCPF-25563 Hardhat result returning string
            accountStatus: utils.toBytes32(addAccountParams[10 - i].accountStatus), // DCPF-25563 Hardhat result returning hex of accountStatus
            reasonCode: addAccountParams[10 - i].reasonCode,
            appliedAt: addAccountParams[10 - i].appliedAt,
            registeredAt: addAccountParams[10 - i].registeredAt,
            terminatingAt: addAccountParams[10 - i].terminatingAt,
            terminatedAt: addAccountParams[10 - i].terminatedAt,
            mintLimit: 300,
            burnLimit: 400,
            chargeLimit: 800,
            dischargeLimit: 100,
            transferLimit: 100,
            cumulativeLimit: 500,
            cumulativeAmount: 0,
            cumulativeDate: 0,
          })
          utils.assertEqualForEachField(result.accounts[i].accountDataAll.cumulativeTransactionLimits, {
            cumulativeMintLimit: 100,
            cumulativeMintAmount: 0,
            cumulativeBurnLimit: 100,
            cumulativeBurnAmount: 0,
            cumulativeChargeLimit: 100,
            cumulativeChargeAmount: 0,
            cumulativeDischargeLimit: 100,
            cumulativeDischargeAmount: 0,
            cumulativeTransferLimit: 100,
            cumulativeTransferAmount: 0,
          })
        }
      })

      it('offset2, limit2を指定した場合、3要素目から2件取得できること', async () => {
        const offset = 2
        const limit = 2
        const result = await validatorFuncs.getAccountAllList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit],
        })
        utils.assertEqualForEachField(result, { totalCount: addAccountParams.length, err: '' })
        for (let i = 0; i < result.accounts.length; i++) {
          assert.isString(result.accounts[i].accountId, 'accountId')
          assert.strictEqual(result.accounts[i].accountId, addAccountParams[8 - i].accountId, 'account id')
          utils.assertEqualForEachField(result.accounts[i].accountDataAll, {
            accountName: addAccountParams[8 - i].accountName,
            balance: addAccountParams[8 - i].balance.toString(), // DCPF-25563 Hardhat result returning string
            accountStatus: utils.toBytes32(addAccountParams[8 - i].accountStatus), // DCPF-25563 Hardhat result returning hex of accountStatus
            reasonCode: addAccountParams[8 - i].reasonCode,
            appliedAt: addAccountParams[8 - i].appliedAt,
            registeredAt: addAccountParams[8 - i].registeredAt,
            terminatingAt: addAccountParams[8 - i].terminatingAt,
            terminatedAt: addAccountParams[8 - i].terminatedAt,
            mintLimit: 300,
            burnLimit: 400,
            chargeLimit: 800,
            dischargeLimit: 100,
            transferLimit: 100,
            cumulativeLimit: 500,
            cumulativeAmount: 0,
            cumulativeDate: 0,
          })
          utils.assertEqualForEachField(result.accounts[i].accountDataAll.cumulativeTransactionLimits, {
            cumulativeMintLimit: 100,
            cumulativeMintAmount: 0,
            cumulativeBurnLimit: 100,
            cumulativeBurnAmount: 0,
            cumulativeChargeLimit: 100,
            cumulativeChargeAmount: 0,
            cumulativeDischargeLimit: 100,
            cumulativeDischargeAmount: 0,
            cumulativeTransferLimit: 100,
            cumulativeTransferAmount: 0,
          })
        }
      })

      it('最後の1件が取得できること', async () => {
        // 最後の１件のみ取得
        const offset = 10
        const limit = 1

        const result = await validatorFuncs.getAccountAllList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit],
        })
        assert.isString(result.accounts[0].accountId, 'accountId')
        assert.strictEqual(result.accounts[0].accountId, addAccountParams[0].accountId, 'account id')
        utils.assertEqualForEachField(result.accounts[0].accountDataAll, {
          accountName: addAccountParams[0].accountName,
          balance: addAccountParams[0].balance.toString(), // DCPF-25563 Hardhat result returning string
          accountStatus: utils.toBytes32(addAccountParams[0].accountStatus), // DCPF-25563 Hardhat result returning hex of accountStatus
          reasonCode: addAccountParams[0].reasonCode,
          appliedAt: addAccountParams[0].appliedAt,
          registeredAt: addAccountParams[0].registeredAt,
          terminatingAt: addAccountParams[0].terminatingAt,
          terminatedAt: addAccountParams[0].terminatedAt,
          mintLimit: 300,
          burnLimit: 400,
          chargeLimit: 800,
          dischargeLimit: 100,
          transferLimit: 100,
          cumulativeLimit: 500,
          cumulativeAmount: 0,
          cumulativeDate: 0,
        })
        utils.assertEqualForEachField(result.accounts[0].accountDataAll.cumulativeTransactionLimits, {
          cumulativeMintLimit: 100,
          cumulativeMintAmount: 0,
          cumulativeBurnLimit: 100,
          cumulativeBurnAmount: 0,
          cumulativeChargeLimit: 100,
          cumulativeChargeAmount: 0,
          cumulativeDischargeLimit: 100,
          cumulativeDischargeAmount: 0,
          cumulativeTransferLimit: 100,
          cumulativeTransferAmount: 0,
        })
      })

      it('limitが取得上限(100件)以下の場合、accountリストが取得できること (limit 100)', async () => {
        // limitが取得上限(100件)以下の場合は検索できる
        const offset = 0
        const limit = 100

        const result = await validatorFuncs.getAccountAllList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit],
        })
        utils.assertEqualForEachField(result, { totalCount: addAccountParams.length, err: '' })
        for (let i = 0; i < addAccountParams.length; i++) {
          assert.isString(result.accounts[i].accountId, 'accountId')
          assert.strictEqual(
            result.accounts[i].accountId,
            addAccountParams[addAccountParams.length - i - 1].accountId,
            'account id',
          )
          utils.assertEqualForEachField(result.accounts[i].accountDataAll, {
            accountName: addAccountParams[addAccountParams.length - i - 1].accountName,
            balance: addAccountParams[addAccountParams.length - i - 1].balance.toString(), // DCPF-25563 Hardhat result returning string
            accountStatus: utils.toBytes32(addAccountParams[addAccountParams.length - i - 1].accountStatus), // DCPF-25563 Hardhat result returning hex of accountStatus
            reasonCode: addAccountParams[addAccountParams.length - i - 1].reasonCode,
            appliedAt: addAccountParams[addAccountParams.length - i - 1].appliedAt,
            registeredAt: addAccountParams[addAccountParams.length - i - 1].registeredAt,
            terminatingAt: addAccountParams[addAccountParams.length - i - 1].terminatingAt,
            terminatedAt: addAccountParams[addAccountParams.length - i - 1].terminatedAt,
            mintLimit: 300,
            burnLimit: 400,
            chargeLimit: 800,
            dischargeLimit: 100,
            transferLimit: 100,
            cumulativeLimit: 500,
            cumulativeAmount: 0,
            cumulativeDate: 0,
          })
          utils.assertEqualForEachField(result.accounts[i].accountDataAll.cumulativeTransactionLimits, {
            cumulativeMintLimit: 100,
            cumulativeMintAmount: 0,
            cumulativeBurnLimit: 100,
            cumulativeBurnAmount: 0,
            cumulativeChargeLimit: 100,
            cumulativeChargeAmount: 0,
            cumulativeDischargeLimit: 100,
            cumulativeDischargeAmount: 0,
            cumulativeTransferLimit: 100,
            cumulativeTransferAmount: 0,
          })
        }
      })

      it('limitが0の場合、空リストが取得できること', async () => {
        const offset = 2
        const limit = 0

        const result = await validatorFuncs.getAccountAllList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit],
        })

        utils.assertEqualForEachField(result, { totalCount: 0, accounts: [], err: '' })
      })

      it('limitを登録数以上に指定した場合、登録されている要素数の範囲内でのみ取得できること', async () => {
        const offset = 0
        const limit = 50

        const result = await validatorFuncs.getAccountAllList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit],
        })

        utils.assertEqualForEachField(result, { totalCount: addAccountParams.length, err: '' })
        for (let i = 0; i < addAccountParams.length; i++) {
          assert.isString(result.accounts[i].accountId, 'accountId')
          assert.strictEqual(
            result.accounts[i].accountId,
            addAccountParams[addAccountParams.length - i - 1].accountId,
            'account id',
          )
          utils.assertEqualForEachField(result.accounts[i].accountDataAll, {
            accountName: addAccountParams[addAccountParams.length - i - 1].accountName,
            balance: addAccountParams[addAccountParams.length - i - 1].balance.toString(), // DCPF-25563 Hardhat result returning string
            accountStatus: utils.toBytes32(addAccountParams[addAccountParams.length - i - 1].accountStatus), // DCPF-25563 Hardhat result returning hex of accountStatus
            reasonCode: addAccountParams[addAccountParams.length - i - 1].reasonCode,
            appliedAt: addAccountParams[addAccountParams.length - i - 1].appliedAt,
            registeredAt: addAccountParams[addAccountParams.length - i - 1].registeredAt,
            terminatingAt: addAccountParams[addAccountParams.length - i - 1].terminatingAt,
            terminatedAt: addAccountParams[addAccountParams.length - i - 1].terminatedAt,
            mintLimit: 300,
            burnLimit: 400,
            chargeLimit: 800,
            dischargeLimit: 100,
            transferLimit: 100,
            cumulativeLimit: 500,
            cumulativeAmount: 0,
            cumulativeDate: 0,
          })
          utils.assertEqualForEachField(result.accounts[i].accountDataAll.cumulativeTransactionLimits, {
            cumulativeMintLimit: 100,
            cumulativeMintAmount: 0,
            cumulativeBurnLimit: 100,
            cumulativeBurnAmount: 0,
            cumulativeChargeLimit: 100,
            cumulativeChargeAmount: 0,
            cumulativeDischargeLimit: 100,
            cumulativeDischargeAmount: 0,
            cumulativeTransferLimit: 100,
            cumulativeTransferAmount: 0,
          })
        }
      })

      it('limitが取得上限(100件)より大きい場合、エラーが返されること', async () => {
        // limitが取得上限(100件)より大きい場合はエラーになる
        const offset = 0
        const limit = 101

        const result = await validatorFuncs.getAccountList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit, sortOrder],
        })

        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACCOUNT.ACCOUNT_TOO_LARGE_LIMIT })
      })

      it('offsetが登録されている件数以上の場合、エラーが返されること', async () => {
        // offsetが指定可能な上限値を超えているためエラー
        const offset = addAccountParams.length + 1
        const limit = 20

        const result = await validatorFuncs.getAccountList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit, sortOrder],
        })

        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACCOUNT.ACCOUNT_OFFSET_OUT_OF_INDEX })
      })

      it('offsetが登録されている件数と同じの場合、エラーが返されること', async () => {
        const offset = addAccountParams.length // 配列の長さと同じ
        const limit = 20

        const result = await validatorFuncs.getAccountList({
          validator,
          prams: [BASE.VALID.VALID0.ID, offset, limit, sortOrder],
        })

        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACCOUNT.ACCOUNT_OFFSET_OUT_OF_INDEX })
      })
    })
  })
})
