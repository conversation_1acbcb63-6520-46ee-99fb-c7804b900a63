import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { before } from 'mocha'

describe('hasAccount()', () => {
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
  }

  const setupBasicRolesWithAccount = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({
      provider,
      accounts,
      options: {
        providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
      },
    })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
    await validatorFuncs.addAccount({ validator, accounts })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('provider, providerRole, issuer, validator, token, accountが登録されている状態', () => {
      before(async () => {
        await setupBasicRolesWithAccount()
      })

      it('accountIdが存在すること', async () => {
        const result = await validatorFuncs.hasAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        utils.assertEqualForEachField(result, { success: true, err: '' })
      })

      it('空accountIdを指定した場合、エラーが返されること', async () => {
        const result = await validatorFuncs.hasAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, utils.toBytes32('')],
        })

        utils.assertEqualForEachField(result, { success: false, err: ERR.VALID.VALIDATOR_INVALID_VAL })
      })

      it('紐付けられていないアカウントを指定した場合、エラーが返されること', async () => {
        const result = await validatorFuncs.hasAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, utils.toBytes32('x110')],
        })

        utils.assertEqualForEachField(result, { success: false, err: ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('provider, providerRole, issuer, validator, token, accountが登録されている状態', () => {
      before(async () => {
        await setupBasicRolesWithAccount()
      })

      it('validatorIdが不正の場合、エラーがスローされること', async () => {
        const result = await validatorFuncs.hasAccount({
          validator,
          prams: [BASE.VALID.EMPTY.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        utils.assertEqualForEachField(result, { success: false, err: ERR.VALID.VALIDATOR_INVALID_VAL })
      })

      it('validatorIdが存在しない場合場合、エラーがスローされること', async () => {
        const result = await validatorFuncs.hasAccount({
          validator,
          prams: [BASE.VALID.VALID10.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        utils.assertEqualForEachField(result, { success: false, err: ERR.VALID.VALIDATOR_ID_NOT_EXIST })
      })
    })
  })
})
