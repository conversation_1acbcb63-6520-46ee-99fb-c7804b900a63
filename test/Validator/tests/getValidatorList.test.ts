import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { before } from 'mocha'
import { PromiseType } from 'utility-types'

// Chai global vars
declare let assert: Chai.Assert

describe('getValidatorList()', () => {
  const addValidatorParams = [
    { validator: BASE.VALID.VALID0, issuer: BASE.ISSUER.ISSUER0 },
    { validator: BASE.VALID.VALID1, issuer: BASE.ISSUER.ISSUER1 },
    { validator: BASE.VALID.VALID2, issuer: BASE.ISSUER.ISSUER2 },
    { validator: BASE.VALID.VALID3, issuer: BASE.ISSUER.ISSUER3 },
    { validator: BASE.VALID.VALID4, issuer: BASE.ISSUER.ISSUER4 },
    { validator: BASE.VALID.VALID5, issuer: BASE.ISSUER.ISSUER5 },
    { validator: BASE.VALID.VALID6, issuer: BASE.ISSUER.ISSUER6 },
    { validator: BASE.VALID.VALID7, issuer: BASE.ISSUER.ISSUER7 },
    { validator: BASE.VALID.VALID8, issuer: BASE.ISSUER.ISSUER8 },
    { validator: BASE.VALID.VALID9, issuer: BASE.ISSUER.ISSUER9 },
    { validator: BASE.VALID.VALID10, issuer: BASE.ISSUER.ISSUER10 },
    { validator: BASE.VALID.VALID11, issuer: BASE.ISSUER.ISSUER11 },
    { validator: BASE.VALID.VALID12, issuer: BASE.ISSUER.ISSUER12 },
    { validator: BASE.VALID.VALID13, issuer: BASE.ISSUER.ISSUER13 },
    { validator: BASE.VALID.VALID14, issuer: BASE.ISSUER.ISSUER14 },
    { validator: BASE.VALID.VALID15, issuer: BASE.ISSUER.ISSUER15 },
    { validator: BASE.VALID.VALID16, issuer: BASE.ISSUER.ISSUER16 },
    { validator: BASE.VALID.VALID17, issuer: BASE.ISSUER.ISSUER17 },
    { validator: BASE.VALID.VALID18, issuer: BASE.ISSUER.ISSUER18 },
    { validator: BASE.VALID.VALID19, issuer: BASE.ISSUER.ISSUER19 },
  ]

  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
  }

  const setupBasicRoles = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({
      provider,
      accounts,
      options: {
        providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
      },
    })
  }

  const setupValidators = async () => {
    const deadline = await utils.getDeadline()
    for (let i = 0; i < addValidatorParams.length; i++) {
      // 20件分のaddIssuer/addValidatorを連続実行することで "sig timeout" が発生するためdeadlineにさらに40秒加算している
      await issuerFuncs.addIssuer({
        issuer,
        accounts,
        options: {
          deadline: deadline + 40,
          issuerId: addValidatorParams[i].issuer.ID,
          bankCode: addValidatorParams[i].issuer.BANK_CODE,
          name: addValidatorParams[i].issuer.NAME,
        },
      })
      await validatorFuncs.addValidator({
        validator,
        accounts,
        options: {
          deadline: deadline + 40,
          validatorId: addValidatorParams[i].validator.ID,
          name: addValidatorParams[i].validator.NAME,
          issuerId: addValidatorParams[i].issuer.ID,
        },
      })
    }
  }

  describe('正常系', () => {
    const assertList = (
      result: PromiseType<ReturnType<typeof validatorFuncs.getValidatorList>>,
      expected: typeof addValidatorParams,
    ) => {
      expected.map((v, i) => {
        utils.assertEqualForEachField(result.validators[i], {
          validatorId: v.validator.ID,
          name: v.validator.NAME,
          issuerId: v.issuer.ID,
        })
      })
    }

    describe('validatorが登録されていない状態', () => {
      before(async () => {
        await setupFixture()
        await setupBasicRoles()
      })

      it('空リストが取得できること', async () => {
        const result = await validatorFuncs.getValidatorList({ validator, prams: [3, 0] })
        utils.assertEqualForEachField(result, { totalCount: 0, err: '' })
        assertList(result, [])
      })
    })

    describe('provider, providerRole, issuer, validatorが登録されている状態', () => {
      before(async () => {
        await setupFixture()
        await setupBasicRoles()
        await setupValidators()
      })

      it('複数のgetValidatorList呼び出しが同時に行われた場合、それぞれ正しい結果を返すこと', async () => {
        // 同時に異なるoffsetとlimitでリクエストを発行
        const requests = [
          validatorFuncs.getValidatorList({ validator, prams: [3, 0] }),
          validatorFuncs.getValidatorList({ validator, prams: [4, 2] }),
          validatorFuncs.getValidatorList({ validator, prams: [5, 5] }),
        ]

        const results = await Promise.all(requests)

        // 各リクエストの結果を検証
        utils.assertEqualForEachField(results[0], { totalCount: addValidatorParams.length, err: '' })
        assertList(results[0], addValidatorParams.slice(0, 3))

        utils.assertEqualForEachField(results[1], { totalCount: addValidatorParams.length, err: '' })
        assertList(results[1], addValidatorParams.slice(2, 6))

        utils.assertEqualForEachField(results[2], { totalCount: addValidatorParams.length, err: '' })
        assertList(results[2], addValidatorParams.slice(5, 10))
      })

      it('offset0, limit10を指定した場合、1要素目から10件取得できること', async () => {
        const offset = 0
        const limit = 10

        const result = await validatorFuncs.getValidatorList({ validator, prams: [limit, offset] })
        utils.assertEqualForEachField(result, { totalCount: addValidatorParams.length, err: '' })
        assertList(result, addValidatorParams.slice(0, 10))
      })

      it('offset1, limit10を指定した場合、2要素目から10件取得できること', async () => {
        const offset = 1
        const limit = 10

        const result = await validatorFuncs.getValidatorList({ validator, prams: [limit, offset] })

        utils.assertEqualForEachField(result, { totalCount: addValidatorParams.length, err: '' })
        assertList(result, addValidatorParams.slice(1, 11))
      })

      it('offset2, limit2を指定した場合、3要素目から2件取得できること', async () => {
        const offset = 2
        const limit = 2

        const result = await validatorFuncs.getValidatorList({ validator, prams: [limit, offset] })

        utils.assertEqualForEachField(result, { totalCount: addValidatorParams.length, err: '' })
        assertList(result, [addValidatorParams[2], addValidatorParams[3]])
      })

      it('最後の1件が取得できること', async () => {
        const offset = 19
        const limit = 1

        const result = await validatorFuncs.getValidatorList({ validator, prams: [limit, offset] })

        utils.assertEqualForEachField(result, { totalCount: addValidatorParams.length, err: '' })
        assertList(result, [addValidatorParams[addValidatorParams.length - 1]])
      })

      it('limitが取得上限(100件)以下の場合、データが取得ができること', async () => {
        const offset = 0
        const limit = 100

        const result = await validatorFuncs.getValidatorList({ validator, prams: [limit, offset] })

        utils.assertEqualForEachField(result, { totalCount: addValidatorParams.length, err: '' })
        assertList(result, addValidatorParams)
      })

      it('limitが0の場合、空リストが取得できること', async () => {
        const offset = 2
        const limit = 0

        const result = await validatorFuncs.getValidatorList({ validator, prams: [limit, offset] })

        assert.equal(result.totalCount, 0, 'validator count')
        assertList(result, [])
      })

      it('limitを登録数以上に指定した場合、登録されている要素数の範囲内でのみ取得できること', async () => {
        const offset = 0
        const limit = 50

        const result = await validatorFuncs.getValidatorList({ validator, prams: [limit, offset] })

        assertList(result, addValidatorParams.slice(0, 20))
      })

      it('limitが取得上限(100件)より大きい場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 101

        const result = await validatorFuncs.getValidatorList({ validator, prams: [limit, offset] })

        assert.equal(result.err, ERR.VALID.VALIDATOR_TOO_LARGE_LIMIT, 'err: too large limit')
      })

      it('offsetが登録されている件数以上の場合、エラーが返されること', async () => {
        const offset = addValidatorParams.length + 1
        const limit = 20

        const result = await validatorFuncs.getValidatorList({ validator, prams: [limit, offset] })

        assert.equal(result.err, ERR.VALID.VALIDATOR_OFFSET_OUT_OF_INDEX, 'err: out of index (offset)')
      })

      it('offsetが登録されている件数と同じ場合、エラーが返されること', async () => {
        const offset = addValidatorParams.length
        const limit = 20

        const result = await validatorFuncs.getValidatorList({ validator, prams: [limit, offset] })

        assert.equal(result.err, ERR.VALID.VALIDATOR_OFFSET_OUT_OF_INDEX, 'err: out of index (offset)')
      })
    })
  })
})
