import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { accountFuncs } from '@test/Account/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  AccountInstance,
  AddAccountOption,
  IssuerInstance,
  ProviderInstance,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('addAccount()', () => {
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let account: AccountInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, account } = await contractFixture<ValidatorContractType>())
  }

  const setupProvider = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
  }

  const setupProviderRole = async () => {
    await providerFuncs.addProviderRole({
      provider,
      accounts,
      options: {
        providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
      },
    })
  }

  const setupIssuer = async () => {
    await issuerFuncs.addIssuer({ issuer, accounts })
  }

  const setupValidator = async () => {
    await validatorFuncs.addValidator({ validator, accounts })
  }

  const setupToken = async () => {
    await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
  }

  const setupFullEnvironment = async () => {
    await setupProvider()
    await setupProviderRole()
    await setupIssuer()
    await setupValidator()
    await setupToken()
  }

  const setupBasicEnvironment = async () => {
    await providerFuncs.addProvider({ provider, accounts })
    await setupIssuer()
    await setupValidator()
  }

  const setupAccountTest = async () => {
    await validatorFuncs.addAccount({
      validator,
      accounts,
    })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('provider, providerRole, issuer, validator, tokenが登録されている状態', () => {
      before(async () => {
        await setupFullEnvironment()
      })

      it('accountが追加できること', async () => {
        const params: AddAccountOption = {
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          validatorId: BASE.VALID.VALID0.ID,
        }

        const tx = await validatorFuncs.addAccount({ validator, accounts, options: params })
        await expect(tx)
          .to.emit(validator, 'AddAccount')
          .withArgs(
            params.validatorId,
            params.accountId,
            false,
            true,
            [
              BASE.LIMIT_VALUES.mint,
              BASE.LIMIT_VALUES.burn,
              BASE.LIMIT_VALUES.charge,
              BASE.LIMIT_VALUES.discharge,
              BASE.LIMIT_VALUES.transfer,
              [
                BASE.LIMIT_VALUES.cumulative.total,
                BASE.LIMIT_VALUES.cumulative.mint,
                BASE.LIMIT_VALUES.cumulative.burn,
                BASE.LIMIT_VALUES.cumulative.charge,
                BASE.LIMIT_VALUES.cumulative.discharge,
                BASE.LIMIT_VALUES.cumulative.transfer,
              ],
            ],
            BASE.TRACE_ID,
          )

        const accountData = await validatorFuncs.getAccount({
          validator,
          prams: [params.validatorId, params.accountId],
        })

        const expectedObj = {
          accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          accountStatus: BASE.STATUS.ACTIVE,
          balance: 0,
          reasonCode: BASE.REASON_CODE1,
          appliedAt: 0,
          registeredAt: await utils.getLatestBlockTimestamp(),
          terminatingAt: 0,
          terminatedAt: 0,
        }
        utils.assertEqualForEachField(accountData.accountData, expectedObj)

        const issuerHasAccountResult = await issuerFuncs.hasAccount({
          issuer,
          params: [BASE.ISSUER.ISSUER0.ID, params.accountId],
        })
        utils.assertEqualForEachField(issuerHasAccountResult, { success: true, err: '' })

        const validatorHasAccountResult = await validatorFuncs.hasAccount({
          validator,
          prams: [params.validatorId, params.accountId],
        })
        utils.assertEqualForEachField(validatorHasAccountResult, { success: true, err: '' })

        const validatorIdByAccountIdResult = await accountFuncs.getValidatorIdByAccountId({
          account,
          params: [params.accountId],
        })
        utils.assertEqualForEachField(validatorIdByAccountIdResult, { validatorId: params.validatorId, err: '' })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('validatorが未登録の場合、エラーがスローされること', async () => {
        const result = validatorFuncs.addAccount({ validator, accounts })
        await expect(result).to.be.revertedWith(ERR.VALID.VALIDATOR_ID_NOT_EXIST)
      })
    })

    describe('accountが登録されている状態', () => {
      it('空validatorIdを指定した場合、エラーがスローされること', async () => {
        const result = validatorFuncs.addAccount({
          validator,
          accounts,
          options: { validatorId: BASE.VALID.EMPTY.ID },
        })
        await expect(result).to.be.revertedWith(ERR.VALID.VALIDATOR_INVALID_VAL)
      })
    })

    describe('accountId not exist or registered', () => {
      before(async () => {
        await setupBasicEnvironment()
      })
      it('should revert when accountId not exist', async () => {
        const result = validatorFuncs.addAccount({
          validator,
          accounts,
          options: { accountId: BASE.ACCOUNT.EMPTY.ID },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })
    })

    describe('accountが登録されている状態', () => {
      before(async () => {
        await setupAccountTest()
      })
      it('account重複登録の場合、エラーがスローされること', async () => {
        const result = validatorFuncs.addAccount({
          validator,
          accounts,
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_EXIST)
      })
    })
  })
})
