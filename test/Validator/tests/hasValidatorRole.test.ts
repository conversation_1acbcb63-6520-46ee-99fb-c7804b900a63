import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('hasValidatorRole()', () => {
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
  }

  const setupBasicRoles = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({
      provider,
      accounts,
      options: {
        providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
      },
    })
    await issuerFuncs.addIssuer({
      issuer,
      accounts,
      options: {
        issuerId: BASE.ISSUER.ISSUER0.ID,
        bankCode: BASE.ISSUER.ISSUER0.BANK_CODE,
      },
    })
    await issuerFuncs.addIssuer({
      issuer,
      accounts,
      options: {
        issuerId: BASE.ISSUER.ISSUER1.ID,
        bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
      },
    })
    await validatorFuncs.addValidator({ validator, accounts })
    await validatorFuncs.addValidatorRole({ validator, accounts })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })
    describe('Validatorが登録されている状態', () => {
      before('', async () => {
        await setupBasicRoles()
      })

      it('roleがある場合、trueが取得できること', async () => {
        const result = await validatorFuncs.hasValidatorRole({ validator: validator })
        utils.assertEqualForEachField(result, { success: true, err: '' })
      })

      it('roleがない場合、falseが取得できること', async () => {
        const result = await validatorFuncs.hasValidatorRole({
          validator,
          options: { eoaKey: BASE.EOA.VALID2 },
        })

        utils.assertEqualForEachField(result, { success: false, err: '' })
      })

      it('空validatorIdを指定した場合、エラーが返されること', async () => {
        const result = await validatorFuncs.hasValidatorRole({
          validator,
          options: { validatorId: BASE.VALID.EMPTY.ID },
        })

        assert.equal(result.err, ERR.VALID.VALIDATOR_INVALID_VAL, 'err')
      })

      it('署名期限切れの場合、エラーが返されること', async () => {
        const exceededDeadline = await utils.getExceededDeadline()

        const result = await validatorFuncs.hasValidatorRole({
          validator,
          options: { deadline: exceededDeadline },
        })

        assert.equal(result.err, ERR.ACTRL.ACTRL_SIG_TIMEOUT, 'err')
      })
    })

    describe('Validatorが無効の状態', () => {
      it('roleがある場合、trueが取得できること', async () => {
        const result = await validatorFuncs.hasValidatorRole({
          validator,
          options: { validatorId: BASE.VALID.VALID0.ID },
        })

        utils.assertEqualForEachField(result, { success: true, err: '' })
      })
    })
  })
})
