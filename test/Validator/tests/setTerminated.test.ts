import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, TokenInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('setTerminated()', () => {
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let token: TokenInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, token } = await contractFixture<ValidatorContractType>())
  }

  const setupBasicRolesWithAccount = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({
      provider,
      accounts,
      options: {
        providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
      },
    })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
    await validatorFuncs.addAccount({ validator, accounts })
  }

  const setupBasicRolesWithAccountAndIssuerRole = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({
      provider,
      accounts,
      options: {
        providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
      },
    })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
    await validatorFuncs.addAccount({ validator, accounts })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('provider, providerRole, issuer, validator, token, accountが登録されている状態', () => {
      before(async () => {
        await setupBasicRolesWithAccount()
      })

      it('accountの解約フラグを更新できること', async () => {
        const tx = await validatorFuncs.setTerminated({ validator, accounts })

        const expectParams = {
          zoneId: BASE.ZONE_ID.ID0,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          reasonCode: BASE.REASON_CODE2,
          traceId: BASE.TRACE_ID,
        }
        await expect(tx)
          .to.emit(validator, 'SetTerminated')
          .withArgs(...Object.values(expectParams))

        const result = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        const expectedObj = {
          accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          accountStatus: BASE.STATUS.TERMINATED,
          balance: 0,
          reasonCode: BASE.REASON_CODE2,
          appliedAt: 0,
          registeredAt: await utils.getLatestBlockTimestamp(),
          terminatingAt: 0,
          terminatedAt: await utils.getLatestBlockTimestamp(),
        }
        utils.assertEqualForEachField(result.accountData, expectedObj)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('provider, providerRole, issuer, validator, token, accountが登録されている状態', () => {
      before(async () => {
        await setupBasicRolesWithAccountAndIssuerRole()
      })

      it('未登録validatorIdを指定した場合、エラーが返されること', async () => {
        const result = validatorFuncs.setTerminated({
          validator,
          accounts,
          options: { validatorId: BASE.VALID.VALID1.ID },
        })
        await expect(result).to.be.revertedWith(ERR.VALID.VALIDATOR_ID_NOT_EXIST)
      })

      it('accountIdが存在しない場合、エラーがスローされること', async () => {
        const result = validatorFuncs.setTerminated({
          validator,
          accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT2.ID },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })

      it('should revert when accountId balance is not 0', async () => {
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        const result = validatorFuncs.setTerminated({
          validator,
          accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_BALANCE_NOT_ZERO)
      })
    })
    // provider.getZoneでエラーがあるケースは、事前準備ができないため未実施
  })
})
