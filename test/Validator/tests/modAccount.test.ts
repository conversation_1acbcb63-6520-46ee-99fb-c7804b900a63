import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountInstance, IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('modAccount', () => {
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let account: AccountInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, account } = await contractFixture<ValidatorContractType>())
  }

  const setupBasicRolesWithAccount = async () => {
    const param = [{ issuer: BASE.ISSUER.ISSUER0 }, { issuer: BASE.ISSUER.ISSUER1 }, { issuer: BASE.ISSUER.ISSUER2 }]

    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({
      provider,
      accounts,
      options: {
        providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
      },
    })
    for (let i = 0; i < param.length; i++) {
      await issuerFuncs.addIssuer({
        issuer,
        accounts,
        options: {
          issuerId: param[i].issuer.ID,
          name: param[i].issuer.NAME,
          bankCode: param[i].issuer.BANK_CODE,
        },
      })
    }
    await validatorFuncs.addValidator({ validator, accounts })
    await validatorFuncs.addValidatorRole({ validator, accounts })
    await validatorFuncs.addAccount({ validator, accounts })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('accountが登録されていない状態', () => {
      before(async () => {
        await setupBasicRolesWithAccount()
      })

      it('Accountが存在しない場合、エラーがスローされること', async () => {
        const tx = await validatorFuncs.modAccount({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
            traceId: BASE.TRACE_ID,
            deadline: await utils.getDeadline(),
          },
        })

        const expectParams = {
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          traceId: BASE.TRACE_ID,
        }
        await expect(tx)
          .to.emit(account, 'ModAccount')
          .withArgs(...Object.values(expectParams))
      })
    })
  })
})
