import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { before } from 'mocha'

describe('getValidator()', () => {
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
  }

  const setupBasicRoles = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({
      provider,
      accounts,
      options: {
        providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
      },
    })
  }

  const setupIssuersAndValidators = async () => {
    await issuerFuncs.addIssuer({
      issuer,
      accounts,
      options: {
        issuerId: BASE.ISSUER.ISSUER0.ID,
        name: BASE.ISSUER.ISSUER0.NAME,
        bankCode: BASE.ISSUER.ISSUER0.BANK_CODE,
      },
    })
    await issuerFuncs.addIssuer({
      issuer,
      accounts,
      options: {
        issuerId: BASE.ISSUER.ISSUER1.ID,
        name: BASE.ISSUER.ISSUER1.NAME,
        bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
      },
    })
    for (const param of [
      { issuer: BASE.ISSUER.ISSUER0, validator: BASE.VALID.VALID0 },
      { issuer: BASE.ISSUER.ISSUER1, validator: BASE.VALID.VALID1 },
    ]) {
      await validatorFuncs.addValidator({
        validator,
        accounts,
        options: {
          validatorId: param.validator.ID,
          name: param.validator.NAME,
          issuerId: param.issuer.ID,
        },
      })
    }
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('provider, providerRole, issuer, validatorが登録されている状態', () => {
      before(async () => {
        await setupBasicRoles()
        await setupIssuersAndValidators()
      })

      it('validator情報が取得できること', async () => {
        let result = await validatorFuncs.getValidator({ validator, prams: [BASE.VALID.VALID0.ID] })

        utils.assertEqualForEachField(result, {
          name: BASE.VALID.VALID0.NAME,
          issuerId: BASE.ISSUER.ISSUER0.ID,
          err: '',
        })

        result = await validatorFuncs.getValidator({ validator, prams: [BASE.VALID.VALID1.ID] })

        utils.assertEqualForEachField(result, {
          name: BASE.VALID.VALID1.NAME,
          issuerId: BASE.ISSUER.ISSUER1.ID,
          err: '',
        })
      })

      it('未登録validatorIdを指定した場合、エラーが返されること', async () => {
        const result = await validatorFuncs.getValidator({ validator, prams: [utils.toBytes32('x102')] })

        utils.assertEqualForEachField(result, {
          name: BASE.VALID.EMPTY.NAME,
          issuerId: BASE.ISSUER.EMPTY.ID,
          err: ERR.VALID.VALIDATOR_ID_NOT_EXIST,
        })
      })
    })
  })
})
