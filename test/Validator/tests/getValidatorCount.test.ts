import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('getValidatorCount()', () => {
  const param = [
    { issuer: BASE.ISSUER.ISSUER0, validator: BASE.VALID.VALID0 },
    { issuer: BASE.ISSUER.ISSUER1, validator: BASE.VALID.VALID1 },
    { issuer: BASE.ISSUER.ISSUER2, validator: BASE.VALID.VALID2 },
  ]

  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
  }

  const setupBasicRoles = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({
      provider,
      accounts,
      options: {
        providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
      },
    })
  }

  const setupIssuersAndValidators = async () => {
    for (let i = 0; i < param.length; i++) {
      await issuerFuncs.addIssuer({
        issuer,
        accounts,
        options: {
          issuerId: param[i].issuer.ID,
          name: param[i].issuer.NAME,
          bankCode: param[i].issuer.BANK_CODE,
        },
      })
      await validatorFuncs.addValidator({
        validator,
        accounts,
        options: {
          validatorId: param[i].validator.ID,
          name: param[i].validator.NAME,
          issuerId: param[i].issuer.ID,
        },
      })
    }
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('provider, providerRole, issuer, validatorが登録されている状態', () => {
      before(async () => {
        await setupBasicRoles()
        await setupIssuersAndValidators()
      })

      it('validator数が取得できること', async () => {
        const result = await validatorFuncs.getValidatorCount({ validator: validator })

        assert.equal(result, 3, 'validator count')
      })
    })
  })
})
