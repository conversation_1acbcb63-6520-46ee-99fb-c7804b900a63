import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { toBytes32 } from '@test/common/utils'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'
import { PromiseType } from 'utility-types'

// Chai global vars
declare let assert: Chai.Assert

describe('setValidatorAll()', () => {
  let validator: ValidatorInstance
  let accounts: SignerWithAddress[]

  let createParams

  const setupFixture = async () => {
    ;({ accounts, validator } = await contractFixture<ValidatorContractType>())
  }

  const createParamsFunction = () => {
    return async (accounts: any, num: number) =>
      [...Array(num).keys()].map((index) => {
        return {
          validatorId: toBytes32(`x${index}`),
          name: toBytes32(`NAME${index}`),
          issuerId: toBytes32(`x${index}`),
          role: toBytes32(`ROLE${index}`),
          validatorAccountId: toBytes32(`x${index}`),
          enabled: true,
          validatorIdExistence: true,
          issuerIdLinkedFlag: true,
          validatorEoa: accounts[index].getAddress(),
          validAccountExistence: [
            {
              accountId: toBytes32(`VALIDATOR${index}_ACCOUNT1`),
              accountIdExistenceByValidatorId: true,
            },
            {
              accountId: toBytes32(`VALIDATOR${index}_ACCOUNT2`),
              accountIdExistenceByValidatorId: true,
            },
          ],
        }
      })
  }

  const setupWithCreateParams = async () => {
    await setupFixture()
    createParams = createParamsFunction()
  }

  describe('正常系', () => {
    before(async () => {
      await setupWithCreateParams()
    })

    describe('初期状態', () => {
      let validatorsPram
      const assertList = (result: PromiseType<ReturnType<typeof validatorFuncs.getValidatorAll>>, expected) => {
        assert.isString(result.role, 'role')
        utils.assertEqualForEachField(result, {
          validatorId: expected.validatorId,
          name: expected.name,
          issuerId: expected.issuerId,
          enabled: expected.enabled,
          validatorIdExistence: expected.validatorIdExistence,
          issuerIdLinkedFlag: expected.issuerIdLinkedFlag,
          validatorEoa: expected.validatorEoa,
        })
        expected.validAccountExistence.forEach((v, i) => {
          utils.assertEqualForEachField(result.validAccountExistence[i], {
            accountId: v.accountId,
            accountIdExistenceByValidatorId: v.accountIdExistenceByValidatorId,
          })
        })
      }
      it('全てのissuers(20件)が登録できること', async () => {
        validatorsPram = createParams(accounts, 20)
        for (let i = 0; i < validatorsPram.length; i++) {
          await validatorFuncs.setValidatorAll({ valid: validator, validator: validatorsPram[i], accounts })
          const result = await validatorFuncs.getValidatorAll({ validator, index: i })
          assertList(result, validatorsPram[i])
        }
      })
    })
  })

  describe('準正常系', () => {
    let validatorsPram

    before(async () => {
      await setupWithCreateParams()
      validatorsPram = await createParams(accounts, 1)
    })

    describe('初期状態', () => {
      it('Admin権限がない場合、エラーがスローされること', async () => {
        const result = validatorFuncs.setValidatorAll({
          valid: validator,
          validator: validatorsPram[0],
          accounts,
          options: { eoaKey: BASE.EOA.ISSUER1 },
        })
        await expect(result).to.be.revertedWith(ERR.VALID.VALIDATOR_NOT_ADMIN_ROLE)
      })

      it('署名無効の場合、エラーがスローされること', async () => {
        const result = validatorFuncs.setValidatorAll({
          valid: validator,
          validator: validatorsPram[0],
          accounts,
          options: { sig: ['0x1234', ''] },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const now = await utils.getExceededDeadline()
        const result = validatorFuncs.setValidatorAll({
          valid: validator,
          validator: validatorsPram[0],
          accounts,
          options: { deadline: now },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })
      it('異常な値が入力された時にfails', async () => {
        const validatorsIvalid = [
          {
            validatorId: '123',
            name: 'name',
            issuerId: '123',
            role: '456',
            validatorAccountId: '123',
            enabled: true,
            validatorIdExistence: true,
            issuerIdLinkedFlag: true,
            issuerEoa: '',
            validAccountExistence: [],
          },
        ]
        const result = validatorFuncs.setValidatorAll({ valid: validator, validator: validatorsIvalid[0], accounts })
        await expect(result).to.be.throw
      })
    })
  })
})
