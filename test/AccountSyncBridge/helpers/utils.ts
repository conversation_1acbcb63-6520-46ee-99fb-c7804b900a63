import { BASE } from '@test/common/consts'
import { genPacketData } from '@test/common/utils'
import Web3 from 'web3'

declare let web3: Web3

const genPacketDataLocal = (packetData, fromZoneId, _deadline, _sig) =>
  packetData ??
  web3.eth.abi.encodeParameters(
    // TODO: SyncBusinessZoneAccountParams に合わせる必要がある。EventからBridgeするように変更必要
    ['(bytes32, string, uint16, string, bytes32, bytes32)'],
    [
      [
        BASE.BRIDGE.ACCOUNT_A,
        BASE.BRIDGE.ACCOUNT_A_NAME,
        fromZoneId ?? BASE.ZONE.BIZ,
        BASE.ZONE.BIZ_NAME,
        BASE.STATUS.APPLYING,
        BASE.TRACE_ID,
      ],
    ],
  )

export const genPacket = (packetData, fromZoneId, _deadline, _sig) =>
  genPacketData(
    genPacketDataLocal(packetData, fromZoneId, _deadline, _sig),
    BASE.ACC_SYNC_BRIDGE.PORT,
    BASE.ACC_SYNC_BRIDGE.CHANNEL,
    BASE.TIMEOUT_HEIGHT,
  )
