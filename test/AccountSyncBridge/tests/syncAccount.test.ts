import '@nomicfoundation/hardhat-chai-matchers'
import { accountSyncBridgeFuncs } from '@test/AccountSyncBridge/helpers/function'
import { AccountSyncBridgeContractType } from '@test/AccountSyncBridge/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountSyncBridgeInstance, IBCTokenMockInstance, ValidatorMockInstance } from '@test/common/types'
import { assertEqualForEachField } from '@test/common/utils'
import { assert, expect } from 'chai'

describe('syncAccount', () => {
  let accountSyncBridge: AccountSyncBridgeInstance
  let validatorMock: ValidatorMockInstance
  let ibcTokenMock: IBCTokenMockInstance

  const setupFixture = async () => {
    ;({ accountSyncBridge, validatorMock, ibcTokenMock } = await contractFixture<AccountSyncBridgeContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('BizZoneアカウント申し込みを実行した場合、BizZoneのアカウント状態が更新されていること', async () => {
        // （テスト用）残高追加
        await ibcTokenMock.issueVoucher(BASE.BRIDGE.ACCOUNT_A, 300n, BASE.EMPTY.ID)
        await accountSyncBridgeFuncs.syncAccount({ accountSyncBridge })

        const accountData = await validatorMock.getAccount(BASE.BRIDGE.VALIDATOR_ID, BASE.BRIDGE.ACCOUNT_A)
        assert.equal(accountData[0].accountStatus, BASE.STATUS.ACTIVE, 'accountStatus')
        // （テスト用）残高取得
        const balance = await ibcTokenMock.balanceOf(1, BASE.BRIDGE.ACCOUNT_A)
        assertEqualForEachField(balance, { balance: 300n })
      })
      it('BizZoneアカウント申し込みを実行した場合、BizZoneのアカウント状態が更新されていること', async () => {
        await accountSyncBridgeFuncs.syncAccount({ accountSyncBridge })

        await ibcTokenMock.issueVoucher(BASE.ACCOUNT.ACCOUNT0.ID, 300, BASE.ACCOUNT.ACCOUNT0.ID)
        const test = await ibcTokenMock.balanceOf(0, BASE.ACCOUNT.ACCOUNT0.ID)

        const accountData = await validatorMock.getAccount(BASE.BRIDGE.VALIDATOR_ID, BASE.BRIDGE.ACCOUNT_A)
        assert.equal(accountData[0].accountStatus, BASE.STATUS.ACTIVE, 'accountStatus')
      })
      it('BizZoneアカウント解約申し込みを実行した場合、BizZoneのアカウント状態が更新されていること', async () => {
        await accountSyncBridgeFuncs.syncAccount({
          accountSyncBridge,
          options: {
            accountStatus: BASE.STATUS.TERMINATING,
          },
        })

        const accountData = await validatorMock.getAccount(BASE.BRIDGE.VALIDATOR_ID, BASE.BRIDGE.ACCOUNT_A)
        assert.equal(accountData[0].accountStatus, BASE.STATUS.TERMINATED, 'accountStatus')
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('不正なAccountStatusが設定された場合、エラーがスローされること', async () => {
        const result = accountSyncBridgeFuncs.syncAccount({
          accountSyncBridge,
          options: {
            accountStatus: BASE.STATUS.FROZEN,
          },
        })
        await expect(result).to.be.revertedWith(ERR.IBC.IBC_INVALID_VAL)
      })
      it('should revert when fin zone op not allowed', async () => {
        const result = accountSyncBridgeFuncs.syncAccount({
          accountSyncBridge,
          options: {
            fromZoneId: BASE.ZONE.FIN,
            accountStatus: BASE.STATUS.FROZEN,
          },
        })
        await expect(result).to.be.revertedWith(ERR.IBC.FIN_ZONE_OP_NOT_ALLOWED)
      })
    })
  })
})
