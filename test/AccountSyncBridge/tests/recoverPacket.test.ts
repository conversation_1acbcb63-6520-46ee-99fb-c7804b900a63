import privateKey from '@/privateKey'
import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { accountSyncBridgeFuncs } from '@test/AccountSyncBridge/helpers/function'
import { AccountSyncBridgeContractType } from '@test/AccountSyncBridge/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccessCtrlMockInstance, AccountSyncBridgeInstance, BusinessZoneAccountMockInstance } from '@test/common/types'
import { assert, expect } from 'chai'

describe('recoverPacket', () => {
  let accounts: SignerWithAddress[]
  let accountSyncBridge: AccountSyncBridgeInstance
  let accessCtrlMock: AccessCtrlMockInstance
  let businessZoneAccountMock: BusinessZoneAccountMockInstance

  const setupFixture = async () => {
    ;({ accounts, accountSyncBridge, accessCtrlMock, businessZoneAccountMock } =
      await contractFixture<AccountSyncBridgeContractType>())
  }

  const initData = async () => {
    await accessCtrlMock.addAdminRole(accounts[0], 0, BASE.TRACE_ID)
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
      await initData()
    })

    describe('初期状態', () => {
      it('BizZoneからpacketを受け取ることができた場合、FinZone管理のBizZoneアカウントが更新されること', async () => {
        await accountSyncBridgeFuncs.recoverPacket({ accountSyncBridge })

        const bizZoneAccountData = await businessZoneAccountMock.getBizZoneAccount(BASE.ZONE.BIZ, BASE.BRIDGE.ACCOUNT_A)

        assert.equal(bizZoneAccountData, BASE.STATUS.APPLYING, 'accountStatus')
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('ADMIN権限ではない場合、エラーが返却されること', async () => {
        const result = accountSyncBridgeFuncs.recoverPacket({
          accountSyncBridge,
          options: {
            privateKeyForSig: privateKey[1],
          },
        })
        await expect(result).to.be.revertedWith(ERR.COMMON.NOT_ADMIN_ROLE)
      })

      it('署名が無効の場合、エラーがスローされること', async () => {
        const result = accountSyncBridgeFuncs.recoverPacket({
          accountSyncBridge,
          options: {
            sig: ['0x12345678', ''],
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('署名が無効（not signature）の場合、エラーがスローされること', async () => {
        const bad_sig =
          '0x0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000'

        const result = accountSyncBridgeFuncs.recoverPacket({
          accountSyncBridge,
          options: {
            sig: [bad_sig, ''],
          },
        })
        await expect(result).to.be.reverted
      })
    })
  })
})
