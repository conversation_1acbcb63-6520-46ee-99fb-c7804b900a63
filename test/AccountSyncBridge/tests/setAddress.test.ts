import privateKey from '@/privateKey'
import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { accountSyncBridgeFuncs } from '@test/AccountSyncBridge/helpers/function'
import { AccountSyncBridgeContractType } from '@test/AccountSyncBridge/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  AccessCtrlMockInstance,
  AccountSyncBridgeInstance,
  BusinessZoneAccountMockInstance,
  IBCTokenMockInstance,
  ProviderMockInstance,
  ValidatorMockInstance,
} from '@test/common/types'
import { expect } from 'chai'

describe('setAddress()', () => {
  let accounts: SignerWithAddress[]
  let accountSyncBridge: AccountSyncBridgeInstance
  let providerMock: ProviderMockInstance
  let validatorMock: ValidatorMockInstance
  let accessCtrlMock: AccessCtrlMockInstance
  let businessZoneAccountMock: BusinessZoneAccountMockInstance
  let ibcTokenMock: IBCTokenMockInstance

  const setupFixture = async () => {
    ;({
      accounts,
      accountSyncBridge,
      providerMock,
      validatorMock,
      accessCtrlMock,
      businessZoneAccountMock,
      ibcTokenMock,
    } = await contractFixture<AccountSyncBridgeContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    it('_validator, _accessCtrl, _businessZoneAccount can be set normally by admin', async () => {
      await accessCtrlMock.addAdminRole(accounts[0], 0, BASE.TRACE_ID)
      const tx = await accountSyncBridgeFuncs.setAddress({
        accountSyncBridge,
        providerMockAddress: await providerMock.getAddress(),
        validatorMockAddress: await validatorMock.getAddress(),
        accessCtrlMockAddress: await accessCtrlMock.getAddress(),
        businessZoneAccountMockAddress: await businessZoneAccountMock.getAddress(),
        ibcTokenMockAddress: await ibcTokenMock.getAddress(),
      })

      // TODO: setAddress don't have event and variable is private
      // Should change this to check the value of the variable when event is implemented
      await expect(tx).to.be.ok
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    it('should revert when not admin', async () => {
      const result = accountSyncBridgeFuncs.setAddress({
        accountSyncBridge,
        providerMockAddress: await providerMock.getAddress(),
        validatorMockAddress: await validatorMock.getAddress(),
        accessCtrlMockAddress: await accessCtrlMock.getAddress(),
        businessZoneAccountMockAddress: await businessZoneAccountMock.getAddress(),
        ibcTokenMockAddress: await ibcTokenMock.getAddress(),
        options: {
          privateKeyForSig: privateKey[1],
        },
      })
      await expect(result).to.be.revertedWith(ERR.COMMON.NOT_ADMIN_ROLE)
    })
  })
})
