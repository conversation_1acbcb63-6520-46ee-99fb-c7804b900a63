import '@nomicfoundation/hardhat-chai-matchers'
import { AccountSyncBridgeContractType } from '@test/AccountSyncBridge/helpers/types'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountSyncBridgeInstance } from '@test/common/types'
import { assert } from 'chai'

describe('getConfig()', () => {
  let accountSyncBridge: AccountSyncBridgeInstance

  const setupFixture = async () => {
    ;({ accountSyncBridge } = await contractFixture<AccountSyncBridgeContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })
    describe('初期状態', () => {
      it('version情報を取得できること', async () => {
        const config = await accountSyncBridge.getConfig()
        assert.equal(config.port, BASE.ACC_SYNC_BRIDGE.PORT, 'source port')
        assert.equal(config.channel, BASE.ACC_SYNC_BRIDGE.CHANNEL, 'source channel')
        assert.equal(config.version, BASE.ACC_SYNC_BRIDGE.VERSION, 'source version')
      })
    })
  })
})
