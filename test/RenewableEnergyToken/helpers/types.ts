/* eslint-disable @typescript-eslint/no-explicit-any */
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccessCtrlInstance,
  AccountInstance,
  ContractCallOption,
  ContractManagerInstance,
  EventReturnType,
  IssuerInstance,
  ProviderInstance,
  RenewableEnergyTokenInstance,
  TokenInstance,
  TransferableMock1Instance,
  TransferableMock2Instance,
  TransferableMock3Instance,
  TransferProxyInstance,
  ValidatorInstance,
} from '@test/common/types'
import { ContractTransactionResponse } from 'ethers'

export type RenewableEnergyTokenContractType = {
  accounts: SignerWithAddress[]
  provider: ProviderInstance
  issuer: IssuerInstance
  validator: ValidatorInstance
  account: AccountInstance
  token: TokenInstance
  renewableEnergyToken: RenewableEnergyTokenInstance
  transferProxy: TransferProxyInstance
  contractManager: ContractManagerInstance
  customTransfer1: TransferableMock1Instance
  customTransfer2: TransferableMock2Instance
  customTransfer3: TransferableMock3Instance
  accessCtrl: AccessCtrlInstance
}

type RenewableEnergyTokenType = { renewableEnergyToken: RenewableEnergyTokenInstance }

type BaseGetListType = RenewableEnergyTokenType & {
  offset: number
  limit: number
}

type BaseTransferType = RenewableEnergyTokenType & {
  fromAccountId: string
  toAccountId: string
}

export type FuncParamsType = {
  version: RenewableEnergyTokenType
  getTokenList: BaseGetListType & {
    validatorId: string
    accountId: string
    sortOrder: string
  }
  getToken: RenewableEnergyTokenType & {
    options: Parameters<RenewableEnergyTokenInstance['getToken']>
  }
  getTokenCount: RenewableEnergyTokenType & {
    options: Parameters<RenewableEnergyTokenInstance['getTokenCount']>
  }
  checkTransaction: BaseTransferType & {
    sendAccountId: string
    miscValue1: string
    miscValue2: string
  }
  mint: RenewableEnergyTokenType & {
    tokenId: string
    metadataId: string
    metadataHash: string
    mintAccountId: string
    ownerAccountId: string
    isLocked: boolean
  }
  transfer: BaseTransferType & {
    tokenId: string
  }
  backupRenewableEnergyTokens: BaseGetListType & {
    options?: Partial<ContractCallOption>
  }
  restoreRenewableEnergyTokens: RenewableEnergyTokenType & {
    params: any
    options?: Partial<ContractCallOption>
  }
  backupTokenIdsByAccountIds: BaseGetListType & {
    options?: Partial<ContractCallOption>
  }
  restoreTokenIdsByAccountId: RenewableEnergyTokenType & {
    params: any
    options?: Partial<ContractCallOption>
  }
}

export type FuncReturnType = {
  version: string
  getTokenList: EventReturnType['RenewableEnergyToken']['GetTokenList']
  getToken: EventReturnType['RenewableEnergyToken']['GetToken']
  getTokenCount: EventReturnType['RenewableEnergyToken']['GetTokenCount']
  checkTransaction: EventReturnType['RenewableEnergyToken']['CheckTransaction']
  mint: ContractTransactionResponse
  transfer: ContractTransactionResponse
  backupRenewableEnergyTokens: EventReturnType['RenewableEnergyToken']['BackupRenewableEnergyTokens']
  restoreRenewableEnergyTokens: ContractTransactionResponse
  backupTokenIdsByAccountIds: EventReturnType['RenewableEnergyToken']['BackupTokenIdsByAccountIds']
  restoreTokenIdsByAccountId: ContractTransactionResponse
}

export type RenewableEnergyTokenFunctionType = {
  [K in keyof FuncParamsType]: (args: FuncParamsType[K]) => Promise<FuncReturnType[K]>
}
