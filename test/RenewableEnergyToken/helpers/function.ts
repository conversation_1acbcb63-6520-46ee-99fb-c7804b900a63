import { BASE } from '@test/common/consts'
import { castReturnType, getDeadline } from '@test/common/utils'
import { RenewableEnergyTokenFunctionType } from './types'
import privateKey from '@/privateKey'

/**
 * renewableEnergyTokenのイベントを呼ぶ関数を持つobject
 */
export const renewableEnergyTokenFunc: RenewableEnergyTokenFunctionType = {
  version: ({ renewableEnergyToken }) => {
    return castReturnType(renewableEnergyToken.version())
  },
  getTokenList: async ({ renewableEnergyToken, validatorId, accountId, offset, limit, sortOrder }) => {
    return castReturnType(renewableEnergyToken.getTokenList(validatorId, accountId, offset, limit, sortOrder))
  },
  getToken: ({ renewableEnergyToken, options }) => {
    return castReturnType(renewableEnergyToken.getToken(...options))
  },
  getTokenCount: ({ renewableEnergyToken, options }) => {
    return castReturnType(renewableEnergyToken.getTokenCount(...options))
  },
  checkTransaction: async (args) => {
    return castReturnType(
      args.renewableEnergyToken.checkTransaction(
        args.sendAccountId,
        args.fromAccountId,
        args.toAccountId,
        args.miscValue1,
        args.miscValue2,
      ),
    )
  },
  mint: async (args) => {
    return castReturnType(
      args.renewableEnergyToken.mint(
        args.tokenId,
        args.metadataId,
        args.metadataHash,
        args.mintAccountId,
        args.ownerAccountId,
        args.isLocked,
        BASE.TRACE_ID,
      ),
    )
  },
  transfer: async ({ renewableEnergyToken, fromAccountId, toAccountId, tokenId }) => {
    return castReturnType(renewableEnergyToken.transfer(fromAccountId, toAccountId, tokenId, BASE.TRACE_ID))
  },
  backupRenewableEnergyTokens: async ({ renewableEnergyToken, offset, limit, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.GET_RETOKEN_ALL } = options
    const _deadline = await getDeadline(deadline)
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return castReturnType(renewableEnergyToken.backupRenewableEnergyTokens(offset, limit, _deadline, _sig[0]))
  },
  restoreRenewableEnergyTokens: async ({ renewableEnergyToken, params, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.SET_RETOKEN_ALL } = options
    const _deadline = await getDeadline(deadline)
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return castReturnType(renewableEnergyToken.restoreRenewableEnergyTokens(params, _deadline, _sig[0]))
  },
  backupTokenIdsByAccountIds: async ({ renewableEnergyToken, offset, limit, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.GET_RETOKEN_ALL } = options
    const _deadline = await getDeadline(deadline)
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return castReturnType(renewableEnergyToken.backupTokenIdsByAccountIds(offset, limit, _deadline, _sig[0]))
  },
  restoreTokenIdsByAccountId: async ({ renewableEnergyToken, params, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.SET_RETOKEN_ALL } = options
    const _deadline = await getDeadline(deadline)
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return castReturnType(renewableEnergyToken.restoreTokenIdsByAccountId(params, _deadline, _sig[0]))
  },
}
