import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  IssuerInstance,
  ProviderInstance,
  RenewableEnergyTokenInstance,
  StructType,
  TokenStatus,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { renewableEnergyTokenFunc } from '@test/RenewableEnergyToken/helpers/function'
import { RenewableEnergyTokenContractType } from '@test/RenewableEnergyToken/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'
// Chai global vars
declare let assert: Chai.Assert

describe('getToken()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let renewableEnergyToken: RenewableEnergyTokenInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, renewableEnergyToken } =
      await contractFixture<RenewableEnergyTokenContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('TokenがMintされている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts })
        await renewableEnergyTokenFunc.mint({
          renewableEnergyToken: renewableEnergyToken,
          tokenId: BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32,
          metadataId: BASE.REToken.TOKENS.TOKEN1.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN1.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          isLocked: false,
        })
      })

      it('Tokenが返されること /* DCPF-21262', async () => {
        const result = await renewableEnergyTokenFunc.getToken({
          renewableEnergyToken: renewableEnergyToken,
          options: [BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32],
        })
        const expected: StructType['RenewableEnergyTokenDataType'] = {
          tokenStatus: TokenStatus.Active,
          metadataId: BASE.REToken.TOKENS.TOKEN1.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN1.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          previousAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          isLocked: false,
        }
        utils.assertEqualForEachField(result.renewableEnergyTokenData, expected)
        assert.equal(result.err, '', 'error')
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('TokenがMintされている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts })
        await renewableEnergyTokenFunc.mint({
          renewableEnergyToken: renewableEnergyToken,
          tokenId: BASE.TOKEN.TOKEN1.ID,
          metadataId: BASE.REToken.TOKENS.TOKEN1.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN1.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          isLocked: false,
        })
      })
      it('存在しないTokenIdを指定した場合、例外が発生すること', async () => {
        const result = await renewableEnergyTokenFunc.getToken({
          renewableEnergyToken: renewableEnergyToken,
          options: [utils.toHex(100)],
        })
        utils.assertEqualForEachField(result.renewableEnergyTokenData, {})
        assert.equal(result.err, ERR.RETOKEN.RETOKEN_NOT_EXIST, 'error')
      })
      it('無効なTokenIdを指定した場合、例外が発生すること', async () => {
        const result = await renewableEnergyTokenFunc.getToken({
          renewableEnergyToken: renewableEnergyToken,
          options: [utils.toHex(0)],
        })
        utils.assertEqualForEachField(result.renewableEnergyTokenData, {})
        assert.equal(result.err, ERR.RETOKEN.RETOKEN_INVALID_VAL, 'error')
      })
    })
  })
})
