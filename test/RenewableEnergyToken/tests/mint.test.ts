import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, RenewableEnergyTokenInstance, ValidatorInstance } from '@test/common/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { renewableEnergyTokenFunc } from '@test/RenewableEnergyToken/helpers/function'
import { RenewableEnergyTokenContractType } from '@test/RenewableEnergyToken/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { before } from 'mocha'

describe('mint()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let renewableEnergyToken: RenewableEnergyTokenInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, renewableEnergyToken } =
      await contractFixture<RenewableEnergyTokenContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts })
        for (const accountId of [BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT1.ID]) {
          await validatorFuncs.addAccount({ validator, accounts, options: { accountId } })
        }
      })

      it('NFTが新規でMintされること /* DCPF-21262', async () => {
        const tx = await renewableEnergyTokenFunc.mint({
          renewableEnergyToken: renewableEnergyToken,
          tokenId: BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32,
          metadataId: BASE.REToken.TOKENS.TOKEN1.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN1.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          isLocked: false,
        })
        // mintのeventが発行されること
        const expectParams = {
          tokenId: BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32,
          metadataId: BASE.REToken.TOKENS.TOKEN1.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN1.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          isLocked: false,
        }
        await expect(tx)
          .to.emit(renewableEnergyToken, 'MintRNToken')
          .withArgs(...Object.values(expectParams), BASE.TRACE_ID)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('TokenがMintされている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts })
        await renewableEnergyTokenFunc.mint({
          renewableEnergyToken: renewableEnergyToken,
          tokenId: BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32,
          metadataId: BASE.REToken.TOKENS.TOKEN1.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN1.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          isLocked: false,
        })
      })

      it('登録済みtokenIdを指定した場合にエラーがスローされること /* DCPF-21262', async () => {
        const result = renewableEnergyTokenFunc.mint({
          renewableEnergyToken: renewableEnergyToken,
          tokenId: BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32,
          metadataId: BASE.REToken.TOKENS.TOKEN1.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN1.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          isLocked: false,
        })
        await expect(result).to.be.revertedWith(ERR.RETOKEN.RETOKEN_ID_EXIST)
      })
    })
  })
})
