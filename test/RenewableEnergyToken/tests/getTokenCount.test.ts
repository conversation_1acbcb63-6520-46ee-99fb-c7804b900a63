import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, RenewableEnergyTokenInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { renewableEnergyTokenFunc } from '@test/RenewableEnergyToken/helpers/function'
import { RenewableEnergyTokenContractType } from '@test/RenewableEnergyToken/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'

describe('getTokenCount()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let renewableEnergyToken: RenewableEnergyTokenInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, renewableEnergyToken } =
      await contractFixture<RenewableEnergyTokenContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })
    describe('初期状態', () => {
      it('空の配列が返されること', async () => {
        const count = await renewableEnergyToken.getTokenCount()
        const result = { count: count.toString() }
        const expected = { count: '0' }
        utils.assertEqualForEachField(result, expected)
      })
    })
    describe('TokenがMintされている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts })
        await renewableEnergyTokenFunc.mint({
          renewableEnergyToken: renewableEnergyToken,
          tokenId: BASE.TOKEN.TOKEN1.ID,
          metadataId: BASE.REToken.TOKENS.TOKEN1.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN1.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          isLocked: false,
        })
      })
      it('1件の配列が返されること', async () => {
        const count = await renewableEnergyToken.getTokenCount()
        const result = { count: count.toString() }
        const expected = { count: '1' }
        utils.assertEqualForEachField(result, expected)
      })
    })
  })
})
