import '@nomicfoundation/hardhat-chai-matchers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { RenewableEnergyTokenInstance, TokenStatus } from '@test/common/types'
import * as utils from '@test/common/utils'
import { toBytes32, toHex } from '@test/common/utils'
import { renewableEnergyTokenFunc } from '@test/RenewableEnergyToken/helpers/function'
import { RenewableEnergyTokenContractType } from '@test/RenewableEnergyToken/helpers/types'
import chai, { expect } from 'chai'
import { before } from 'mocha'
import { PromiseType } from 'utility-types'
// Chai global vars
declare let assert: Chai.Assert

describe('restoreRenewableEnergyTokens()', () => {
  let renewableEnergyToken: RenewableEnergyTokenInstance

  const createParams = (num: number) =>
    [...Array(num).keys()].map((index) => {
      return {
        tokenId: toHex(index + 1),
        renewableEnergyTokenData: {
          tokenStatus: TokenStatus.Empty,
          metadataId: toBytes32(`12${index}`),
          metadataHash: toBytes32(`0x00${index}`),
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          previousAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          isLocked: false,
        },
      }
    })

  const setupFixture = async () => {
    ;({ renewableEnergyToken } = await contractFixture<RenewableEnergyTokenContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      const tokenPram = createParams(20)
      const assertList = (
        result: PromiseType<ReturnType<typeof renewableEnergyTokenFunc.backupRenewableEnergyTokens>>,
        expected: typeof tokenPram,
      ) => {
        assert.strictEqual(result.renewableEnergyTokenAll.length, expected.length, 'account count')
        expected.forEach((v, i) => {
          utils.assertEqualForEachField(result.renewableEnergyTokenAll[i], {
            tokenId: v.tokenId,
            renewableEnergyTokenData: v.renewableEnergyTokenData,
          })
        })
      }

      it('全てのRenewableEnergyToken(20件)が登録できること', async () => {
        await renewableEnergyTokenFunc.restoreRenewableEnergyTokens({
          renewableEnergyToken: renewableEnergyToken,
          params: tokenPram,
        })
        const result = await renewableEnergyTokenFunc.backupRenewableEnergyTokens({
          renewableEnergyToken: renewableEnergyToken,
          offset: 0,
          limit: 1000,
        })
        chai.assert.equal(result.renewableEnergyTokenAll.length, tokenPram.length)
        await expect(
          result.renewableEnergyTokenAll.map((v) => {
            return {
              tokenId: v.tokenId,
              renewableEnergyTokenData: {
                tokenStatus: v.renewableEnergyTokenData.tokenStatus,
                metadataId: v.renewableEnergyTokenData.metadataId,
                metadataHash: v.renewableEnergyTokenData.metadataHash,
                mintAccountId: v.renewableEnergyTokenData.mintAccountId,
                ownerAccountId: v.renewableEnergyTokenData.ownerAccountId,
                previousAccountId: v.renewableEnergyTokenData.previousAccountId,
                isLocked: v.renewableEnergyTokenData.isLocked,
              },
            }
          }),
        ).to.deep.equal(tokenPram)
      })
    })
  })

  describe('準正常系', () => {
    const tokenPram = createParams(20)

    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('Admin権限がない場合、エラーがスローされること', async () => {
        const result = renewableEnergyTokenFunc.restoreRenewableEnergyTokens({
          renewableEnergyToken: renewableEnergyToken,
          params: tokenPram,
          options: {
            eoaKey: BASE.EOA.ISSUER1,
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_ROLE)
      })

      it('署名無効の場合、エラーがスローされること', async () => {
        const result = renewableEnergyTokenFunc.restoreRenewableEnergyTokens({
          renewableEnergyToken: renewableEnergyToken,
          params: tokenPram,
          options: {
            sig: ['0x1234', ''],
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const now = await utils.getExceededDeadline()
        const result = renewableEnergyTokenFunc.restoreRenewableEnergyTokens({
          renewableEnergyToken: renewableEnergyToken,
          params: tokenPram,
          options: { deadline: now },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('異常な値が入力された時にfails', async () => {
        const result = renewableEnergyTokenFunc.restoreRenewableEnergyTokens({
          renewableEnergyToken: renewableEnergyToken,
          params: [
            {
              tokenId: '123',
              renewableEnergyTokenData: {
                tokenStatus: TokenStatus.Active,
                metadataId: '124',
                metadataHash: '111',
                mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
                ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
                previousAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
                isLocked: 'ok',
              },
            },
          ],
        })
        await expect(result).to.be.throw
      })
    })
  })
})
