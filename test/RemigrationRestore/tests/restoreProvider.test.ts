import { BASE, ERR } from '@test/common/consts'
import { GetProviderAllOption, ProviderInstance, RemigrationRestoreInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { providerFuncs } from '@test/Provider/helpers/function'
import { remigrationFuncs } from '@test/RemigrationRestore/helpers/function'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { RemigrationRestoreContractType } from '@test/RemigrationRestore/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('restoreProvider()', () => {
  let accounts: SignerWithAddress[]
  let provider: ProviderInstance
  let remigrationRestore: RemigrationRestoreInstance

  const setupFixture = async () => {
    ;({ accounts, provider, remigrationRestore } = await contractFixture<RemigrationRestoreContractType>())
  }

  describe('正常系', () => {
    let params: GetProviderAllOption[]

    before(async () => {
      await setupFixture()
      params = [
        {
          providerId: BASE.PROV.PROV0.ID,
          providerData: {
            role: '0x8edf85e2d94cd8e4ad77c5b0aa98c9c7200b6f1bc68c11b67db960b27cb0f5d4',
            name: BASE.PROV.PROV0.NAME,
            zoneId: BASE.ZONE_ID.ID0,
            enabled: true,
          },
          providerEoa: await accounts[BASE.EOA.PROV1].getAddress(),
          zoneData: [
            {
              zoneId: BASE.ZONE_ID.ID0,
              zoneName: BASE.ZONE_NAME.NAME0,
              availableIssuerIds: [BASE.ISSUER.ISSUER0.ID, BASE.ISSUER.ISSUER1.ID, BASE.ISSUER.ISSUER2.ID],
            },
          ],
        },
      ]
    })

    describe('providerが登録されていない状態', () => {
      it('すべてのproviderが登録できること', async () => {
        await remigrationFuncs.restoreProviders({
          remigration: remigrationRestore,
          providers: params,
          accounts,
        })

        const getProvider = await providerFuncs.getProvider({ provider })
        utils.assertEqualForEachField(getProvider, {
          providerId: params[0].providerId,
          zoneId: params[0].providerData.zoneId,
          zoneName: params[0].zoneData[0].zoneName,
          err: '',
        })
      })
    })
  })

  describe('準正常系', () => {
    let params: GetProviderAllOption[]

    before(async () => {
      await setupFixture()
      params = [
        {
          providerId: BASE.PROV.PROV0.ID,
          providerData: {
            role: '0x8edf85e2d94cd8e4ad77c5b0aa98c9c7200b6f1bc68c11b67db960b27cb0f5d4',
            name: BASE.PROV.PROV0.NAME,
            zoneId: BASE.ZONE_ID.ID0,
            enabled: true,
          },
          providerEoa: await accounts[BASE.EOA.PROV1].getAddress(),
          zoneData: [
            {
              zoneId: BASE.ZONE_ID.ID0,
              zoneName: BASE.ZONE_NAME.NAME0,
              availableIssuerIds: [BASE.ISSUER.ISSUER0.ID, BASE.ISSUER.ISSUER1.ID, BASE.ISSUER.ISSUER2.ID],
            },
          ],
        },
      ]
    })

    describe('providerが登録されていない状態', () => {
      it('署名無効の場合、エラーがスローされること', async () => {
        const result = remigrationFuncs.restoreProviders({
          remigration: remigrationRestore,
          providers: params,
          accounts,
          options: { sig: ['0x1234', ''] },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const exceededDeadline = await utils.getExceededDeadline()
        const result = remigrationFuncs.restoreProviders({
          remigration: remigrationRestore,
          providers: params,
          accounts,
          options: { deadline: exceededDeadline },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('異常な値が入力された時にfails', async () => {
        const result = remigrationFuncs.restoreProviders({
          remigration: remigrationRestore,
          providers: { providerId: '123' },
          accounts,
        })
        await expect(result).to.be.throw
      })
    })
  })
})
