import { BASE } from '@test/common/consts'
import { RemigrationRestoreInstance } from '@test/common/types'
import { remigrationFuncs } from '@test/RemigrationRestore/helpers/function'
import '@nomicfoundation/hardhat-chai-matchers'
import { contractFixture } from '@test/common/contractFixture'
import { RemigrationRestoreContractType } from '@test/RemigrationRestore/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('version()', () => {
  let remigrationRestore: RemigrationRestoreInstance

  const setupFixture = async () => {
    ;({ remigrationRestore } = await contractFixture<RemigrationRestoreContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    it('versionが取得できること', async () => {
      assert.equal(await remigrationFuncs.version({ remigration: remigrationRestore }), BASE.APP.VERSION, 'version')
    })
  })
})
