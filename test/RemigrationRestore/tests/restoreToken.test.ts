import { BASE, ERR } from '@test/common/consts'
import { RemigrationBackupInstance, RemigrationRestoreInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { remigrationFuncs } from '@test/RemigrationRestore/helpers/function'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { RemigrationRestoreContractType } from '@test/RemigrationRestore/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('restoreToken()', () => {
  let accounts: SignerWithAddress[]
  let remigrationBackup: RemigrationBackupInstance
  let remigrationRestore: RemigrationRestoreInstance

  const setupFixture = async () => {
    ;({ accounts, remigrationBackup, remigrationRestore } = await contractFixture<RemigrationRestoreContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      const tokenPram = {
        tokenId: BASE.TOKEN.TOKEN1.ID,
        name: BASE.TOKEN.TOKEN1.NAME,
        symbol: BASE.TOKEN.TOKEN1.SYMBOL,
        totalSupply: '1000',
        enabled: true,
      }

      it('tokenが登録できること', async () => {
        await remigrationFuncs.restoreToken({ remigration: remigrationRestore, token: tokenPram, accounts })
        const result = await remigrationFuncs.backupToken({ remigration: remigrationBackup })
        await expect(result.token).to.deep.equal(Object.values(tokenPram))
        await expect(result.err).to.equal('')
      })
    })
  })

  describe('準正常系', () => {
    const tokenPram = {
      tokenId: BASE.TOKEN.TOKEN1.ID,
      name: BASE.TOKEN.TOKEN1.NAME,
      symbol: BASE.TOKEN.TOKEN1.SYMBOL,
      totalSupply: '1000',
      enabled: true,
    }

    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('Admin権限がない場合、エラーがスローされること', async () => {
        const result = remigrationFuncs.restoreToken({
          remigration: remigrationRestore,
          token: tokenPram,
          accounts,
          options: { eoaKey: BASE.EOA.ISSUER1 },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_ROLE)
      })

      it('署名無効の場合、エラーがスローされること', async () => {
        const result = remigrationFuncs.restoreToken({
          remigration: remigrationRestore,
          token: tokenPram,
          accounts,
          options: { sig: ['0x1234', ''] },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const now = await utils.getExceededDeadline()
        const result = remigrationFuncs.restoreToken({
          remigration: remigrationRestore,
          token: tokenPram,
          accounts,
          options: { deadline: now },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('異常な値が入力された時にfails', async () => {
        const tokenInvalid = [
          {
            validatorId: '123',
            name: 'name',
            issuerId: '123',
            role: '456',
            validatorAccountId: '123',
            enabled: true,
            validatorIdExistence: true,
            issuerIdLinkedFlag: true,
            issuerEoa: '',
            validAccountExistence: [],
          },
        ]
        const result = remigrationFuncs.restoreToken({ remigration: remigrationRestore, token: tokenInvalid, accounts })
        await expect(result).to.be.throw
      })
    })
  })
})
