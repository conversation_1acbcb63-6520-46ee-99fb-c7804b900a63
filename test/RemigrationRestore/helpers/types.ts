/* eslint-disable @typescript-eslint/no-explicit-any */
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccountInstance,
  BusinessZoneAccountInstance,
  ContractCallOption,
  ContractManagerInstance,
  EventReturnType,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  RemigrationBackupInstance,
  RemigrationRestoreInstance,
  RestoreAccountsOption,
  RestoreBusinessZoneAccountsOption,
  RestoreFinAccountsOption,
  RestoreIssuersOption,
  RestoreProviderOption,
  RestoreTokenOption,
  RestoreValidatorsOption,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { ContractTransactionResponse } from 'ethers'

export type RemigrationRestoreContractType = {
  accounts: SignerWithAddress[]
  provider: ProviderInstance
  issuer: IssuerInstance
  validator: ValidatorInstance
  account: AccountInstance
  token: TokenInstance
  ibcToken: IBCTokenInstance
  businessZoneAccount: BusinessZoneAccountInstance
  remigrationBackup: RemigrationBackupInstance
  remigrationRestore: RemigrationRestoreInstance
  contractManager: ContractManagerInstance
}

type RemigrationRestoreType = { remigration: RemigrationRestoreInstance }

type RemigrationBackupType = { remigration: RemigrationBackupInstance }

type BaseBackupType = RemigrationBackupType & {
  offset: number
  limit: number
}

type BaseRestoreType = RemigrationRestoreType & {
  accounts: SignerWithAddress[]
}

export type FuncParamsType = {
  version: RemigrationRestoreType
  backupValidators: BaseBackupType & {
    options?: Partial<RestoreValidatorsOption & ContractCallOption>
  }
  restoreValidators: BaseRestoreType & {
    validators: any
    options?: Partial<RestoreValidatorsOption & ContractCallOption>
  }
  backupProvider: {
    remigration: RemigrationBackupInstance
    options?: Partial<RestoreProviderOption & ContractCallOption>
  }
  backupAccounts: BaseBackupType & {
    options?: Partial<RestoreProviderOption & ContractCallOption>
  }
  backupFinancialZoneAccounts: BaseBackupType & {
    options?: Partial<RestoreFinAccountsOption & ContractCallOption>
  }
  backupIssuers: BaseBackupType & {
    options?: Partial<RestoreIssuersOption & ContractCallOption>
  }
  restoreProviders: BaseRestoreType & {
    providers: any
    options?: Partial<RestoreProviderOption & ContractCallOption>
  }
  restoreAccounts: BaseRestoreType & {
    accs: any
    options?: Partial<RestoreAccountsOption & ContractCallOption>
  }
  restoreFinancialZoneAccounts: BaseRestoreType & {
    finAccounts: any
    options?: Partial<RestoreFinAccountsOption & ContractCallOption>
  }
  restoreIssuers: BaseRestoreType & {
    issuers: any
    options?: Partial<RestoreIssuersOption & ContractCallOption>
  }
  backupToken: RemigrationBackupType & {
    options?: Partial<RestoreTokenOption & ContractCallOption>
  }
  restoreToken: BaseRestoreType & {
    token: any
    options?: Partial<RestoreIssuersOption & ContractCallOption>
  }
  backupBusinessZoneAccounts: BaseBackupType & {
    options?: Partial<RestoreBusinessZoneAccountsOption & ContractCallOption>
  }
  restoreBusinessZoneAccounts: BaseRestoreType & {
    bizAccounts: any
    options?: Partial<RestoreBusinessZoneAccountsOption & ContractCallOption>
  }
}

type FuncReturnType = {
  version: string
  backupValidators: EventReturnType['Remigration']['BackupValidators']
  restoreValidators: ContractTransactionResponse
  backupProvider: EventReturnType['Remigration']['BackupProvider']
  backupAccounts: EventReturnType['Remigration']['BackupAccounts']
  backupFinancialZoneAccounts: EventReturnType['Remigration']['BackupFinAccounts']
  backupIssuers: EventReturnType['Remigration']['BackupIssuers']
  restoreProviders: ContractTransactionResponse
  restoreAccounts: ContractTransactionResponse
  restoreFinancialZoneAccounts: ContractTransactionResponse
  restoreIssuers: ContractTransactionResponse
  backupToken: EventReturnType['Remigration']['BackupToken']
  restoreToken: ContractTransactionResponse
  backupBusinessZoneAccounts: EventReturnType['Remigration']['BackupBusinessZoneAccounts']
  restoreBusinessZoneAccounts: ContractTransactionResponse
}

export type RemigrationRestoreFunctionType = {
  [K in keyof FuncParamsType]: (args: FuncParamsType[K]) => Promise<FuncReturnType[K]>
}
