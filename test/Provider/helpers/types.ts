import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AddProviderOption,
  AddProviderRoleOption,
  AddTokenOption,
  CheckRoleOption,
  ContractCallOption,
  IssuerInstance,
  EventReturnType,
  ModProviderOption,
  ModTokenOption,
  ModZoneOption,
  ProviderInstance,
  SetProviderAllOption,
  TokenInstance,
  ValidatorInstance,
  GetProviderAllOption,
} from '@test/common/types'
import { ContractTransactionResponse } from 'ethers'
export type ProviderContractType = {
  accounts: SignerWithAddress[]
  provider: ProviderInstance
  validator: ValidatorInstance
  token: TokenInstance
  issuer: IssuerInstance
}

type ProviderType = { provider: ProviderInstance }

type BaseAccountsType = {
  provider: ProviderInstance
  accounts: SignerWithAddress[]
}

type FuncParamsType = {
  version: ProviderType
  getProvider: ProviderType
  getZone: ProviderType
  getZoneName: ProviderType & {
    options: Parameters<ProviderInstance['getZoneName']>
  }
  getProviderCount: ProviderType
  getToken: ProviderType & {
    options: Parameters<ProviderInstance['getToken']>
  }
  getTokenId: ProviderType
  hasToken: ProviderType & {
    options: Parameters<ProviderInstance['hasToken']>
  }
  hasProvider: ProviderType & {
    options: Parameters<ProviderInstance['hasProvider']>
  }
  getAvailableIssuerIds: ProviderType & {
    options: Parameters<ProviderInstance['getAvailableIssuerIds']>
  }
  checkAvailableIssuerIds: ProviderType & {
    options: Parameters<ProviderInstance['checkAvailableIssuerIds']>
  }
  addProvider: BaseAccountsType & {
    options?: Partial<AddProviderOption & ContractCallOption>
  }
  addBizZone: BaseAccountsType & {
    options?: Partial<AddProviderOption & ContractCallOption>
  }
  addProviderRole: BaseAccountsType & {
    options?: Partial<AddProviderRoleOption & ContractCallOption>
  }
  modZone: BaseAccountsType & {
    options?: Partial<ModZoneOption & ContractCallOption>
  }
  addToken: BaseAccountsType & {
    options?: Partial<AddTokenOption & ContractCallOption>
  }
  modToken: BaseAccountsType & {
    options?: Partial<ModTokenOption & ContractCallOption>
  }
  modProvider: BaseAccountsType & {
    providerName: string
    options?: Partial<ModProviderOption & ContractCallOption>
  }
  checkRole: ProviderType & {
    options?: Partial<CheckRoleOption & ContractCallOption>
  }
  getProviderAll: ProviderType & { providerId: string }
  setProviderAll: {
    provider: ProviderInstance
    prams: GetProviderAllOption
    options?: Partial<SetProviderAllOption & ContractCallOption>
  }
}

type FuncReturnType = {
  version: string
  getProvider: EventReturnType['Provider']['GetProvider']
  getZone: EventReturnType['Provider']['GetZone']
  getZoneName: EventReturnType['Provider']['GetZoneName']
  getProviderCount: EventReturnType['Provider']['GetProviderCount']
  getToken: EventReturnType['Provider']['GetToken']
  getTokenId: EventReturnType['Provider']['GetTokenId']
  hasToken: EventReturnType['Provider']['HasToken']
  hasProvider: EventReturnType['Provider']['HasProvider']
  getAvailableIssuerIds: EventReturnType['Provider']['GetAvailableIssuerIds']
  checkAvailableIssuerIds: EventReturnType['Provider']['CheckAvailableIssuerIds']
  addProvider: ContractTransactionResponse
  addBizZone: ContractTransactionResponse
  addProviderRole: ContractTransactionResponse
  modZone: ContractTransactionResponse
  addToken: ContractTransactionResponse
  modToken: ContractTransactionResponse
  modProvider: ContractTransactionResponse
  checkRole: EventReturnType['Provider']['CheckRole']
  getProviderAll: EventReturnType['Provider']['GetProviderAll']
  setProviderAll: ContractTransactionResponse
}

export type ProviderFunctionType = {
  [K in keyof FuncParamsType]: (args: FuncParamsType[K]) => Promise<FuncReturnType[K]>
}
