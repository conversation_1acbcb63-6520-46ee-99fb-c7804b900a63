import '@nomicfoundation/hardhat-chai-matchers'

describe('Provider', () => {
  require('./tests/version.test')
  require('./tests/initialize.test')
  require('./tests/getProvider.test')
  require('./tests/getZone.test')
  require('./tests/getZoneName.test')
  require('./tests/getProviderCount.test')
  require('./tests/getAvailableIssuerIds.test')
  require('./tests/addProvider.test')
  require('./tests/addBizZone.test')
  require('./tests/addProviderRole.test')
  require('./tests/modZone.test')
  require('./tests/addToken.test')
  require('./tests/modToken.test')
  require('./tests/hasToken.test')
  require('./tests/getTokenId.test')
  require('./tests/modProvider.test')
  require('./tests/getToken.test')
  require('./tests/hasProvider.test')
  require('./tests/checkRole.test')
  require('./tests/getProviderAll.test')
  require('./tests/setProviderAll.test')
})
