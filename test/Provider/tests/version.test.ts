import '@nomicfoundation/hardhat-chai-matchers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ProviderInstance } from '@test/common/types'
import { providerFuncs } from '@test/Provider/helpers/function'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('version()', () => {
  let provider: ProviderInstance

  const setupFixture = async () => {
    ;({ provider } = await contractFixture<ProviderContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    it('versionを取得できること', async () => {
      assert.strictEqual(await providerFuncs.version({ provider }), BASE.APP.VERSION, 'version')
    })
  })
})
