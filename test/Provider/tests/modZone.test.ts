import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ModZoneOption, ProviderInstance } from '@test/common/types'
import { providerFuncs } from '@test/Provider/helpers/function'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('modZone()', () => {
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider } = await contractFixture<ProviderContractType>())
  }

  const setupProvider = async () => {
    await providerFuncs.addProvider({ provider, accounts })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('mod zone normally', async () => {
      before(async () => {
        await setupProvider()
      })

      it('should mod zone normally', async () => {
        const params: ModZoneOption = {
          providerId: BASE.PROV.PROV0.ID,
          zoneName: BASE.ZONE_NAME.NAME1,
          traceId: BASE.TRACE_ID,
        }

        const tx = await providerFuncs.modZone({
          provider,
          accounts,
          options: {
            providerId: params.providerId,
            zoneName: params.zoneName,
          },
        })

        await expect(tx).to.emit(provider, 'ModZone').withArgs(params.providerId, params.zoneName, params.traceId)
      })
    })
  })

  describe('準正常系', () => {
    describe('mod zone with invalid providerId', async () => {
      before(async () => {
        await setupFixture()
      })

      it('should revert when providerId is invalid', async () => {
        const result = providerFuncs.modZone({
          provider,
          accounts,
          options: {
            providerId: BASE.PROV.EMPTY.ID,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })
        await expect(result).to.be.revertedWith(ERR.PROV.PROV_INVALID_VAL)
      })

      it('should revert when providerId is not registered', async () => {
        const result = providerFuncs.modZone({
          provider,
          accounts,
          options: {
            providerId: BASE.PROV.PROV1.ID,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })
        await expect(result).to.be.revertedWith(ERR.PROV.PROV_ID_NOT_EXIST)
      })
    })
  })
})
