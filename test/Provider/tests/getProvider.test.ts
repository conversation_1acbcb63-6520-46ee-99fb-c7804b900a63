import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ProviderInstance } from '@test/common/types'
import { assertEqualForEachField } from '@test/common/utils'
import { providerFuncs } from '@test/Provider/helpers/function'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('getProvider()', () => {
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider } = await contractFixture<ProviderContractType>())
  }

  const setupProviderWithRoleAndToken = async () => {
    await providerFuncs.addProvider({ provider, accounts })
    await providerFuncs.addProviderRole({ provider, accounts })
    await providerFuncs.addToken({ provider, accounts })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('providerが登録されていない状態', () => {
      it('エラーが返されること', async () => {
        const result = await providerFuncs.getProvider({ provider })

        assert.equal(result.err, ERR.PROV.PROV_NOT_EXIST, 'err')
      })
    })

    describe('provider, role, tokenが登録されている状態', () => {
      before(async () => {
        await setupProviderWithRoleAndToken()
      })

      it('provider情報が取得できること', async () => {
        const result = await providerFuncs.getProvider({ provider })

        assertEqualForEachField(result, {
          providerId: BASE.PROV.PROV0.ID,
          zoneId: BASE.ZONE_ID.ID0,
          zoneName: BASE.ZONE_NAME.NAME0,
          err: '',
        })
      })
    })
  })
})
