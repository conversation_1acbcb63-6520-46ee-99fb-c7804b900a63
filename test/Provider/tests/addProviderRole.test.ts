import '@nomicfoundation/hardhat-chai-matchers'
import { anyValue } from '@nomicfoundation/hardhat-chai-matchers/withArgs'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AddProviderRoleOption, ProviderInstance } from '@test/common/types'
import { assertEqualForEachField, getExceededDeadline } from '@test/common/utils'
import { providerFuncs } from '@test/Provider/helpers/function'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('addProviderRole()', () => {
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider } = await contractFixture<ProviderContractType>())
  }

  const setupProvider = async () => {
    await providerFuncs.addProvider({ provider, accounts })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('providerが登録されている状態', () => {
      before(async () => {
        await setupProvider()
      })

      it('provider roleが登録できること', async () => {
        const params: AddProviderRoleOption = {
          providerId: BASE.PROV.PROV0.ID,
          providerEoa: await accounts[BASE.EOA.PROV1].getAddress(),
        }

        const tx = await providerFuncs.addProviderRole({ provider, accounts, options: params })

        await expect(tx).to.emit(provider, 'AddProviderRole').withArgs(params.providerId, params.providerEoa, anyValue)

        const result = await providerFuncs.checkRole({ provider, options: { providerId: params.providerId } })
        assertEqualForEachField(result, { has: true, err: '' })
      })
    })

    describe('roleが登録されている状態', () => {
      it('同じproviderに対して複数providerRoleが登録できること', async () => {
        const params: AddProviderRoleOption = {
          providerId: BASE.PROV.PROV0.ID,
          providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
        }

        const tx = await providerFuncs.addProviderRole({ provider, accounts, options: params })

        await expect(tx).to.emit(provider, 'AddProviderRole').withArgs(params.providerId, params.providerEoa, anyValue)

        const result = await providerFuncs.checkRole({
          provider,
          options: {
            eoaKey: BASE.EOA.PROV2,
            providerId: params.providerId,
          },
        })
        assertEqualForEachField(result, { has: true, err: '' })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('providerが登録されている状態', () => {
      before(async () => {
        await setupProvider()
      })
      it('Admin権限ではない署名使用の場合、エラーがスローされること', async () => {
        const result = providerFuncs.addProviderRole({
          provider,
          accounts,
          options: { eoaKey: BASE.EOA.PROV1 },
        })
        await expect(result).to.be.revertedWith(ERR.PROV.PROV_NOT_ADMIN_ROLE)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const exceededDeadline = await getExceededDeadline()

        const result = providerFuncs.addProviderRole({
          provider,
          accounts,
          options: { deadline: exceededDeadline },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('署名無効の場合、エラーがスローされること', async () => {
        const result = providerFuncs.addProviderRole({ provider, accounts, options: { sig: ['0x2345', ''] } })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('未登録providerId指定の場合、エラーがスローされること', async () => {
        const result = providerFuncs.addProviderRole({
          provider,
          accounts,
          options: { providerId: BASE.PROV.PROV1.ID },
        })
        await expect(result).to.be.revertedWith(ERR.PROV.PROV_ID_NOT_EXIST)
      })

      it('無効EOA(0x0)指定の場合、エラーがスローされること', async () => {
        const result = providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: '0x0000000000000000000000000000000000000000',
          },
        })
        await expect(result).to.be.revertedWith(ERR.PROV.PROV_INVALID_VAL)
      })
    })
  })
})
