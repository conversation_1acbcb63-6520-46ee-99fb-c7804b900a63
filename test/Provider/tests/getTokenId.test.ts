import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ProviderInstance } from '@test/common/types'
import { assertEqualForEachField, toBytes32 } from '@test/common/utils'
import { providerFuncs } from '@test/Provider/helpers/function'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { before } from 'mocha'

describe('getTokenId()', () => {
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider } = await contractFixture<ProviderContractType>())
  }

  const setupProvider = async () => {
    await providerFuncs.addProvider({ provider, accounts })
  }

  const setupRoleAndToken = async () => {
    await providerFuncs.addProviderRole({ provider, accounts })
    await providerFuncs.addToken({ provider, accounts })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('providerが登録されていない状態', () => {
      it('エラーが返されること', async () => {
        const result = await providerFuncs.getTokenId({ provider })

        assertEqualForEachField(result, {
          tokenId: toBytes32(''),
          err: ERR.TOKEN.TOKEN_NOT_EXIST,
        })
      })
    })

    describe('providerが登録されている状態', () => {
      before(async () => {
        await setupProvider()
      })

      it('tokenが登録されていない場合、エラーが返されること', async () => {
        const result = await providerFuncs.getTokenId({ provider })

        assertEqualForEachField(result, {
          tokenId: toBytes32(''),
          err: ERR.TOKEN.TOKEN_NOT_EXIST,
        })
      })
    })

    describe('role, tokenが登録されている状態', () => {
      before(async () => {
        await setupRoleAndToken()
      })

      it('tokenIdが取得できること', async () => {
        const tokenId = BASE.TOKEN.TOKEN1.ID

        const result = await providerFuncs.getTokenId({ provider })

        assertEqualForEachField(result, {
          tokenId,
          err: '',
        })
      })
    })
  })
})
