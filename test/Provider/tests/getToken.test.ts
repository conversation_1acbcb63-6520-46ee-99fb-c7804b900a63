import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ProviderInstance } from '@test/common/types'
import { assertEqualForEachField } from '@test/common/utils'
import { providerFuncs } from '@test/Provider/helpers/function'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { before } from 'mocha'

describe('getToken()', () => {
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider } = await contractFixture<ProviderContractType>())
  }

  const setupProviderWithRoleAndToken = async () => {
    await providerFuncs.addProvider({ provider, accounts })
    await providerFuncs.addProviderRole({ provider, accounts })
    await providerFuncs.addToken({ provider, accounts })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('provider, role, tokenが登録されている状態', () => {
      before(async () => {
        await setupProviderWithRoleAndToken()
      })

      it('tokenの情報が取得できること', async () => {
        const result = await providerFuncs.getToken({ provider, options: [BASE.PROV.PROV0.ID] })

        assertEqualForEachField(result, {
          tokenId: BASE.TOKEN.TOKEN1.ID,
          name: BASE.TOKEN.TOKEN1.NAME,
          symbol: BASE.TOKEN.TOKEN1.SYMBOL,
          enabled: true,
          totalSupply: 0,
          err: '',
        })
      })

      it('違うproviderIdで取得する場合、エラーが返されること', async () => {
        const result = await providerFuncs.getToken({ provider, options: [BASE.PROV.PROV1.ID] })

        assertEqualForEachField(result, {
          tokenId: BASE.TOKEN.EMPTY,
          name: BASE.PROV.EMPTY.ID,
          symbol: BASE.PROV.EMPTY.ID,
          enabled: false,
          err: ERR.PROV.PROV_ID_NOT_EXIST,
        })
      })
    })
  })
})
