import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ProviderInstance } from '@test/common/types'
import { assertEqualForEachField, getExceededDeadline } from '@test/common/utils'
import { providerFuncs } from '@test/Provider/helpers/function'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { before } from 'mocha'

describe('checkRole()', () => {
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider } = await contractFixture<ProviderContractType>())
  }

  const setupProviderWithRole = async () => {
    await providerFuncs.addProvider({ provider, accounts })
    await providerFuncs.addProviderRole({ provider, accounts })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('provider, roleが登録されている状態', () => {
      before(async () => {
        await setupProviderWithRole()
      })

      it('Role付与済みの場合Trueが返されること', async () => {
        const result = await providerFuncs.checkRole({ provider })

        assertEqualForEachField(result, { has: true, err: '' })
      })

      it('Role付与済みでないの場合Falseが返されること', async () => {
        const result = await providerFuncs.checkRole({ provider, options: { eoaKey: BASE.EOA.ADMIN } })

        assertEqualForEachField(result, { has: false, err: '' })
      })

      it('空providerId指定の場合、falseが返されること', async () => {
        const result = await providerFuncs.checkRole({
          provider,
          options: { providerId: BASE.PROV.EMPTY.ID },
        })

        assertEqualForEachField(result, { has: false, err: ERR.PROV.PROV_INVALID_VAL })
      })

      it('タイムアウトが発生する場合、falseが返されること', async () => {
        const exceededDeadline = await getExceededDeadline()

        const result = await providerFuncs.checkRole({ provider, options: { deadline: exceededDeadline } })

        assertEqualForEachField(result, { has: false, err: ERR.ACTRL.ACTRL_SIG_TIMEOUT })
      })
    })
  })
})
