import '@nomicfoundation/hardhat-chai-matchers'
import { balanceSyncBridgeFuncs } from '@test/BalanceSyncBridge/helpers/function'
import { BalanceSyncBridgeContractType } from '@test/BalanceSyncBridge/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { BalanceSyncBridgeInstance, IBCHandlerInstance, IBCTokenMockInstance } from '@test/common/types'
import { expect } from 'chai'
import Web3 from 'web3'

declare let web3: Web3

describe('onRecvPacket', () => {
  let balanceSyncBridge: BalanceSyncBridgeInstance
  let ibcHandler: IBCHandlerInstance
  let ibcTokenMock: IBCTokenMockInstance

  const setupFixture = async () => {
    ;({ balanceSyncBridge, ibcHandler, ibcTokenMock } = await contractFixture<BalanceSyncBridgeContractType>())
  }

  beforeEach(async () => {
    await setupFixture()
  })

  describe('正常系', () => {
    describe('初期状態', () => {
      it('BizZoneからpacketを受け取ることができた場合、FinZone管理のBizZoneアカウント残高が更新されること', async () => {
        await balanceSyncBridgeFuncs.recvPacket({ ibcHandler, balanceSyncBridge })

        const bizZoneAccountData = await ibcTokenMock.getBalanceByZone(BASE.BRIDGE.ACCOUNT_B, BASE.ZONE.BIZ)
        await expect(bizZoneAccountData).to.be.equal(BigInt(BASE.BRIDGE.EXCHANGE_AMOUNT))
      })
    })
  })

  describe('準正常系', () => {
    describe('初期状態', () => {
      it('packet空の場合、エラーがスローされること', async () => {
        const packet = web3.eth.abi.encodeParameters([], [])

        const result = balanceSyncBridgeFuncs.recvPacket({
          ibcHandler,
          balanceSyncBridge,
          options: {
            packetData: packet,
          },
        })
        await expect(result).to.be.revertedWith(ERR.IBC.IBC_INVALID_VAL)
      })
    })
  })
})
