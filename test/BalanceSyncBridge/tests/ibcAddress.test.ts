import '@nomicfoundation/hardhat-chai-matchers'
import { BalanceSyncBridgeContractType } from '@test/BalanceSyncBridge/helpers/types'
import { contractFixture } from '@test/common/contractFixture'
import { BalanceSyncBridgeInstance, IBCHandlerInstance } from '@test/common/types'
import { assert } from 'chai'

describe('ibcAddress()', () => {
  let balanceSyncBridge: BalanceSyncBridgeInstance
  let ibcHandler: IBCHandlerInstance

  const setupFixture = async () => {
    ;({ balanceSyncBridge, ibcHandler } = await contractFixture<BalanceSyncBridgeContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('ibc<PERSON><PERSON><PERSON> return the correct address', async () => {
        const ibcAddress = await balanceSyncBridge.ibcAddress()
        assert.equal(ibc<PERSON>dd<PERSON>, await ibc<PERSON><PERSON><PERSON>.getAddress(), 'ibcAddress')
      })
    })
  })
})
