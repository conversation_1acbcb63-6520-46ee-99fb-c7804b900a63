import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccessCtrlMockInstance,
  AccountMockInstance,
  BalanceSyncBridgeInstance,
  ContractCallOption,
  IBCHandlerInstance,
  IBCTokenMockInstance,
  PacketCallOption,
  SyncTransferCallOption,
} from '@test/common/types'
import { ContractTransactionResponse } from 'ethers'

export type BalanceSyncBridgeContractType = {
  accounts: SignerWithAddress[]
  balanceSyncBridge: BalanceSyncBridgeInstance
  ibcHandler: IBCHandlerInstance
  ibcTokenMock: IBCTokenMockInstance
  accountMock: AccountMockInstance
  accessCtrlMock: AccessCtrlMockInstance
}

type BalanceSyncBridgeType = {
  balanceSyncBridge: BalanceSyncBridgeInstance
}

type PacketType = BalanceSyncBridgeType & {
  ibcHandler: IBCHandlerInstance
  options?: Partial<PacketCallOption>
}

export type FuncParamsType = {
  setAddress: BalanceSyncBridgeType & {
    ibcTokenMockAddress: string
    accountMockAddress: string
    accessCtrlMockAddress: string
    options?: Partial<ContractCallOption & { newAddress: string }>
  }
  syncTransfer: BalanceSyncBridgeType & {
    options?: Partial<SyncTransferCallOption>
  }
  recvPacket: PacketType
  acknowledgementPacket: PacketType
  timeoutPacket: PacketType
  recoverPacket: BalanceSyncBridgeType & {
    options?: Partial<ContractCallOption & PacketCallOption>
  }
}

export type FunctionType = {
  [K in keyof FuncParamsType]: (args: FuncParamsType[K]) => Promise<ContractTransactionResponse>
}
