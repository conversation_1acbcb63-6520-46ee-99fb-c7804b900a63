import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccessCtrlMockInstance,
  ContractCallOption,
  IBCHandlerInstance,
  IBCTokenMockInstance,
  JPYTokenTransferBridgeInstance,
  PacketCallOption,
  TransferCallOption,
} from '@test/common/types'
import { ContractTransactionResponse } from 'ethers'

export type JPYTokenTransferBridgeContractType = {
  accounts: SignerWithAddress[]
  jpyTokenTransferBridge: JPYTokenTransferBridgeInstance
  ibcHandler: IBCHandlerInstance
  ibcTokenMock: IBCTokenMockInstance
  accessCtrlMock: AccessCtrlMockInstance
}

type JPYTokenTransferBridgeType = {
  jpyTokenTransferBridge: JPYTokenTransferBridgeInstance
}

type PacketType = JPYTokenTransferBridgeType & {
  ibcHandler: IBCHandlerInstance
  options?: Partial<PacketCallOption>
}

export type FuncParamsType = {
  setAddress: JPYTokenTransferBridgeType & {
    ibcTokenMockAddress: string
    accessCtrlMockAddress: string
    options?: Partial<ContractCallOption & { newAddress: string }>
  }
  registerEscrowAccount: JPYTokenTransferBridgeType & {
    zoneId: Parameters<JPYTokenTransferBridgeInstance['registerEscrowAccount']>[0]
    dstChannelID: Parameters<JPYTokenTransferBridgeInstance['registerEscrowAccount']>[1]
    escrowAccount: Parameters<JPYTokenTransferBridgeInstance['registerEscrowAccount']>[2]
    options?: Partial<ContractCallOption>
  }
  unregisterEscrowAccount: JPYTokenTransferBridgeType & {
    zoneId: Parameters<JPYTokenTransferBridgeInstance['unregisterEscrowAccount']>[0]
    dstChannelID: Parameters<JPYTokenTransferBridgeInstance['unregisterEscrowAccount']>[1]
    options?: Partial<ContractCallOption>
  }
  transfer: JPYTokenTransferBridgeType & {
    options?: Partial<TransferCallOption>
  }
  recvPacket: PacketType
  acknowledgementPacket: PacketType
  timeoutPacket: PacketType
  recoverPacket: JPYTokenTransferBridgeType & {
    options?: Partial<ContractCallOption & PacketCallOption>
  }
}

export type JPYTokenTransferBridgeFunctionType = {
  [K in keyof FuncParamsType]: (args: FuncParamsType[K]) => Promise<ContractTransactionResponse>
}
