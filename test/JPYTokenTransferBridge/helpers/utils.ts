import { BASE } from '@test/common/consts'
import { genPacketData as utilsGenPacketData } from '@test/common/utils'
import Web3 from 'web3'

declare let web3: Web3

export const genPacketData = (packetData, fromZoneId, toZoneId, amount, transferType) =>
  packetData ??
  web3.eth.abi.encodeParameters(
    ['(bytes32, uint16, uint16, uint256, bytes32, bytes32)'],
    [
      [
        BASE.BRIDGE.ACCOUNT_A,
        fromZoneId ?? BASE.ZONE.FIN,
        toZoneId ?? BASE.ZONE.BIZ,
        amount ?? BASE.BRIDGE.AMOUNT,
        transferType ?? BASE.TOKEN.TRANSFER_TYPE.CHARGE,
        BASE.TRACE_ID,
      ],
    ],
  )

export const genPacket = (packetData, fromZoneId, toZoneId, amount, transferType) =>
  utilsGenPacketData(
    genPacketData(packetData, fromZoneId, toZoneId, amount, transferType),
    BASE.TOKEN_TRANS_BRIDGE.PORT,
    BASE.TOKEN_TRANS_BRIDGE.CHANNEL,
    BASE.TIMEOUT_HEIGHT,
  )
