import '@nomicfoundation/hardhat-chai-matchers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { JPYTokenTransferBridgeInstance } from '@test/common/types'
import { assertEqualBytes32 } from '@test/common/utils'
import { jpyTokenTransferBridgeFuncs } from '@test/JPYTokenTransferBridge/helpers/function'
import { JPYTokenTransferBridgeContractType } from '@test/JPYTokenTransferBridge/helpers/types'
import { before } from 'mocha'

describe('unregisterEscrowAccount()', () => {
  let jpyTokenTransferBridge: JPYTokenTransferBridgeInstance

  const setupFixture = async () => {
    ;({ jpyTokenTransferBridge } = await contractFixture<JPYTokenTransferBridgeContractType>())
  }

  const setupEscrowAccount = async () => {
    await jpyTokenTransferBridgeFuncs.registerEscrowAccount({
      jpyTokenTransferBridge,
      zoneId: BASE.ZONE.FIN,
      dstChannelID: BASE.ZONE.BIZ,
      escrowAccount: BASE.BRIDGE.ESCROW_ACCOUNT,
    })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('EscrowAccountが登録されている状態', () => {
      before(async () => {
        await setupEscrowAccount()
      })

      it('EscrowAccountが削除できること', async () => {
        await jpyTokenTransferBridgeFuncs.unregisterEscrowAccount({
          jpyTokenTransferBridge,
          zoneId: BASE.ZONE.FIN,
          dstChannelID: BASE.ZONE.BIZ,
        })

        const registeredEscrowAccount = await jpyTokenTransferBridge.escrowAccount(BASE.ZONE.FIN, BASE.ZONE.BIZ)
        assertEqualBytes32(registeredEscrowAccount, BASE.BRIDGE._NO_ACCOUNT)
      })
    })
  })

  describe('準正常系', () => {
    // HACK: Admin権限でない署名の場合のケースは、TokenMock.checkAdminRoleがtrueしか返さないため実施不可
  })
})
