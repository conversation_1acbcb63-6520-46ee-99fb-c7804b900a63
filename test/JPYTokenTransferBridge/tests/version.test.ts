import '@nomicfoundation/hardhat-chai-matchers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { JPYTokenTransferBridgeInstance } from '@test/common/types'
import { JPYTokenTransferBridgeContractType } from '@test/JPYTokenTransferBridge/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('version()', () => {
  let jpyTokenTransferBridge: JPYTokenTransferBridgeInstance

  const setupFixture = async () => {
    ;({ jpyTokenTransferBridge } = await contractFixture<JPYTokenTransferBridgeContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })
    describe('初期状態', () => {
      it('version情報を取得できること', async () => {
        assert.equal(await jpyTokenTransferBridge.version(), BASE.APP.VERSION, 'version')
      })
    })
  })
})
