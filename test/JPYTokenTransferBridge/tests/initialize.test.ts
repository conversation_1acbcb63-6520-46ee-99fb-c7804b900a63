import '@nomicfoundation/hardhat-chai-matchers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  AccessCtrlMockInstance,
  IBCHandlerInstance,
  IBCTokenMockInstance,
  JPYTokenTransferBridgeInstance,
} from '@test/common/types'
import { JPYTokenTransferBridgeContractType } from '@test/JPYTokenTransferBridge/helpers/types'
import { expect } from 'chai'
import hre from 'hardhat'
import { before } from 'mocha'

const JPYTokenTransferBridgeFactory = hre.ethers.getContractFactory('JPYTokenTransferBridge')

describe('initialize()', () => {
  let jpyTokenTransferBridge: JPYTokenTransferBridgeInstance
  let ibcHandler: IBCHandlerInstance
  let ibcTokenMock: IBCTokenMockInstance
  let accessCtrlMock: AccessCtrlMockInstance

  const setupFixture = async () => {
    ;({ ibc<PERSON><PERSON><PERSON>, ibcTokenMock, accessCtrlMock } = await contractFixture<JPYTokenTransferBridgeContractType>())
  }

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    beforeEach(async () => {
      jpyTokenTransferBridge = await (await JPYTokenTransferBridgeFactory).deploy()
    })

    describe('初期状態', () => {
      it('IBCHandlerのコントラクトアドレスが空の場合。エラーをスローすること', async () => {
        const result = jpyTokenTransferBridge.initialize(
          BASE.EMPTY_ADDRESS,
          await ibcTokenMock.getAddress(),
          await accessCtrlMock.getAddress(),
        )
        await expect(result).to.be.revertedWith(ERR.IBC.IBC_INVALID_VAL)
      })
      it('IBCTokenのコントラクトアドレスが空の場合。エラーをスローすること', async () => {
        const result = jpyTokenTransferBridge.initialize(
          await ibcHandler.getAddress(),
          BASE.EMPTY_ADDRESS,
          await accessCtrlMock.getAddress(),
        )
        await expect(result).to.be.revertedWith(ERR.IBC.IBC_INVALID_VAL)
      })
      it('AccessCtrlのコントラクトアドレスが空の場合。エラーをスローすること', async () => {
        const result = jpyTokenTransferBridge.initialize(
          await ibcHandler.getAddress(),
          await ibcTokenMock.getAddress(),
          BASE.EMPTY_ADDRESS,
        )
        await expect(result).to.be.revertedWith(ERR.IBC.IBC_INVALID_VAL)
      })
      it('should revert if already initialized', async () => {
        await jpyTokenTransferBridge.initialize(
          await ibcHandler.getAddress(),
          await ibcTokenMock.getAddress(),
          await accessCtrlMock.getAddress(),
        )
        const result = jpyTokenTransferBridge.initialize(
          await ibcHandler.getAddress(),
          await ibcTokenMock.getAddress(),
          await accessCtrlMock.getAddress(),
        )
        await expect(result).to.be.revertedWith(ERR.INITIALIZER.ALREADY_INIT)
      })
    })
  })
})
