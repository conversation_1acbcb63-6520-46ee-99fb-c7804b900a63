import '@nomicfoundation/hardhat-chai-matchers'
import { contractFixture } from '@test/common/contractFixture'
import { ContractManagerInstance, ValidatorInstance } from '@test/common/types'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { ContractManagerContractType } from '@test/ContractManager/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('validator()', () => {
  let contractManager: ContractManagerInstance
  let validator: ValidatorInstance

  const setupFixture = async () => {
    ;({ validator, contractManager } = await contractFixture<ContractManagerContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    it('validatorインスタンスのアドレスが取得できること', async () => {
      const result = await contractManagerFuncs.validator({ contractManager })

      assert.equal(result, await validator.getAddress())
    })
  })
})
