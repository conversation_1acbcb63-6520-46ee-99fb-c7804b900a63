import '@nomicfoundation/hardhat-chai-matchers'
import { contractFixture } from '@test/common/contractFixture'
import { ContractManagerInstance, TransferProxyInstance } from '@test/common/types'
import { ContractManagerContractType } from '@test/ContractManager/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('transferProxy()', () => {
  let contractManager: ContractManagerInstance
  let transferProxy: TransferProxyInstance

  const setupFixture = async () => {
    ;({ transferProxy, contractManager } = await contractFixture<ContractManagerContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    it('transferProxyインスタンスのアドレスが取得できること', async () => {
      const result = await contractManager.transferProxy()

      assert.equal(result, await transferProxy.getAddress())
    })
  })
})
