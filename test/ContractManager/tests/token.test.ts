import '@nomicfoundation/hardhat-chai-matchers'
import { contractFixture } from '@test/common/contractFixture'
import { ContractManagerInstance, TokenInstance } from '@test/common/types'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { ContractManagerContractType } from '@test/ContractManager/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('token()', () => {
  let contractManager: ContractManagerInstance
  let token: TokenInstance

  const setupFixture = async () => {
    ;({ token, contractManager } = await contractFixture<ContractManagerContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    it('tokenインスタンスのアドレスが取得できること', async () => {
      const result = await contractManagerFuncs.token({ contractManager })

      assert.equal(result, await token.getAddress())
    })
  })
})
