import '@nomicfoundation/hardhat-chai-matchers'
import { contractFixture } from '@test/common/contractFixture'
import { ContractManagerInstance, IssuerInstance } from '@test/common/types'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { ContractManagerContractType } from '@test/ContractManager/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('issuer()', () => {
  let contractManager: ContractManagerInstance
  let issuer: IssuerInstance

  const setupFixture = async () => {
    ;({ issuer, contractManager } = await contractFixture<ContractManagerContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    it('issuerインスタンスのアドレスが取得できること', async () => {
      const result = await contractManagerFuncs.issuer({ contractManager })

      assert.equal(result, await issuer.getAddress())
    })
  })
})
