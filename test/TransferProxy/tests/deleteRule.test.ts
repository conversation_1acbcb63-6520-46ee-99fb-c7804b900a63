import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { TransferProxyInstance } from '@test/common/types'
import { transferProxyFuncs } from '@test/TransferProxy/helpers/function'
import { TransferProxyContractType } from '@test/TransferProxy/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('deleteRule', () => {
  let accounts: SignerWithAddress[]
  let transferProxy: TransferProxyInstance

  const setupFixture = async () => {
    ;({ accounts, transferProxy } = await contractFixture<TransferProxyContractType>())
  }

  const setupFiveRules = async () => {
    for (let i = 0; i < 5; i++) {
      const rule = await accounts[i].getAddress()
      const position = i
      await transferProxyFuncs.addRule({ transferProxy: transferProxy, rule: rule, position: position })
    }
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('Ruleが5件登録されている状態', () => {
      before(async () => {
        await setupFiveRules()
      })

      it('5番目(_nextRule)のaddressを削除', async function () {
        const rule = await accounts[4].getAddress()
        const tx = await transferProxyFuncs.deleteRule({ transferProxy: transferProxy, rule: rule })
        await expect(tx).emit(transferProxy, 'DeleteRule').withArgs(rule)
        const rules = await transferProxyFuncs.findAll({ transferProxy: transferProxy })
        assert.equal(rules.length, 4, 'Rule')
        assert.equal(rules[0], await accounts[0].getAddress(), 'Rule1')
        assert.equal(rules[1], await accounts[1].getAddress(), 'Rule2')
        assert.equal(rules[2], await accounts[2].getAddress(), 'Rule3')
        assert.equal(rules[3], await accounts[3].getAddress(), 'Rule4')
      })
      it('1番目のaddressを削除', async function () {
        const rule = await accounts[0].getAddress()
        const tx = await transferProxyFuncs.deleteRule({ transferProxy: transferProxy, rule: rule })
        await expect(tx).emit(transferProxy, 'DeleteRule').withArgs(rule)
        const rules = await transferProxyFuncs.findAll({ transferProxy: transferProxy })
        assert.equal(rules.length, 3, 'Rule')
        assert.equal(rules[0], await accounts[1].getAddress(), 'Rule1')
        assert.equal(rules[1], await accounts[2].getAddress(), 'Rule2')
        assert.equal(rules[2], await accounts[3].getAddress(), 'Rule3')
      })
      it('3番目の内、2番目のルールを削除する', async function () {
        const rule = await accounts[2].getAddress()
        const tx = await transferProxyFuncs.deleteRule({ transferProxy: transferProxy, rule: rule })
        await expect(tx).emit(transferProxy, 'DeleteRule').withArgs(rule)

        const rules = await transferProxyFuncs.findAll({ transferProxy: transferProxy })
        assert.equal(rules.length, 2, 'Rule')
        assert.equal(rules[0], await accounts[1].getAddress(), 'Rule1')
        assert.equal(rules[1], await accounts[3].getAddress(), 'Rule2')
      })
      it('2番目の内、2番目のルールを削除する', async function () {
        const rule = await accounts[3].getAddress()
        const tx = await transferProxyFuncs.deleteRule({ transferProxy: transferProxy, rule: rule })
        await expect(tx).emit(transferProxy, 'DeleteRule').withArgs(rule)
        const rules = await transferProxyFuncs.findAll({ transferProxy: transferProxy })
        assert.equal(rules.length, 1, 'Rule')
        assert.equal(rules[0], await accounts[1].getAddress(), 'Rule1')
      })
      it('最後のルールを削除する', async function () {
        const rule = await accounts[1].getAddress()
        const tx = await transferProxyFuncs.deleteRule({ transferProxy: transferProxy, rule: rule })
        await expect(tx).emit(transferProxy, 'DeleteRule').withArgs(rule)
        const rules = await transferProxyFuncs.findAll({ transferProxy: transferProxy })
        assert.equal(rules.length, 0, 'Rule')
      })
    })

    describe('異常系', () => {
      before(async () => {
        await setupFixture()
      })

      describe('初期状態', () => {
        it('Ruleが未登録の場合はエラー', async function () {
          const rule = await accounts[4].getAddress()
          const result = transferProxyFuncs.deleteRule({ transferProxy: transferProxy, rule: rule })
          await expect(result).to.be.revertedWith(ERR.TRANSFERPROXY.CUSTOM_CONTRACT_NOT_EXIST)
          const rules = await transferProxyFuncs.findAll({ transferProxy: transferProxy })
          assert.equal(rules.length, 0, 'Rule')
        })
      })

      describe('Ruleが5件登録されている状態', () => {
        before(async () => {
          await setupFiveRules()
        })

        it('存在しないRuleを指定した場合はエラー', async function () {
          const rule = await accounts[6].getAddress()
          const result = transferProxyFuncs.deleteRule({ transferProxy: transferProxy, rule: rule })
          await expect(result).to.be.revertedWith(ERR.TRANSFERPROXY.CUSTOM_CONTRACT_NOT_EXIST)
          // Ruleに変化がない事を確認する
          const rules = await transferProxyFuncs.findAll({ transferProxy: transferProxy })
          assert.equal(rules.length, 5, 'Rule')
          assert.equal(rules[0], await accounts[0].getAddress(), 'Rule1')
          assert.equal(rules[1], await accounts[1].getAddress(), 'Rule2')
          assert.equal(rules[2], await accounts[2].getAddress(), 'Rule3')
          assert.equal(rules[3], await accounts[3].getAddress(), 'Rule4')
          assert.equal(rules[4], await accounts[4].getAddress(), 'Rule5')
        })
      })
    })
  })
})
