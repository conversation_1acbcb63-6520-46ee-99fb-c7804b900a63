import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { TransferProxyInstance } from '@test/common/types'
import { transferProxyFuncs } from '@test/TransferProxy/helpers/function'
import { TransferProxyContractType } from '@test/TransferProxy/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('findAll', () => {
  let accounts: SignerWithAddress[]
  let transferProxy: TransferProxyInstance

  const setupFixture = async () => {
    ;({ accounts, transferProxy } = await contractFixture<TransferProxyContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('未登録の場合は空を返す', async function () {
        const rules = await transferProxyFuncs.findAll({ transferProxy: transferProxy })
        assert.equal(rules.length, 0, 'Rule')
      })
    })

    describe('Ruleが5件登録されている状態', () => {
      before(async () => {
        for (let i = 0; i < 5; i++) {
          const rule = await accounts[i].getAddress()
          const position = i
          await transferProxyFuncs.addRule({ transferProxy: transferProxy, rule: rule, position: position })
        }
      })

      it('ルールを全て取得する', async function () {
        const rules = await transferProxyFuncs.findAll({ transferProxy: transferProxy })
        assert.equal(rules.length, 5, 'Rule')
        assert.equal(rules[0], await accounts[0].getAddress(), 'Rule1')
        assert.equal(rules[1], await accounts[1].getAddress(), 'Rule2')
        assert.equal(rules[2], await accounts[2].getAddress(), 'Rule3')
        assert.equal(rules[3], await accounts[3].getAddress(), 'Rule4')
        assert.equal(rules[4], await accounts[4].getAddress(), 'Rule5')
      })
    })
  })
})
