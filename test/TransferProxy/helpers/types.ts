import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccountInstance,
  ContractManagerInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  TransferableMock1Instance,
  TransferableMock2Instance,
  TransferableMock3Instance,
  TransferProxyInstance,
  ValidatorInstance,
} from '@test/common/types'
import { ContractTransactionResponse } from 'ethers'

export type TransferProxyContractType = {
  accounts: SignerWithAddress[]
  provider: ProviderInstance
  issuer: IssuerInstance
  validator: ValidatorInstance
  account: AccountInstance
  token: TokenInstance
  transferProxy: TransferProxyInstance
  contractManager: ContractManagerInstance
  customTransfer1: TransferableMock1Instance
  customTransfer2: TransferableMock2Instance
  customTransfer3: TransferableMock3Instance
  ibcToken: IBCTokenInstance
}

type TransferProxyType = { transferProxy: TransferProxyInstance }

type RuleType = TransferProxyType & { rule: string }

export type FuncParamsType = {
  version: TransferProxyType
  addRule: RuleType & {
    position: number
  }
  deleteRule: RuleType
  isRegistered: RuleType
  customTransfer: TransferProxyType & {
    sendAccountId: string
    fromAccountId: string
    toAccountId: string
    amount: number
    miscValue1: string
    miscValue2: string
  }
  findAll: TransferProxyType
  clearRule: TransferProxyType
}

export type FuncReturnType = {
  version: string
  addRule: ContractTransactionResponse
  deleteRule: ContractTransactionResponse
  isRegistered: boolean
  customTransfer: ContractTransactionResponse
  findAll: string[]
  clearRule: ContractTransactionResponse
}
export type TransferProxyFunctionType = {
  [K in keyof FuncParamsType]: (args: FuncParamsType[K]) => Promise<FuncReturnType[K]>
}
