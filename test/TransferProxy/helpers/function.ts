import { BASE } from '@test/common/consts'
import { castReturnType } from '@test/common/utils'
import { TransferProxyFunctionType } from './types'

/**
 * transferProxyのイベントを呼ぶ関数を持つobject
 */
export const transferProxyFuncs: TransferProxyFunctionType = {
  version: ({ transferProxy }) => {
    return castReturnType(transferProxy.version())
  },
  addRule: async ({ transferProxy, rule, position }) => {
    return castReturnType(transferProxy.addRule(rule, position))
  },
  deleteRule: async ({ transferProxy, rule }) => {
    return castReturnType(transferProxy.deleteRule(rule))
  },
  isRegistered: ({ transferProxy, rule }) => {
    return castReturnType(transferProxy.isRegistered(rule))
  },
  customTransfer: async ({
    transferProxy,
    sendAccountId,
    fromAccountId,
    toAccountId,
    amount,
    miscValue1,
    miscValue2,
  }) => {
    return castReturnType(
      await transferProxy.customTransfer(
        sendAccountId,
        fromAccountId,
        toAccountId,
        amount,
        miscValue1,
        miscValue2,
        BASE.MEMO,
        BASE.TRACE_ID,
      ),
    )
  },
  findAll: ({ transferProxy }) => {
    return castReturnType(transferProxy.findAll())
  },
  clearRule: ({ transferProxy }) => {
    return castReturnType(transferProxy.clearRule())
  },
}
