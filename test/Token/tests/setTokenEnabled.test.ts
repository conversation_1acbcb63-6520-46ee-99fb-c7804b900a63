import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, TokenInstance, ValidatorInstance } from '@test/common/types'
import { getExceededDeadline } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { TokenContractType } from '@test/Token/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { before } from 'mocha'

describe('setTokenEnabled()', () => {
  let accounts: SignerWithAddress[]
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let token: TokenInstance

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, token } = await contractFixture<TokenContractType>())
  }

  const setupProvider = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({ provider, accounts })
  }

  const setupIssuer = async () => {
    await issuerFuncs.addIssuer({ issuer, accounts })
  }

  const setupValidator = async () => {
    await validatorFuncs.addValidator({ validator, accounts })
  }

  const setupToken = async () => {
    await providerFuncs.addToken({ provider, accounts })
  }

  const setupProviderWithoutRole = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await setupIssuer()
    await setupValidator()
  }

  const setupTokenEnabledTest = async () => {
    await setupProvider()
    await setupIssuer()
    await setupValidator()
    await setupToken()
  }

  const setupTokenWithProviderRole = async () => {
    await providerFuncs.addProviderRole({ provider, accounts })
    await setupToken()
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('tokenが登録されている状態', () => {
      before(async () => {
        await setupTokenEnabledTest()
      })

      it('enabledがfalseに変更されること', async () => {
        const tokenId = BASE.TOKEN.TOKEN1.ID
        const enabled = false

        const tx = await tokenFuncs.setTokenEnabled({ token, accounts, enabled: enabled })

        await expect(tx).emit(token, 'SetEnabledToken').withArgs(tokenId, enabled, BASE.TRACE_ID)
      })

      it('enabledがtrueに変更されること', async () => {
        const tokenId = BASE.TOKEN.TOKEN1.ID
        const enabled = true

        const tx = await tokenFuncs.setTokenEnabled({ token, accounts, enabled: enabled })

        await expect(tx).emit(token, 'SetEnabledToken').withArgs(tokenId, enabled, BASE.TRACE_ID)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('providerRoleが登録されていない状態', () => {
      before(async () => {
        await setupProviderWithoutRole()
      })

      it('provider権限がない場合、エラーがスローされること', async () => {
        await expect(tokenFuncs.setTokenEnabled({ token, accounts, enabled: false })).revertedWith(
          ERR.PROV.PROV_NOT_ROLE,
        )
      })
    })

    describe('tokenが登録されている状態', () => {
      before(async () => {
        await setupTokenWithProviderRole()
      })

      it('未登録providerIdを指定した場合、エラーがスローされること', async () => {
        const providerId = BASE.PROV.PROV1.ID
        const enabled = false

        await expect(
          tokenFuncs.setTokenEnabled({ token, accounts, enabled: enabled, options: { providerId } }),
        ).revertedWith(ERR.PROV.PROV_ID_NOT_EXIST)
      })

      it('未登録tokenIdを指定した場合、エラーがスローされること', async () => {
        const tokenId = BASE.TOKEN.TOKEN2.ID
        const enabled = false

        await expect(
          tokenFuncs.setTokenEnabled({ token, accounts, enabled: enabled, options: { tokenId } }),
        ).revertedWith(ERR.TOKEN.TOKEN_ID_NOT_EXIST)
      })

      it('署名が不正である場合、エラーがスローされること', async () => {
        const enabled = false

        await expect(
          tokenFuncs.setTokenEnabled({
            token,
            accounts,
            enabled: enabled,
            options: { sig: ['0x1234', ''] },
          }),
        ).revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const enabled = false
        const exceededDeadline = await getExceededDeadline()

        await expect(
          tokenFuncs.setTokenEnabled({
            token,
            accounts,
            enabled: enabled,
            options: { deadline: exceededDeadline },
          }),
        ).revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })
    })
  })
})
