import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, TokenInstance, ValidatorInstance } from '@test/common/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { TokenContractType } from '@test/Token/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { before } from 'mocha'

describe('subTotalSupply', () => {
  let accounts: SignerWithAddress[]
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let token: TokenInstance

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, token } = await contractFixture<TokenContractType>())
  }

  const setupProvider = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({ provider, accounts })
  }

  const setupIssuer = async () => {
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
  }

  const setupValidator = async () => {
    await validatorFuncs.addValidator({ validator, accounts })
    await validatorFuncs.addValidatorRole({ validator, accounts })
  }

  const setupToken = async () => {
    await providerFuncs.addToken({ provider, accounts })
  }

  const setupAccounts = async () => {
    for (const accountId of [BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT1.ID, BASE.ACCOUNT.ACCOUNT2.ID]) {
      await validatorFuncs.addAccount({ validator, accounts, options: { accountId } })
    }
  }

  const setupTokenMint = async () => {
    await tokenFuncs.mint({ token, accounts, amount: 100 })
  }

  const setupUnderflowTest = async () => {
    await setupProvider()
    await setupIssuer()
    await setupValidator()
    await setupToken()
    await setupAccounts()
    await setupTokenMint()
  }

  describe('正常系', () => {})
  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('Underflow handling', () => {
      before(async () => {
        await setupUnderflowTest()
      })

      it('should revert when token underflow', async () => {
        await expect(token.subTotalSupply(1000)).revertedWith(ERR.TOKEN.TOKEN_UNDERFLOW)
      })
    })
  })
})
