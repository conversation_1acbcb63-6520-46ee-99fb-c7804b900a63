import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, TokenInstance, ValidatorInstance } from '@test/common/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { TokenContractType } from '@test/Token/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { before } from 'mocha'

// This MAX_UINT256 is used to test overflow handling
// Hex: 0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
const MAX_UINT256 = 115792089237316195423570985008687907853269984665640564039457584007913129639935n

describe('addTotalSupply', () => {
  let accounts: SignerWithAddress[]
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let token: TokenInstance

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, token } = await contractFixture<TokenContractType>())
  }

  const setupProvider = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({ provider, accounts })
  }

  const setupIssuer = async () => {
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
  }

  const setupValidator = async () => {
    await validatorFuncs.addValidator({ validator, accounts })
    await validatorFuncs.addValidatorRole({ validator, accounts })
  }

  const setupToken = async () => {
    await providerFuncs.addToken({ provider, accounts })
  }

  const setupAccounts = async () => {
    for (const accountId of [BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT1.ID, BASE.ACCOUNT.ACCOUNT2.ID]) {
      await validatorFuncs.addAccount({ validator, accounts, options: { accountId } })
    }
  }

  const setupTokenMint = async () => {
    await tokenFuncs.mint({ token, accounts, amount: 100 })
  }

  const setupOverflowTest = async () => {
    await setupProvider()
    await setupIssuer()
    await setupValidator()
    await setupToken()
    await setupAccounts()
    await setupTokenMint()
  }

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('overflow handling', () => {
      before(async () => {
        await setupOverflowTest()
      })
      it('should revert when totalSupply is overflow', async () => {
        // amount is never negative, use MAX_UINT256+1 to get overflow case
        // This revert will return "Arithmetic operation overflowed" instead of our message
        await expect(token.addTotalSupply(MAX_UINT256)).reverted
      })
    })
  })
  // TODO:
  //   contract('Not normal', (accounts) => {
  //     before(async () => {
  //       [provider, issuer, validator, account, token] = await contractFixture<TokenContractType>();
  //     });

  //     describe('provider error when getZone', () => {
  //       it('should revert when provider not exist', async () => {
  //         const paramsSetProvider: SetProviderAllOption = {
  //           providerId: BASE.PROV.EMPTY.ID,
  //           role: '0x8edf85e2d94cd8e4ad77c5b0aa98c9c7200b6f1bc68c11b67db960b27cb0f5d4',
  //           name: BASE.PROV.PROV0.NAME,
  //           zoneId: BASE.ZONE_ID.ID0,
  //           zoneName: BASE.ZONE_NAME.NAME0,
  //           enabled: true,
  //           providerEoa: accounts[BASE.EOA.PROV1],
  //         };
  //         await providerFuncs.setProviderAll(provider, paramsSetProvider);

  //         const params: AddTokenOption = {
  //           tokenId: BASE.TOKEN.TOKEN1.ID,
  //           providerId: BASE.PROV.EMPTY.ID,
  //           name: BASE.TOKEN.TOKEN1.NAME,
  //           symbol: BASE.TOKEN.TOKEN1.SYMBOL,
  //         };

  //         await truffleAssert.reverts(providerFuncs.addToken(provider, accounts, params), ERR.PROV.PROV_NOT_EXIST);
  //       });

  //       it('should revert when zoneId not exist', async () => {
  //         const paramsSetProvider: SetProviderAllOption = {
  //           providerId: BASE.PROV.PROV0.ID,
  //           role: '0x8edf85e2d94cd8e4ad77c5b0aa98c9c7200b6f1bc68c11b67db960b27cb0f5d4',
  //           name: BASE.PROV.PROV0.NAME,
  //           zoneId: BASE.ZONE_ID.EMPTY_ID,
  //           zoneName: BASE.ZONE_NAME.NAME0,
  //           enabled: true,
  //           providerEoa: accounts[BASE.EOA.PROV1],
  //         };
  //         await providerFuncs.setProviderAll(provider, paramsSetProvider);

  //         const params: AddTokenOption = {
  //           tokenId: BASE.TOKEN.TOKEN1.ID,
  //           providerId: BASE.PROV.PROV0.ID,
  //           name: BASE.TOKEN.TOKEN1.NAME,
  //           symbol: BASE.TOKEN.TOKEN1.SYMBOL,
  //         };
  //         await truffleAssert.reverts(providerFuncs.addToken(provider, accounts, params), ERR.PROV.ZONE_NOT_EXIST);
  //       });
  //     });
  //   });
  // });

  // describe('modToken', () => {
  //   // modTokenのテストはprovider.modTokenから呼ばれるためproviderのテストで実施する, ここでは呼び出し元検証のみ行う
  //   contract('準正常系', (accounts) => {
  //     before(async () => {
  //       [provider, issuer, validator, account, token] = await contractFixture<TokenContractType>();
  //     });

  //     describe('初期状態', () => {
  //       it('呼び出し元がProviderではない場合、エラーがスローされること', async () => {
  //         await truffleAssert.reverts(
  //           token.modToken(BASE.TOKEN.TOKEN1.ID, BASE.TOKEN.TOKEN1.NAME, BASE.TOKEN.TOKEN1.SYMBOL, BASE.TRACE_ID, {
  //             from: accounts[1], // providerコントラクト以外をfromに設定する
  //           }),
  //           ERR.PROV.NOT_PROVIDER_CONTRACT,
  //         );
  //       });
  //     });

  //     describe('key or token id not valid', () => {
  //       before(async () => {
  //         await providerFuncs.addProvider(provider, accounts);
  //         await providerFuncs.addProviderRole(provider, accounts);
  //         await providerFuncs.addToken(provider, accounts);
  //       });

  //       it('should revert when key is not valid', async () => {
  //         // Hack: Fake provider can call to modToken
  //         // Unreachable: Provider_modToken already check and revert when tokenId == 0x00
  //         // So this line can only reach if bypass Provider
  //         await helpers.impersonateAccount(provider.address);
  //         await helpers.setBalance(provider.address, 100n ** 18n);

  //         await truffleAssert.reverts(
  //           token.modToken(BASE.TOKEN.EMPTY, BASE.TOKEN.TOKEN1.NAME, BASE.TOKEN.TOKEN1.SYMBOL, BASE.TRACE_ID, {
  //             from: provider.address,
  //           }),
  //           ERR.TOKEN.TOKEN_INVALID_VAL,
  //         );
  //       });

  //       it('should revert when tokenId is not key', async () => {
  //         // Hack: Fake provider can call to modToken
  //         // Unreachable: Provider_modToken already check and revert when tokenId == 0x00
  //         // So this line can only reach if bypass Provider
  //         await helpers.impersonateAccount(provider.address);
  //         await helpers.setBalance(provider.address, 100n ** 18n);

  //         await truffleAssert.reverts(
  //           token.modToken(BASE.TOKEN.TOKEN2.ID, BASE.TOKEN.TOKEN1.NAME, BASE.TOKEN.TOKEN1.SYMBOL, BASE.TRACE_ID, {
  //             from: provider.address,
  //           }),
  //           ERR.TOKEN.NOT_TOKEN_ID,
  //         );
  //       });
  //   });
  // });
})
