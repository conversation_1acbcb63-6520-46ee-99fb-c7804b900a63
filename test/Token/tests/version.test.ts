import '@nomicfoundation/hardhat-chai-matchers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { TokenInstance } from '@test/common/types'
import { tokenFuncs } from '@test/Token/helpers/function'
import { TokenContractType } from '@test/Token/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('version()', () => {
  let token: TokenInstance

  const setupFixture = async () => {
    ;({ token } = await contractFixture<TokenContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    it('versionが返されること', async () => {
      assert.equal(await tokenFuncs.version({ token: token }), BASE.APP.VERSION, 'version')
    })
  })
})
