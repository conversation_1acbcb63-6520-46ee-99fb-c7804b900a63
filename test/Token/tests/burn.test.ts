import { accountFuncs } from '@/test/Account/helpers/function'
import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { time } from '@nomicfoundation/hardhat-network-helpers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountInstance, IssuerInstance, ProviderInstance, TokenInstance, ValidatorInstance } from '@test/common/types'
import { assertEqualForEachField, toBytes32 } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { TokenContractType } from '@test/Token/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { before } from 'mocha'

const TERMINATED_ACCOUNT1 = toBytes32('x490')

describe('burn()', () => {
  let accounts: SignerWithAddress[]
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let account: AccountInstance
  let token: TokenInstance

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, account, token } = await contractFixture<TokenContractType>())
  }

  const setupProvider = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({ provider, accounts })
  }

  const setupIssuer = async () => {
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
  }

  const setupValidator = async () => {
    await validatorFuncs.addValidator({ validator, accounts })
  }

  const setupToken = async () => {
    await providerFuncs.addToken({ provider, accounts })
  }

  const setupBasicAccounts = async () => {
    for (const accountId of [BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT1.ID]) {
      await validatorFuncs.addAccount({ validator, accounts, options: { accountId } })
    }
  }

  const setupTokenMint = async () => {
    await tokenFuncs.mint({
      token,
      accounts,
      amount: 200,
      options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
    })
    await tokenFuncs.mint({
      token,
      accounts,
      amount: 100,
      options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
    })
  }

  const setupBasicBurnTest = async () => {
    await setupProvider()
    await setupIssuer()
    await setupValidator()
    await setupToken()
    await setupBasicAccounts()
    await setupTokenMint()
  }

  const setupMultipleIssuers = async () => {
    await setupProvider()
    await setupIssuer()
    await issuerFuncs.addIssuer({
      issuer,
      accounts,
      options: {
        issuerId: BASE.ISSUER.ISSUER1.ID,
        bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
      },
    })
    await validatorFuncs.addValidator({
      validator,
      accounts,
      options: {
        validatorId: BASE.VALID.VALID1.ID,
        issuerId: BASE.ISSUER.ISSUER1.ID,
      },
    })
    await validatorFuncs.addAccount({
      validator,
      accounts,
      options: {
        accountId: BASE.ACCOUNT.ACCOUNT5.ID,
        validatorId: BASE.VALID.VALID1.ID,
      },
    })
    for (const _issuer of [BASE.ISSUER.ISSUER0, BASE.ISSUER.ISSUER1]) {
      await issuerFuncs.addIssuerRole({ issuer, accounts, options: { issuerId: _issuer.ID } })
    }
    await setupToken()
    await setupTokenMint()
  }

  const setupTerminatedAccount = async () => {
    await validatorFuncs.addAccount({
      validator,
      accounts,
      options: { accountId: TERMINATED_ACCOUNT1 },
    })
    await validatorFuncs.setTerminated({
      validator,
      accounts,
      options: { accountId: TERMINATED_ACCOUNT1 },
    })
  }

  const setupNotNormalTest = async () => {
    await setupProvider()
    await setupIssuer()
    await setupValidator()
    await setupToken()
    await validatorFuncs.addAccount({
      validator,
      accounts,
      options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
    })
    await validatorFuncs.addAccount({
      validator,
      accounts,
      options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
    })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('mintがされている状態(balance(account0: 200, account1: 100), totalSupply(300))', () => {
      before(async () => {
        await setupBasicBurnTest()
      })

      it('Burnされた額が減算されること、totalSupplyが減算されること', async () => {
        const accountId = BASE.ACCOUNT.ACCOUNT0.ID
        const accountName = BASE.ACCOUNT.ACCOUNT1.NAME
        const issuerId = BASE.ISSUER.ISSUER0.ID
        const amount = 100
        const tokenId = BASE.TOKEN.TOKEN1.ID
        const validatorId = BASE.VALID.VALID0.ID

        const burnTx = await tokenFuncs.burn({
          token,
          accounts,
          amount: amount,
          options: { accountId },
        })

        await expect(burnTx)
          .emit(token, 'Burn')
          .withArgs(
            BASE.ZONE_ID.ID0,
            validatorId,
            issuerId,
            accountId,
            accountName,
            amount,
            BigInt(BASE.TEST_BALANCES.AMOUNT_200 - BASE.TEST_BALANCES.AMOUNT_100),
            BASE.TRACE_ID,
          )
          .emit(account, 'AfterBalance')
          .withArgs(
            [[BASE.ZONE_ID.ID0, BigInt(BASE.TEST_BALANCES.AMOUNT_100)]], // fromBalance (after burn)
            [[BASE.ZONE_ID.ID0, 0n]], // toBalance (bytes32(0) returns array with zoneId and 0 balance)
            BASE.TRACE_ID,
          )

        const accountData = await accountFuncs.getAccount({ account, params: [accountId] })
        assertEqualForEachField(accountData.accountData, {
          balance: BASE.TEST_BALANCES.AMOUNT_200 - BASE.TEST_BALANCES.AMOUNT_100,
        })
        const totalSupply = await providerFuncs.getToken({ provider, options: [BASE.PROV.PROV0.ID] })
        assertEqualForEachField(totalSupply, {
          totalSupply: BASE.TEST_BALANCES.BALANCE_300 - BASE.TEST_BALANCES.AMOUNT_100,
        })
      })
    })

    describe('burnがされている状態(balance(account0: 100, account1: 100), totalSupply(200))', () => {
      it('(amount=0のため)値が変動されないこと', async () => {
        const accountId = BASE.ACCOUNT.ACCOUNT0.ID
        const accountName = BASE.ACCOUNT.ACCOUNT1.NAME
        const issuerId = BASE.ISSUER.ISSUER0.ID
        const amount = 0
        const tokenId = BASE.TOKEN.TOKEN1.ID
        const validatorId = BASE.VALID.VALID0.ID

        const burnTx = await tokenFuncs.burn({
          token,
          accounts,
          amount: amount,
          options: { accountId },
        })
        await expect(burnTx)
          .emit(token, 'Burn')
          .withArgs(
            BASE.ZONE_ID.ID0,
            validatorId,
            issuerId,
            accountId,
            accountName,
            amount,
            BigInt(BASE.TEST_BALANCES.AMOUNT_100),
            BASE.TRACE_ID,
          )
          .emit(account, 'AfterBalance')
          .withArgs(
            [[BASE.ZONE_ID.ID0, BigInt(BASE.TEST_BALANCES.AMOUNT_100)]], // fromBalance (unchanged as amount=0)
            [[BASE.ZONE_ID.ID0, 0n]], // toBalance (bytes32(0) returns array with zoneId and 0 balance)
            BASE.TRACE_ID,
          )

        const accountData = await accountFuncs.getAccount({ account, params: [accountId] })
        assertEqualForEachField(accountData.accountData, { balance: 100 })
        const totalSupply = await providerFuncs.getToken({ provider, options: [BASE.PROV.PROV0.ID] })
        assertEqualForEachField(totalSupply, { totalSupply: 200 })
      })

      it('異なるaccountに対してBurnした場合でも、Burnされた額が減算されること、totalSupplyが減算されること', async () => {
        const accountId = BASE.ACCOUNT.ACCOUNT1.ID
        const accountName = BASE.ACCOUNT.ACCOUNT1.NAME
        const issuerId = BASE.ISSUER.ISSUER0.ID
        const amount = 100
        const tokenId = BASE.TOKEN.TOKEN1.ID
        const validatorId = BASE.VALID.VALID0.ID

        const burnTx = await tokenFuncs.burn({ token, accounts, amount: amount })

        const accountData = await accountFuncs.getAccount({ account, params: [accountId] })

        await expect(burnTx)
          .emit(token, 'Burn')
          .withArgs(
            BASE.ZONE_ID.ID0,
            validatorId,
            issuerId,
            accountId,
            accountName,
            amount,
            accountData.accountData.balance,
            BASE.TRACE_ID,
          )
          .emit(account, 'AfterBalance')
          .withArgs(
            [[BASE.ZONE_ID.ID0, 0n]], // fromBalance (after burn)
            [[BASE.ZONE_ID.ID0, 0n]], // toBalance (bytes32(0) returns array with zoneId and 0 balance)
            BASE.TRACE_ID,
          )

        assertEqualForEachField(accountData.accountData, { balance: 0 })
        const totalSupply = await providerFuncs.getToken({ provider, options: [BASE.PROV.PROV0.ID] })
        assertEqualForEachField(totalSupply, {
          totalSupply: BASE.TEST_BALANCES.AMOUNT_200 - BASE.TEST_BALANCES.AMOUNT_100,
        })
      })
    })
    describe('一回の操作限度額を50, 累積限度額を400のアカウントを作成した場合', () => {
      const accountName = BASE.ACCOUNT.ACCOUNT1.NAME
      const issuerId = BASE.ISSUER.ISSUER0.ID
      const accountId = BASE.ACCOUNT.ACCOUNT10.ID
      const limitAmounts = [400, 400, 400, 50, 400]
      const validatorId = BASE.VALID.VALID0.ID

      before(async () => {
        await validatorFuncs.addAccount({
          validator,
          accounts,
          options: { accountId, limitAmounts },
        })
        await tokenFuncs.mint({ token, accounts, amount: 300, options: { accountId: accountId } })
      })

      it('日跨りで一日の累積限度額を3回目の償却で超えている場合、残高が減算されること', async () => {
        const amount = 50
        const accountData = await accountFuncs.getAccount({ account, params: [accountId] })
        await tokenFuncs.burn({ token, accounts, amount: amount, options: { accountId } })
        await tokenFuncs.burn({ token, accounts, amount: amount, options: { accountId } })
        // 1日（24時間）時間を進める
        await time.increase(24 * 60 * 60)
        const burnTx = await tokenFuncs.burn({
          token,
          accounts,
          amount: amount,
          options: { accountId },
        })

        await expect(burnTx)
          .emit(token, 'Burn')
          .withArgs(
            BASE.ZONE_ID.ID0,
            validatorId,
            issuerId,
            accountId,
            accountName,
            amount,
            Number(accountData.accountData.balance) - BASE.TEST_BALANCES.AMOUNT_50 * 3,
            BASE.TRACE_ID,
          )
          .emit(account, 'AfterBalance')
          .withArgs(
            [[BASE.ZONE_ID.ID0, BigInt(BASE.TEST_BALANCES.BALANCE_150)]], // fromBalance (after burn)
            [[BASE.ZONE_ID.ID0, 0n]], // toBalance (bytes32(0) returns array with zoneId and 0 balance)
            BASE.TRACE_ID,
          )
        const afterAccountData = await accountFuncs.getAccount({ account, params: [accountId] })
        assertEqualForEachField(afterAccountData.accountData, { balance: BASE.TEST_BALANCES.BALANCE_150 })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('mintがされている状態(balance(account0: 200, account1: 100), totalSupply(300))', () => {
      before(async () => {
        await setupMultipleIssuers()
      })

      it('空accountIdを指定した場合、エラーがスローされること', async () => {
        const accountId = BASE.ACCOUNT.EMPTY.ID
        const amount = 100

        const result = tokenFuncs.burn({ token, accounts, amount, options: { accountId } })
        await expect(result).to.be.revertedWith(ERR.TOKEN.INVALID_ACCOUNT_ID)
      })

      it('空issuerIdを指定した場合、エラーがスローされること', async () => {
        const accountId = BASE.ACCOUNT.ACCOUNT0.ID
        const issuerId = BASE.ISSUER.EMPTY.ID
        const amount = 100

        const result = tokenFuncs.burn({ token, accounts, amount, options: { issuerId, accountId } })
        await expect(result).to.be.revertedWith(ERR.TOKEN.INVALID_ORGANIZATION_ID)
      })

      it('balanceが不足している場合、エラーがスローされること', async () => {
        const result = tokenFuncs.burn({
          token,
          accounts,
          amount: 200,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        await expect(result).to.be.revertedWith(ERR.TOKEN.TOKEN_BALANCE_NOT_ENOUGH)
      })
    })

    describe('accountが解約状態の場合', () => {
      before(async () => {
        await setupTerminatedAccount()
      })

      // TODO アカウントステータスの保持の仕方が確定し、アカウントコントラクトが実装された後に確認
      // it('解約済accountIdを指定した場合、エラーがスローされること', async () => {
      //   const accountId = TERMINATED_ACCOUNT1;
      //   const amount = 100;

      //   await expect(
      //     tokenFuncs.burn(token, accounts, amount, { accountId })).revertedWith(
      //     ERR.ACCOUNT.ACCOUNT_TERMINATED,
      //   );
      // });
    })
    // provider.getZoneでエラーがあるケースは、事前準備ができないため未実施
  })

  describe('Not normal', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, account, token } = await contractFixture<TokenContractType>())
    })

    describe('getZone return some error', () => {
      before(async () => {
        await setupNotNormalTest()
      })
      // TODO:後で修正
      // it('should revert when getZone return error provider not exist', async () => {
      //   const paramsSetProvider: SetProviderAllOption = {
      //     providerId: BASE.PROV.EMPTY.ID,
      //     role: '0x8edf85e2d94cd8e4ad77c5b0aa98c9c7200b6f1bc68c11b67db960b27cb0f5d4',
      //     name: BASE.PROV.PROV0.NAME,
      //     zoneId: BASE.ZONE_ID.ID0,
      //     zoneName: BASE.ZONE_NAME.NAME0,
      //     enabled: true,
      //     providerEoa: accounts[BASE.EOA.PROV1],
      //   };
      //   await providerFuncs.setProviderAll(provider, paramsSetProvider);

      //   const accountId = BASE.ACCOUNT.ACCOUNT0.ID;
      //   const amount = 100;

      //   await truffleAssert.reverts(tokenFuncs.burn(token, accounts, amount, { accountId }), ERR.PROV.PROV_NOT_EXIST);
      // });

      // it('should revert when getZone return error zoneId is 0', async () => {
      //   const paramsSetProvider: SetProviderAllOption = {
      //     providerId: BASE.PROV.PROV0.ID,
      //     role: '0x8edf85e2d94cd8e4ad77c5b0aa98c9c7200b6f1bc68c11b67db960b27cb0f5d4',
      //     name: BASE.PROV.PROV0.NAME,
      //     zoneId: BASE.ZONE_ID.EMPTY_ID,
      //     zoneName: BASE.ZONE_NAME.NAME0,
      //     enabled: true,
      //     providerEoa: accounts[BASE.EOA.PROV1],
      //   };
      //   await providerFuncs.setProviderAll(provider, paramsSetProvider);

      //   const accountId = BASE.ACCOUNT.ACCOUNT0.ID;
      //   const amount = 100;

      //   await truffleAssert.reverts(
      //     tokenFuncs.burn(token, accounts, amount, { accountId }),
      //     ERR.PROV.ZONE_NOT_EXIST,
      //   );
      // });
    })
  })
})
