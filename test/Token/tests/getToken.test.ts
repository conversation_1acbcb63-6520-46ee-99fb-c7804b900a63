import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { TokenInstance } from '@test/common/types'
import { TokenContractType } from '@test/Token/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('getToken', () => {
  let accounts: SignerWithAddress[]
  let token: TokenInstance

  const setupFixture = async () => {
    ;({ accounts, token } = await contractFixture<TokenContractType>())
  }

  // getTokenのテストはprovider.getTokenから呼ばれるためproviderのテストで実施する, ここでは呼び出し元検証のみ行う
  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('呼び出し元がProviderではない場合、エラーがスローされること', async () => {
        await expect(token.connect(accounts[1]).getToken()).revertedWith(ERR.PROV.NOT_PROVIDER_CONTRACT)
      })
    })
  })
})
