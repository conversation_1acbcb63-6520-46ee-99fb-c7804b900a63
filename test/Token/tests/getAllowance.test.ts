import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, TokenInstance, ValidatorInstance } from '@test/common/types'
import { assertEqualForEachField } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { TokenContractType } from '@test/Token/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('getAllowance()', () => {
  let accounts: SignerWithAddress[]
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let token: TokenInstance

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, token } = await contractFixture<TokenContractType>())
  }

  const setupProvider = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({ provider, accounts })
  }

  const setupIssuer = async () => {
    await issuerFuncs.addIssuer({ issuer, accounts })
  }

  const setupValidator = async () => {
    await validatorFuncs.addValidator({ validator, accounts })
    await validatorFuncs.addValidatorRole({ validator, accounts })
  }

  const setupToken = async () => {
    await providerFuncs.addToken({ provider, accounts })
  }

  const setupAccounts = async () => {
    for (const accountId of [BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT1.ID, BASE.ACCOUNT.ACCOUNT2.ID]) {
      await validatorFuncs.addAccount({ validator, accounts, options: { accountId } })
    }
  }

  const setupTokenApprove = async () => {
    await tokenFuncs.approve({
      token,
      accounts,
      spenderId: BASE.ACCOUNT.ACCOUNT1.ID,
      amount: 200,
      options: {
        validatorId: BASE.VALID.VALID0.ID,
        ownerId: BASE.ACCOUNT.ACCOUNT0.ID,
      },
    })
  }

  const setupGetAllowanceTest = async () => {
    await setupProvider()
    await setupIssuer()
    await setupValidator()
    await setupToken()
    await setupAccounts()
    await setupTokenApprove()
  }

  const setupValidatorNotRegisteredTest = async () => {
    await setupProvider()
    await setupIssuer()
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('allowanceが設定されている状態', () => {
      before(async () => {
        await setupGetAllowanceTest()
      })

      it('approveで指定した額が返されること', async () => {
        const result = await tokenFuncs.getAllowance({
          token,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        assertEqualForEachField(result, { allowance: 200 })
      })

      it('approveで設定していないaccountIdを指定した場合、0が返されること', async () => {
        const result = await tokenFuncs.getAllowance({
          token,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT2.ID],
        })

        assertEqualForEachField(result, { allowance: 0 })
      })

      it('未登録accountIdをspenderに指定した場合、エラーがスローされること', async () => {
        const result = await tokenFuncs.getAllowance({
          token,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT3.ID],
        })

        assert.equal(result.err, ERR.ACCOUNT.SPENDER_NOT_EXIST, 'Error')
      })

      it('未登録accountIdをownerに指定した場合、エラーがスローされること', async () => {
        const result = await tokenFuncs.getAllowance({
          token,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT3.ID, BASE.ACCOUNT.ACCOUNT0.ID],
        })

        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST, 'Error')
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('validatorが登録されていない状態', () => {
      before(async () => {
        await setupValidatorNotRegisteredTest()
      })

      it('validatorIdが未入力である場合、エラーがスローされること', async () => {
        const result = await tokenFuncs.getAllowance({
          token,
          prams: [BASE.VALID.EMPTY.ID, BASE.ACCOUNT.ACCOUNT3.ID, BASE.ACCOUNT.ACCOUNT0.ID],
        })

        assert.equal(result.err, ERR.VALID.VALIDATOR_INVALID_VAL, 'Error')
      })

      it('存在しないvalidatorを指定した場合、エラーがスローされること', async () => {
        const result = await tokenFuncs.getAllowance({
          token,
          prams: [BASE.VALID.VALID9.ID, BASE.ACCOUNT.ACCOUNT3.ID, BASE.ACCOUNT.ACCOUNT0.ID],
        })

        assert.equal(result.err, ERR.VALID.VALIDATOR_ID_NOT_EXIST, 'Error')
      })
    })
  })
})
