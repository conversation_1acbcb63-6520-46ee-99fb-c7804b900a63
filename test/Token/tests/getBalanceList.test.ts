import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { businessZoneAccountFuncs } from '@test/BusinessZoneAccount/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { assertEqualForEachField } from '@test/common/utils'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { ibcTokenFuncs } from '@test/IBCToken/helpers/function'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { TokenContractType } from '@test/Token/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('getBalanceList()', () => {
  let accounts: SignerWithAddress[]
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let token: TokenInstance
  let businessZoneAccount: BusinessZoneAccountInstance
  let contractManager: ContractManagerInstance
  let ibcToken: IBCTokenInstance

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, contractManager, validator, token, ibcToken, businessZoneAccount } =
      await contractFixture<TokenContractType>())
  }

  const setupProvider = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({ provider, accounts })
  }

  const setupIssuer = async () => {
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
  }

  const setupValidator = async () => {
    await validatorFuncs.addValidator({ validator, accounts })
  }

  const setupToken = async () => {
    await providerFuncs.addToken({ provider, accounts })
  }

  const setupAccounts = async () => {
    await validatorFuncs.addAccount({
      validator,
      accounts,
      options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
    })
    await validatorFuncs.addAccount({
      validator,
      accounts,
      options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
    })
  }

  const setupTokenMint = async () => {
    await tokenFuncs.mint({
      token,
      accounts,
      amount: 300,
      options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
    })
    await tokenFuncs.mint({
      token,
      accounts,
      amount: 300,
      options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
    })
  }

  const setupIbcApps = async (ibcAddress: string) => {
    await contractManagerFuncs.setIbcApp({
      contractManager,
      accounts,
      ibcAddress: ibcAddress,
      ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
    })
    await contractManagerFuncs.setIbcApp({
      contractManager,
      accounts,
      ibcAddress: ibcAddress,
      ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
    })
  }

  const setupBusinessZones = async () => {
    await providerFuncs.addBizZone({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID1,
        zoneName: BASE.ZONE_NAME.NAME1,
      },
    })
    await await providerFuncs.addBizZone({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID2,
        zoneName: BASE.ZONE_NAME.NAME2,
      },
    })
  }

  const setupBusinessZoneAccounts = async () => {
    await businessZoneAccountFuncs.syncBusinessZoneStatus({
      businessZoneAccount,
      accounts,
      options: {
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
        zoneId: BASE.ZONE_ID.ID1,
      },
    })
    await validatorFuncs.setActiveBusinessAccountWithZone({
      validator,
      accounts,
      options: {
        validatorId: BASE.VALID.VALID0.ID,
        zoneId: BASE.ZONE_ID.ID1,
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
      },
    })

    await businessZoneAccountFuncs.syncBusinessZoneStatus({
      businessZoneAccount,
      accounts,
      options: {
        accountId: BASE.ACCOUNT.ACCOUNT0.ID,
        zoneId: BASE.ZONE_ID.ID1,
      },
    })
    await validatorFuncs.setActiveBusinessAccountWithZone({
      validator,
      accounts,
      options: {
        validatorId: BASE.VALID.VALID0.ID,
        zoneId: BASE.ZONE_ID.ID1,
        accountId: BASE.ACCOUNT.ACCOUNT0.ID,
      },
    })
    await businessZoneAccountFuncs.syncBusinessZoneStatus({
      businessZoneAccount,
      accounts,
      options: {
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
        zoneId: BASE.ZONE_ID.ID2,
      },
    })
    await validatorFuncs.setActiveBusinessAccountWithZone({
      validator,
      accounts,
      options: {
        validatorId: BASE.VALID.VALID0.ID,
        zoneId: BASE.ZONE_ID.ID2,
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
      },
    })
  }

  const setupIbcTokenTransfer = async (ibcAddress: any) => {
    await ibcTokenFuncs.transferToEscrow({
      ibcToken,
      from: ibcAddress,
      amount: 300,
      options: {
        fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
        toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
      },
    })
  }

  const setupTerminatedAccount = async () => {
    await validatorFuncs.setBizZoneTerminated({
      validator,
      accounts,
      options: {
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
        zoneId: BASE.ZONE_ID.ID1,
      },
    })
  }

  const setupBalanceListTest = async (ibcAddress: any, ibcAddressString: string) => {
    await setupProvider()
    await setupIssuer()
    await setupValidator()
    await setupToken()
    await setupAccounts()
    await setupTokenMint()
    await setupIbcApps(ibcAddressString)
    await setupBusinessZones()
    await setupBusinessZoneAccounts()
    await setupIbcTokenTransfer(ibcAddress)
  }

  const setupTerminatedAccountTest = async (ibcAddress: string) => {
    await setupProvider()
    await setupIssuer()
    await setupValidator()
    await setupToken()
    await setupAccounts()
    await setupTokenMint()
    await setupIbcApps(ibcAddress)
    await setupBusinessZones()
    await businessZoneAccountFuncs.syncBusinessZoneStatus({
      businessZoneAccount,
      accounts,
      options: {
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
        zoneId: BASE.ZONE_ID.ID1,
      },
    })
    await validatorFuncs.setActiveBusinessAccountWithZone({
      validator,
      accounts,
      options: {
        validatorId: BASE.VALID.VALID0.ID,
        zoneId: BASE.ZONE_ID.ID1,
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
      },
    })
    await businessZoneAccountFuncs.syncBusinessZoneStatus({
      businessZoneAccount,
      accounts,
      options: {
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
        zoneId: BASE.ZONE_ID.ID2,
      },
    })
    await validatorFuncs.setActiveBusinessAccountWithZone({
      validator,
      accounts,
      options: {
        validatorId: BASE.VALID.VALID0.ID,
        zoneId: BASE.ZONE_ID.ID2,
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
      },
    })
    await setupTerminatedAccount()
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('provider, providerRole, issuer, validator, token, account, 二つのbusinessZoneAccountが登録されている状態', () => {
      let ibcAddress
      let ibcAddressString

      before(async () => {
        ibcAddress = await accounts[0]
        ibcAddressString = await ibcAddress.getAddress()
        await setupBalanceListTest(ibcAddress, ibcAddressString)
      })

      it('BusinessZoneのアカウント残高の一覧が取得できること', async () => {
        const zoneIds = [BASE.ZONE_ID.ID1, BASE.ZONE_ID.ID2]
        const zoneNames = [BASE.ZONE_NAME.NAME1, BASE.ZONE_NAME.NAME2]
        const balances = [300, 0]
        const accountNames = [BASE.ACCOUNT.ACCOUNT1.NAME, BASE.ACCOUNT.ACCOUNT1.NAME]
        const accountStatus = [BASE.STATUS.ACTIVE, BASE.STATUS.ACTIVE]
        const totalBalance = 300

        const result = await tokenFuncs.getBalanceList({ token, prams: [BASE.ACCOUNT.ACCOUNT1.ID] })

        assertEqualForEachField(result.zoneIds, zoneIds)
        assertEqualForEachField(result.zoneNames, zoneNames)
        assertEqualForEachField(result.balances, balances)
        assertEqualForEachField(result.accountNames, accountNames)
        assertEqualForEachField(result.accountStatus, accountStatus)
        assert.equal(result.totalBalance, totalBalance)
      })

      it('存在しないアカウントIDで実行した場合、エラーが返却されること', async () => {
        const result = await tokenFuncs.getBalanceList({ token, prams: [BASE.ACCOUNT.ACCOUNT20.ID] })

        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })

      it('空のアカウントIDで実行した場合、エラーが返却されること', async () => {
        const result = await tokenFuncs.getBalanceList({ token, prams: [BASE.ACCOUNT.EMPTY.ID] })

        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_INVALID_VAL)
      })
    })
  })

  describe('準正常系', () => {
    let ibcAddress: string
    before(async () => {
      await setupFixture()
      ibcAddress = await accounts[0].getAddress()
    })

    describe('account is terminated', () => {
      before(async () => {
        await setupTerminatedAccountTest(ibcAddress)
      })

      it('should return data of accountId without any changed when account is terminated ', async () => {
        const result = await tokenFuncs.getBalanceList({ token, prams: [BASE.ACCOUNT.ACCOUNT1.ID] })
        assertEqualForEachField(result.zoneIds, [])
      })
    })
  })
})
