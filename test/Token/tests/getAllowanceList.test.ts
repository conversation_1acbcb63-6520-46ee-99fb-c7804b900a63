import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, TokenInstance, ValidatorInstance } from '@test/common/types'
import { assertEqualForEachField, getDeadline } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { TokenContractType } from '@test/Token/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'
import { PromiseType } from 'utility-types'

// Chai global vars
declare let assert: Chai.Assert

describe('getAllowanceList()', () => {
  let accounts: SignerWithAddress[]
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let token: TokenInstance

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, token } = await contractFixture<TokenContractType>())
  }

  const setupProvider = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({ provider, accounts })
  }

  const setupIssuer = async () => {
    await issuerFuncs.addIssuer({ issuer, accounts })
  }

  const setupValidator = async () => {
    await validatorFuncs.addValidator({ validator, accounts })
    await validatorFuncs.addValidatorRole({ validator, accounts })
  }

  const setupToken = async () => {
    await providerFuncs.addToken({ provider, accounts })
  }

  const setupOwnerAccount = async () => {
    await validatorFuncs.addAccount({
      validator,
      accounts,
      options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
    })
  }

  const setupSpenderAccounts = async (approvalParams: any[]) => {
    for (let i = 0; i < approvalParams.length; i++) {
      await validatorFuncs.addAccount({
        validator,
        accounts,
        options: {
          accountId: approvalParams[i].spender,
        },
      })
    }
  }

  const setupTokenApprovals = async (approvalParams: any[]) => {
    for (let i = 0; i < approvalParams.length; i++) {
      await tokenFuncs.approve({
        token,
        accounts,
        spenderId: approvalParams[i].spender,
        amount: approvalParams[i].amount,
        options: {
          validatorId: approvalParams[i].validator,
          ownerId: approvalParams[i].owner,
          deadline: (await getDeadline()) + 100, // 複数approve時の途中でsig timeoutとなるため、長めにdeadlineを設定
        },
      })
    }
  }

  const setupBasicTest = async () => {
    await setupProvider()
    await setupIssuer()
    await setupValidator()
    await setupToken()
  }

  const setupAllowanceTest = async (approvalParams: any[]) => {
    await setupOwnerAccount()
    await setupSpenderAccounts(approvalParams)
    await setupTokenApprovals(approvalParams)
  }

  const setupSpenderNotRegisteredTest = async () => {
    await setupBasicTest()
    await setupOwnerAccount()
  }

  before(async () => {
    await setupFixture()
  })
  describe('正常系', () => {
    const approvalParams = [
      {
        validator: BASE.VALID.VALID0.ID,
        spender: BASE.ACCOUNT.ACCOUNT1.ID,
        owner: BASE.ACCOUNT.ACCOUNT0.ID,
        amount: 100,
      },
      {
        validator: BASE.VALID.VALID0.ID,
        spender: BASE.ACCOUNT.ACCOUNT2.ID,
        owner: BASE.ACCOUNT.ACCOUNT0.ID,
        amount: 200,
      },
      {
        validator: BASE.VALID.VALID0.ID,
        spender: BASE.ACCOUNT.ACCOUNT3.ID,
        owner: BASE.ACCOUNT.ACCOUNT0.ID,
        amount: 300,
      },
      {
        validator: BASE.VALID.VALID0.ID,
        spender: BASE.ACCOUNT.ACCOUNT4.ID,
        owner: BASE.ACCOUNT.ACCOUNT0.ID,
        amount: 400,
      },
      {
        validator: BASE.VALID.VALID0.ID,
        spender: BASE.ACCOUNT.ACCOUNT5.ID,
        owner: BASE.ACCOUNT.ACCOUNT0.ID,
        amount: 500,
      },
      {
        validator: BASE.VALID.VALID0.ID,
        spender: BASE.ACCOUNT.ACCOUNT6.ID,
        owner: BASE.ACCOUNT.ACCOUNT0.ID,
        amount: 600,
      },
      {
        validator: BASE.VALID.VALID0.ID,
        spender: BASE.ACCOUNT.ACCOUNT7.ID,
        owner: BASE.ACCOUNT.ACCOUNT0.ID,
        amount: 700,
      },
      {
        validator: BASE.VALID.VALID0.ID,
        spender: BASE.ACCOUNT.ACCOUNT8.ID,
        owner: BASE.ACCOUNT.ACCOUNT0.ID,
        amount: 800,
      },
      {
        validator: BASE.VALID.VALID0.ID,
        spender: BASE.ACCOUNT.ACCOUNT9.ID,
        owner: BASE.ACCOUNT.ACCOUNT0.ID,
        amount: 900,
      },
      {
        validator: BASE.VALID.VALID0.ID,
        spender: BASE.ACCOUNT.ACCOUNT10.ID,
        owner: BASE.ACCOUNT.ACCOUNT0.ID,
        amount: 1000,
      },
      {
        validator: BASE.VALID.VALID0.ID,
        spender: BASE.ACCOUNT.ACCOUNT11.ID,
        owner: BASE.ACCOUNT.ACCOUNT0.ID,
        amount: 1100,
      },
      {
        validator: BASE.VALID.VALID0.ID,
        spender: BASE.ACCOUNT.ACCOUNT12.ID,
        owner: BASE.ACCOUNT.ACCOUNT0.ID,
        amount: 1200,
      },
      {
        validator: BASE.VALID.VALID0.ID,
        spender: BASE.ACCOUNT.ACCOUNT13.ID,
        owner: BASE.ACCOUNT.ACCOUNT0.ID,
        amount: 1300,
      },
      {
        validator: BASE.VALID.VALID0.ID,
        spender: BASE.ACCOUNT.ACCOUNT14.ID,
        owner: BASE.ACCOUNT.ACCOUNT0.ID,
        amount: 1400,
      },
      {
        validator: BASE.VALID.VALID0.ID,
        spender: BASE.ACCOUNT.ACCOUNT15.ID,
        owner: BASE.ACCOUNT.ACCOUNT0.ID,
        amount: 1500,
      },
      {
        validator: BASE.VALID.VALID0.ID,
        spender: BASE.ACCOUNT.ACCOUNT16.ID,
        owner: BASE.ACCOUNT.ACCOUNT0.ID,
        amount: 1600,
      },
      {
        validator: BASE.VALID.VALID0.ID,
        spender: BASE.ACCOUNT.ACCOUNT17.ID,
        owner: BASE.ACCOUNT.ACCOUNT0.ID,
        amount: 1700,
      },
      {
        validator: BASE.VALID.VALID0.ID,
        spender: BASE.ACCOUNT.ACCOUNT18.ID,
        owner: BASE.ACCOUNT.ACCOUNT0.ID,
        amount: 1800,
      },
      {
        validator: BASE.VALID.VALID0.ID,
        spender: BASE.ACCOUNT.ACCOUNT19.ID,
        owner: BASE.ACCOUNT.ACCOUNT0.ID,
        amount: 1900,
      },
      {
        validator: BASE.VALID.VALID0.ID,
        spender: BASE.ACCOUNT.ACCOUNT20.ID,
        owner: BASE.ACCOUNT.ACCOUNT0.ID,
        amount: 2000,
      },
    ]

    const assertList = (
      result: PromiseType<ReturnType<typeof tokenFuncs.getAllowanceList>>,
      expected: typeof approvalParams,
    ) => {
      expected.map((v, i) => {
        assertEqualForEachField(result.approvalData[i], {
          spanderId: v.spender,
          spenderAccountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          allowanceAmount: v.amount,
        })
      })
    }

    before(async () => {
      await setupFixture()
    })

    describe('accountが登録されていない状態', () => {
      before(async () => {
        await setupBasicTest()
      })

      it('未登録accountIdをownerに指定した場合、エラーがスローされること', async () => {
        const offset = 0
        const limit = 10
        const result = await tokenFuncs.getAllowanceList({
          token,
          prams: [BASE.ACCOUNT.ACCOUNT0.ID, offset, limit],
        })

        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST, 'Error')
      })

      it('accountIdが空の場合、エラーがスローされること', async () => {
        const offset = 0
        const limit = 10
        const result = await tokenFuncs.getAllowanceList({
          token,
          prams: [BASE.ACCOUNT.EMPTY.ID, offset, limit],
        })

        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_INVALID_VAL, 'Error')
      })
    })

    describe('allowanceが設定されている状態', () => {
      before(async () => {
        await setupAllowanceTest(approvalParams)
      })

      it('複数のgetAllowanceList呼び出しが同時に行われた場合、それぞれ正しい結果を返すこと', async () => {
        const request = [
          tokenFuncs.getAllowanceList({ token, prams: [BASE.ACCOUNT.ACCOUNT0.ID, 0, 3] }),
          tokenFuncs.getAllowanceList({ token, prams: [BASE.ACCOUNT.ACCOUNT0.ID, 2, 4] }),
          tokenFuncs.getAllowanceList({ token, prams: [BASE.ACCOUNT.ACCOUNT0.ID, 5, 5] }),
        ]

        const results = await Promise.all(request)

        assertEqualForEachField(results[0], { totalCount: approvalParams.length, err: '' })
        assertList(results[0], approvalParams.slice(0, 3))
        assertEqualForEachField(results[1], { totalCount: approvalParams.length, err: '' })
        assertList(results[1], approvalParams.slice(2, 6))
        assertEqualForEachField(results[2], { totalCount: approvalParams.length, err: '' })
        assertList(results[2], approvalParams.slice(5, 10))
      })

      it('offset0, limit10を指定した場合、1要素目から10件取得できること', async () => {
        const offset = 0
        const limit = 10

        const result = await tokenFuncs.getAllowanceList({
          token,
          prams: [BASE.ACCOUNT.ACCOUNT0.ID, offset, limit],
        })

        assertEqualForEachField(result, { totalCount: approvalParams.length, err: '' })
        assertList(result, approvalParams.slice(0, 10))
      })

      it('offset1, limit10を指定した場合、2要素目から10件取得できること', async () => {
        const offset = 1
        const limit = 10

        const result = await tokenFuncs.getAllowanceList({
          token,
          prams: [BASE.ACCOUNT.ACCOUNT0.ID, offset, limit],
        })

        assertEqualForEachField(result, { totalCount: approvalParams.length, err: '' })
        assertList(result, approvalParams.slice(1, 11))
      })

      it('offset1, limit2を指定した場合、2要素目から2件取得できること', async () => {
        const offset = 1
        const limit = 2

        const result = await tokenFuncs.getAllowanceList({
          token,
          prams: [BASE.ACCOUNT.ACCOUNT0.ID, offset, limit],
        })

        assertEqualForEachField(result, { totalCount: approvalParams.length, err: '' })
        assertList(result, approvalParams.slice(1, 2))
      })

      it('最後の1件が取得できること', async () => {
        const offset = 19
        const limit = 1

        const result = await tokenFuncs.getAllowanceList({
          token,
          prams: [BASE.ACCOUNT.ACCOUNT0.ID, offset, limit],
        })

        assertEqualForEachField(result, { totalCount: approvalParams.length, err: '' })
        assertList(result, [approvalParams[approvalParams.length - 1]])
      })

      it('limitが0の場合、空リストが取得できること', async () => {
        const offset = 2
        const limit = 0

        const result = await tokenFuncs.getAllowanceList({
          token,
          prams: [BASE.ACCOUNT.ACCOUNT0.ID, offset, limit],
        })

        assertEqualForEachField(result, { totalCount: 0, err: '' })
        assertList(result, [])
      })

      it('limitを登録数以上に指定した場合、登録されている要素数の範囲内でのみ取得できること', async () => {
        const offset = 0
        const limit = 50

        const result = await tokenFuncs.getAllowanceList({
          token,
          prams: [BASE.ACCOUNT.ACCOUNT0.ID, offset, limit],
        })

        assertEqualForEachField(result, { totalCount: approvalParams.length, err: '' })
        assertList(result, approvalParams.slice(0, 20))
      })

      it('limitが取得上限(100件)より大きい場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 101

        const result = await tokenFuncs.getAllowanceList({
          token,
          prams: [BASE.ACCOUNT.ACCOUNT0.ID, offset, limit],
        })

        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_TOO_LARGE_LIMIT, 'err: too large limit')
      })

      it('offsetが登録されている件数以上の場合、エラーが返されること', async () => {
        const offset = approvalParams.length + 1
        const limit = 20

        const result = await tokenFuncs.getAllowanceList({
          token,
          prams: [BASE.ACCOUNT.ACCOUNT0.ID, offset, limit],
        })

        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_OFFSET_OUT_OF_INDEX, 'err: out of index (offset)')
      })

      it('offsetが配列と同じ長さの場合、エラーが返されること', async () => {
        const offset = approvalParams.length
        const limit = 20

        const result = await tokenFuncs.getAllowanceList({
          token,
          prams: [BASE.ACCOUNT.ACCOUNT0.ID, offset, limit],
        })

        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_OFFSET_OUT_OF_INDEX, 'err: out of index (offset)')
      })
    })
  })

  describe('準正常系', () => {
    const approvalParams = [
      {
        validator: BASE.VALID.VALID0.ID,
        spender: BASE.ACCOUNT.EMPTY.ID,
        owner: BASE.ACCOUNT.EMPTY.ID,
        amount: 0,
      },
    ]
    const assertList = (
      result: PromiseType<ReturnType<typeof tokenFuncs.getAllowanceList>>,
      expected: typeof approvalParams,
    ) => {
      expected.map((v, i) => {
        assertEqualForEachField(result.approvalData[i], {
          spanderId: v.spender,
          spenderAccountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          allowanceAmount: v.amount,
        })
      })
    }

    before(async () => {
      await setupFixture()
    })

    describe('spenderが登録されていない状態', () => {
      before(async () => {
        await setupSpenderNotRegisteredTest()
      })

      it('spenderが登録されていない場合、空リストが取得できること', async () => {
        const offset = 0
        const limit = 100

        const result = await tokenFuncs.getAllowanceList({
          token,
          prams: [BASE.ACCOUNT.ACCOUNT0.ID, offset, limit],
        })

        assertEqualForEachField(result, { totalCount: 0, err: '' })
        assertList(result, [])
      })
    })
  })
})
