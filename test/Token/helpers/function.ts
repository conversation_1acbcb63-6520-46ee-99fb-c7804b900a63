/* eslint-disable @typescript-eslint/no-explicit-any */
import { BASE } from '@test/common/consts'
import { castReturnType, getDeadline } from '@test/common/utils'
import { TokenFunctionType } from './types'
import privateKey from '@/privateKey'

/**
 * tokenのイベントを呼ぶ関数を持つobject
 */
export const tokenFuncs: TokenFunctionType = {
  version: ({ token }) => {
    return castReturnType(token.version())
  },
  // getTotalSupply: (token: TokenInstance, ...params: Parameters<TokenInstance['getTotalSupply']>) => {
  //   return token.getTotalSupply(...params) as unknown as Promise<EventReturnType['Token']['GetTotalSupply']>;
  // },
  getAllowance: ({ token, prams }) => {
    return castReturnType(token.getAllowance(...prams))
  },
  getAllowanceList: ({ token, prams }) => {
    return castReturnType(token.getAllowanceList(...prams))
  },
  getBalanceList: ({ token, prams }) => {
    return castReturnType(token.getBalanceList(...prams))
  },
  hasToken: ({ token, prams }) => {
    return castReturnType(token.hasToken(...prams))
  },

  checkApprove: async ({ token, validatorId, ownerId, spenderId, amount, sigInfo, options = {} }) => {
    const { accountSignature, sig, deadline, eoaKey = BASE.EOA.VALID1 } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ??
      privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'bytes32', 'uint256'], [validatorId, ownerId, _deadline])
    const _accountSignature =
      accountSignature ??
      privateKey.sig(
        sigInfo.signer,
        ['bytes32', 'bytes32', 'uint256', 'uint256'],
        [ownerId, spenderId, amount, BASE.ACCOUNT_SIG_MSG.APPROVE],
      )[0]

    return castReturnType(
      token.checkApprove(validatorId, ownerId, spenderId, amount, _accountSignature, sigInfo.info, _deadline, _sig[0]),
    )
  },
  getToken: ({ token, prams }) => {
    return castReturnType(token.getToken(...prams))
  },
  setTokenEnabled: async ({ token, accounts, enabled, options = {} }) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.PROV1,
      providerId = BASE.PROV.PROV0.ID,
      tokenId = BASE.TOKEN.TOKEN1.ID,
    } = options
    const _deadline = await getDeadline(deadline)
    const signer = privateKey.key[eoaKey]
    const _sig =
      sig ??
      privateKey.sig(signer, ['bytes32', 'bytes32', 'bool', 'uint256'], [providerId, tokenId, enabled, _deadline])

    return castReturnType(
      token.connect(accounts[0]).setTokenEnabled(providerId, tokenId, enabled, BASE.TRACE_ID, _deadline, _sig[0]),
    )
  },
  approve: async ({ token, accounts, spenderId, amount, options = {} }) => {
    const { from, validatorId = BASE.VALID.VALID1.ID, ownerId = BASE.ACCOUNT.ACCOUNT1.ID } = options
    const _from = from ?? accounts[0]
    return castReturnType(token.connect(_from).approve(validatorId, ownerId, spenderId, amount, BASE.TRACE_ID))
  },
  mint: async ({ token, accounts, amount, options = {} }) => {
    const { from, issuerId = BASE.ISSUER.ISSUER0.ID, accountId = BASE.ACCOUNT.ACCOUNT1.ID } = options
    const _from = from ?? accounts[0]
    return castReturnType(token.connect(_from).mint(issuerId, accountId, amount, BASE.TRACE_ID))
  },
  burn: async ({ token, accounts, amount, options = {} }) => {
    const { issuerId = BASE.ISSUER.ISSUER0.ID, accountId = BASE.ACCOUNT.ACCOUNT1.ID } = options
    return castReturnType(token.connect(accounts[0]).burn(issuerId, accountId, amount, BASE.TRACE_ID))
  },
  burnCancel: async ({ token, accounts, amount, blockTimestamp, options = {} }) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ISSUER1,
      issuerId = BASE.ISSUER.ISSUER0.ID,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
    } = options
    const _deadline = await getDeadline(deadline)
    const signer = privateKey.key[eoaKey]
    const _sig =
      sig ??
      privateKey.sig(
        signer,
        ['bytes32', 'bytes32', 'uint256', 'uint256', 'uint256'],
        [issuerId, accountId, amount, blockTimestamp, _deadline],
      )

    return castReturnType(
      token
        .connect(accounts[0])
        .burnCancel(issuerId, accountId, amount, blockTimestamp, BASE.TRACE_ID, _deadline, _sig[0]),
    )
  },
  transferSingle: async ({ token, accounts, amount, miscValue1, miscValue2, options = {} }) => {
    const {
      sendAccountId = BASE.ACCOUNT.ACCOUNT0.ID,
      fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID,
      toAccountId = BASE.ACCOUNT.ACCOUNT1.ID,
    } = options
    return castReturnType(
      token
        .connect(accounts[0])
        .transferSingle(
          sendAccountId,
          fromAccountId,
          toAccountId,
          amount,
          miscValue1,
          miscValue2,
          BASE.MEMO,
          BASE.TRACE_ID,
        ),
    )
  },
  customTransfer: async ({ token, accounts, amount, miscValue1, miscValue2, options = {} }) => {
    const {
      sendAccountId = BASE.ACCOUNT.ACCOUNT0.ID,
      fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID,
      toAccountId = BASE.ACCOUNT.ACCOUNT1.ID,
    } = options
    return castReturnType(
      token.customTransfer(
        sendAccountId,
        fromAccountId,
        toAccountId,
        amount,
        miscValue1,
        miscValue2,
        BASE.MEMO,
        BASE.TRACE_ID,
      ),
    )
  },
  getTokenAll: async ({ token }) => {
    return castReturnType(token.getTokenAll())
  },
  setTokenAll: async ({ token, prams, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [BASE.SALTS.SET_TOKEN_ALL, _deadline])
    return castReturnType(token.setTokenAll(prams, _deadline, _sig[0]))
  },
}
