import { BASE, ERR } from '@test/common/consts'
import {
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { assertEqualForEachField } from '@test/common/utils'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { ibcTokenFuncs } from '@test/IBCToken/helpers/function'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { BusinessZoneAccountContractType } from '@test/BusinessZoneAccount/helpers/types'
import { contractFixture } from '@test/common/contractFixture'
import { expect } from 'chai'
import { ethers } from 'hardhat'
import { before } from 'mocha'

// Tokenコントラクトで利用
// balanceUpdateByIssueVoucherの雛形のみ作成
describe('balanceUpdateByIssueVoucher()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let businessZoneAccount: BusinessZoneAccountInstance
  let token: TokenInstance
  let contractManager: ContractManagerInstance
  let ibcToken: IBCTokenInstance
  let accounts: SignerWithAddress[]

  // balanceUpdateByIssueVoucherのテストは、token.redeemVoucherで呼び出されるため, ここでは呼び出し元の検証のみを行う

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, businessZoneAccount, token, contractManager, ibcToken } =
      await contractFixture<BusinessZoneAccountContractType>())
  }

  describe('正常系', () => {
    let ibcAddress
    before(async () => {
      await setupFixture()
      ibcAddress = await accounts[0]
    })

    describe('初期状態', () => {
      it('呼び出し元がTokenではない場合、エラーがスローされること', async () => {
        const result = businessZoneAccount
          .connect(accounts[0])
          .balanceUpdateByIssueVoucher(BASE.ZONE_ID.ID0, BASE.ACCOUNT.ACCOUNT1.ID, 100, BASE.TRACE_ID)
        await expect(result).to.be.revertedWith(ERR.TOKEN.NOT_TOKEN_CONTRACT)
      })
    })

    describe('balance update with event emited when issue voucher', () => {
      const setupBasicRoles = async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts })
      }

      const setupAccounts = async () => {
        for (const accountId of [
          BASE.ACCOUNT.ACCOUNT0.ID,
          BASE.ACCOUNT.ACCOUNT1.ID,
          BASE.ACCOUNT.ACCOUNT2.ID,
          BASE.ACCOUNT.ACCOUNT3.ID,
          BASE.ACCOUNT.ACCOUNT4.ID,
        ]) {
          await validatorFuncs.addAccount({ validator, accounts, options: { accountId } })
        }
        await tokenFuncs.mint({
          token,
          accounts,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
      }

      const setupIbcApps = async (ibcAddressString: string) => {
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager,
          accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.BALANCE_SYNC,
        })
      }

      const setupBusinessZone = async () => {
        await businessZoneAccount
          .connect(accounts[0])
          .syncBusinessZoneStatus(
            BASE.ZONE_ID.ID1,
            BASE.ZONE_NAME.NAME1,
            BASE.ACCOUNT.ACCOUNT0.ID,
            BASE.ACCOUNT.ACCOUNT0.NAME,
            BASE.STATUS.ACTIVE,
            BASE.TRACE_ID,
          )
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
      }

      before(async () => {
        const ibcAddressString = await ibcAddress.getAddress()
        await setupBasicRoles()
        await setupAccounts()
        await setupIbcApps(ibcAddressString)
        await setupBusinessZone()

        await ibcTokenFuncs.transferToEscrow({
          ibcToken,
          from: ibcAddress,
          amount: 300,
          options: {
            fromAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
      })

      it('redeem voucher success from ibcToken contract', async () => {
        const result = await ibcToken.connect(accounts[0]).syncBusinessZoneBalance({
          fromZoneId: BASE.ZONE_ID.ID1,
          fromAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          fromAccountName: BASE.ACCOUNT.ACCOUNT0.NAME,
          toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          toAccountName: BASE.ACCOUNT.ACCOUNT0.NAME,
          amount: 100,
          traceId: BASE.TRACE_ID,
        })
        assertEqualForEachField(result, {})
        // Impersonate token contract because none of the other contract call
        // TODO: This should be change when there's an new integrate of this function from other contract
        const ibcTokenAddress = await ibcToken.getAddress()
        await helpers.setBalance(ibcTokenAddress, 100n ** 18n)
        await helpers.impersonateAccount(ibcTokenAddress)
        const fakeIbcToken = await ethers.getSigner(ibcTokenAddress)
        // Call the function
        const tx = await businessZoneAccount
          .connect(fakeIbcToken)
          .balanceUpdateByIssueVoucher(BASE.ZONE_ID.ID1, BASE.ACCOUNT.ACCOUNT0.ID, 100, BASE.TRACE_ID)
        const expectParams = {
          zoneId: BASE.ZONE_ID.ID1,
          accountId: BASE.ACCOUNT.ACCOUNT0.ID,
          amount: 100,
          traceId: BASE.TRACE_ID,
        }
        await expect(tx)
          .to.emit(businessZoneAccount, 'BalanceUpdateByIssueVoucher')
          .withArgs(...Object.values(expectParams))
        await helpers.stopImpersonatingAccount(ibcTokenAddress)
      })

      it('redeem voucher success from token contract', async () => {
        // Impersonate token contract because none of the other contract call
        // TODO: This should be change when there's an new integrate of this function from other contract
        const tokenAddress = await token.getAddress()
        await helpers.setBalance(tokenAddress, 100n ** 18n)
        await helpers.impersonateAccount(tokenAddress)
        const fakeToken = await ethers.getSigner(tokenAddress)
        // Call the function
        const tx = await businessZoneAccount
          .connect(fakeToken)
          .balanceUpdateByIssueVoucher(BASE.ZONE_ID.ID1, BASE.ACCOUNT.ACCOUNT0.ID, 100, BASE.TRACE_ID)
        const expectParams = {
          zoneId: BASE.ZONE_ID.ID1,
          accountId: BASE.ACCOUNT.ACCOUNT0.ID,
          amount: 100,
          traceId: BASE.TRACE_ID,
        }
        await expect(tx)
          .to.emit(businessZoneAccount, 'BalanceUpdateByIssueVoucher')
          .withArgs(...Object.values(expectParams))
        await helpers.stopImpersonatingAccount(tokenAddress)
      })
    })
  })
})
