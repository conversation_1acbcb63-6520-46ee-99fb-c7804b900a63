import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccountInstance,
  BusinessZoneAccountInstance,
  ContractCallOption,
  ContractManagerInstance,
  EventReturnType,
  FinancialCheckInstance,
  FinancialZoneAccountInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  SetBizAccountsAllOption,
  SyncBusinessZoneStatusOption,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { ContractTransactionResponse } from 'ethers'

export type BusinessZoneAccountContractType = {
  accounts: SignerWithAddress[]
  provider: ProviderInstance
  issuer: IssuerInstance
  validator: ValidatorInstance
  account: AccountInstance
  financialZoneAccount: FinancialZoneAccountInstance
  businessZoneAccount: BusinessZoneAccountInstance
  token: TokenInstance
  financialCheck: FinancialCheckInstance
  contractManager: ContractManagerInstance
  ibcToken: IBCTokenInstance
}

type BusinessZoneAccountType = { businessZoneAccount: BusinessZoneAccountInstance }

export type FuncParamsType = {
  version: BusinessZoneAccountType
  hasAccountByZone: BusinessZoneAccountType & {
    params: Parameters<BusinessZoneAccountInstance['hasAccountByZone']>
  }
  getBusinessZoneAccount: BusinessZoneAccountType & {
    params: Parameters<BusinessZoneAccountInstance['getBusinessZoneAccount']>
  }
  isActivatedByZone: BusinessZoneAccountType & {
    params: Parameters<BusinessZoneAccountInstance['isActivatedByZone']>
  }
  accountIdExistenceByZoneId: BusinessZoneAccountType & {
    params: Parameters<BusinessZoneAccountInstance['accountIdExistenceByZoneId']>
  }
  forceBurnAllBalance: BusinessZoneAccountType & {
    params: Parameters<BusinessZoneAccountInstance['forceBurnAllBalance']>
  }
  getBizAccountsAll: BusinessZoneAccountType & {
    offset: number
  }
  setBizAccountsAll: BusinessZoneAccountType & {
    bizAccounts: any
    options: Partial<SetBizAccountsAllOption & ContractCallOption>
  }
  syncBusinessZoneStatus: BusinessZoneAccountType & {
    accounts: SignerWithAddress[]
    options?: Partial<SyncBusinessZoneStatusOption & ContractCallOption>
  }
}

type FuncReturnType = {
  version: string
  hasAccountByZone: EventReturnType['BusinessZoneAccount']['HasAccountByZone']
  getBusinessZoneAccount: EventReturnType['BusinessZoneAccount']['GetBusinessZoneAccount']
  isActivatedByZone: EventReturnType['BusinessZoneAccount']['IsActivatedByZone']
  accountIdExistenceByZoneId: EventReturnType['BusinessZoneAccount']['AccountIdExistenceByZoneId']
  forceBurnAllBalance: EventReturnType['BusinessZoneAccount']['ForceBurnAllBalance']
  getBizAccountsAll: EventReturnType['BusinessZoneAccount']['GetBizAccountsAll']
  setBizAccountsAll: ContractTransactionResponse
  syncBusinessZoneStatus: ContractTransactionResponse
}

export type FunctionType = {
  [K in keyof FuncParamsType]: (args: FuncParamsType[K]) => Promise<FuncReturnType[K]>
}
