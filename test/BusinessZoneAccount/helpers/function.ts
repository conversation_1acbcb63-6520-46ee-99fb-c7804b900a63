import { BASE } from '@test/common/consts'
import { castReturnType, getDeadline } from '@test/common/utils'
import { FunctionType } from './types'
import privateKey from '@/privateKey'

export const businessZoneAccountFuncs: FunctionType = {
  version: ({ businessZoneAccount }) => {
    return castReturnType(businessZoneAccount.version())
  },
  hasAccountByZone: ({ businessZoneAccount, params }) => {
    return castReturnType(businessZoneAccount.hasAccountByZone(...params))
  },
  getBusinessZoneAccount: ({ businessZoneAccount, params }) => {
    return castReturnType(businessZoneAccount.getBusinessZoneAccount(...params))
  },
  isActivatedByZone: ({ businessZoneAccount, params }) => {
    return castReturnType(businessZoneAccount.isActivatedByZone(...params))
  },
  accountIdExistenceByZoneId: ({ businessZoneAccount, params }) => {
    return castReturnType(businessZoneAccount.accountIdExistenceByZoneId(...params))
  },
  forceBurnAllBalance: ({ businessZoneAccount, params }) => {
    return castReturnType(businessZoneAccount.forceBurnAllBalance(...params))
  },
  getBizAccountsAll: async ({ businessZoneAccount, offset }) => {
    return castReturnType(businessZoneAccount.getBizAccountsAll(offset))
  },
  setBizAccountsAll: async ({ businessZoneAccount, bizAccounts, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.SET_BIZACCOUNTS_ALL } = options
    const _deadline = await getDeadline(deadline)
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return castReturnType(businessZoneAccount.setBizAccountsAll(bizAccounts, _deadline, _sig[0]))
  },
  syncBusinessZoneStatus: ({ businessZoneAccount, accounts, options = {} }) => {
    const {
      zoneId = BASE.ZONE_ID.ID0,
      zoneName = BASE.ZONE_NAME.NAME0,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
      accountName = BASE.ACCOUNT.ACCOUNT1.NAME,
      accountStatus = BASE.STATUS.APPLYING,
    } = options

    return castReturnType(
      businessZoneAccount
        .connect(accounts[0])
        .syncBusinessZoneStatus(zoneId, zoneName, accountId, accountName, accountStatus, BASE.TRACE_ID),
    )
  },
}
