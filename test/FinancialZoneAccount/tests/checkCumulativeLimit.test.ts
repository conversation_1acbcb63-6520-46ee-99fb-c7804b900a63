import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { FinancialZoneAccountInstance, IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import { toBytes32 } from '@test/common/utils'
import { financialZoneAccountFuncs } from '@test/FinancialZoneAccount/helpers/function'
import { FinancialZoneAccountContractType } from '@test/FinancialZoneAccount/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('checkCumulativeLimit', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let financialZoneAccount: FinancialZoneAccountInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, financialZoneAccount } =
      await contractFixture<FinancialZoneAccountContractType>())
  }

  const setupBasicRoles = async () => {
    await providerFuncs.addProvider({ provider, accounts })
    await providerFuncs.addProviderRole({ provider, accounts })
    await providerFuncs.addToken({ provider, accounts })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await validatorFuncs.addValidatorRole({ validator, accounts })
    await validatorFuncs.addAccount({ validator, accounts })
  }

  const setupAccountLimits = async () => {
    const limitValues = {
      mint: 200,
      burn: 200,
      charge: 200,
      discharge: 200,
      transfer: 200,
      cumulative: {
        total: 500,
        mint: 300,
        burn: 300,
        charge: 300,
        discharge: 300,
        transfer: 300,
      },
    }
    await financialZoneAccountFuncs.addAccountLimit({
      finAccount: financialZoneAccount,
      params: [BASE.ACCOUNT.ACCOUNT1.ID, limitValues, BASE.TRACE_ID],
    })
  }

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('exceed limit', () => {
      before(async () => {
        await setupBasicRoles()
        await setupAccountLimits()
      })

      it('should return false and error when exceed mint limit', async () => {
        const result = await financialZoneAccount.checkTransactionLimits(
          BASE.ACCOUNT.ACCOUNT1.ID,
          201, // Exceeds mint limit of 200
          toBytes32(`checkMint`),
        )
        assert.equal(result.err, ERR.FINACCOUNT.EXCEEDED_MINT_LIMIT)
      })

      it('should return false and error when exceed daily mint cumulative limit', async () => {
        // 200をmintする
        await financialZoneAccountFuncs.syncMint({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, 200, BASE.TRACE_ID],
        })
        const result = await financialZoneAccount.checkTransactionLimits(
          BASE.ACCOUNT.ACCOUNT1.ID,
          101, // Exceeds daily mint limit of 301
          toBytes32(`checkMint`),
        )
        assert.equal(result.err, ERR.FINACCOUNT.EXCEEDED_DAILY_MINT_LIMIT)
      })

      it('should return false and error when exceed daily limit', async () => {
        // 200をburnする
        await financialZoneAccountFuncs.syncBurn({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, 200, BASE.TRACE_ID],
        })
        const result = await financialZoneAccount.checkTransactionLimits(
          BASE.ACCOUNT.ACCOUNT1.ID,
          101, // Exceeds daily limit of 501
          toBytes32(`checkTransfer`),
        )
        assert.equal(result.err, ERR.FINACCOUNT.EXCEEDED_DAILY_LIMIT)
      })

      it('should return false and error when exceed burn limit', async () => {
        const result = await financialZoneAccount.checkTransactionLimits(
          BASE.ACCOUNT.ACCOUNT1.ID,
          201, // Exceeds burn limit of 200
          toBytes32(`checkBurn`),
        )
        assert.equal(result.err, ERR.FINACCOUNT.EXCEEDED_BURN_LIMIT)
      })

      it('should return false and error when exceed daily burn cumulative limit', async () => {
        // 200をburnする
        await financialZoneAccountFuncs.syncBurn({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, 200, BASE.TRACE_ID],
        })
        const result = await financialZoneAccount.checkTransactionLimits(
          BASE.ACCOUNT.ACCOUNT1.ID,
          101, // Exceeds daily burn limit of 301
          toBytes32(`checkBurn`),
        )
        assert.equal(result.err, ERR.FINACCOUNT.EXCEEDED_DAILY_BURN_LIMIT)
      })

      it('should return false and error when exceed charge limit', async () => {
        const result = await financialZoneAccount.checkTransactionLimits(
          BASE.ACCOUNT.ACCOUNT1.ID,
          201, // Exceeds charge limit of 200
          toBytes32(`checkCharge`),
        )
        assert.equal(result.err, ERR.FINACCOUNT.EXCEEDED_CHARGE_LIMIT)
      })

      it('should return false and error when exceed daily charge cumulative limit', async () => {
        // 200をchargeする
        await financialZoneAccountFuncs.syncCharge({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, 200, BASE.TRACE_ID],
        })
        const result = await financialZoneAccount.checkTransactionLimits(
          BASE.ACCOUNT.ACCOUNT1.ID,
          101, // Exceeds daily charge limit of 301
          toBytes32(`checkCharge`),
        )
        assert.equal(result.err, ERR.FINACCOUNT.EXCEEDED_DAILY_CHARGE_LIMIT)
      })

      it('should return false and error when exceed discharge limit', async () => {
        const result = await financialZoneAccount.checkTransactionLimits(
          BASE.ACCOUNT.ACCOUNT1.ID,
          201, // Exceeds discharge limit of 200
          toBytes32(`checkDischarge`),
        )
        assert.equal(result.err, ERR.FINACCOUNT.EXCEEDED_DISCHARGE_LIMIT)
      })

      it('should return false and error when exceed daily discharge cumulative limit', async () => {
        // 200をchargeする
        await financialZoneAccountFuncs.syncDischarge({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, 200, BASE.TRACE_ID],
        })
        const result = await financialZoneAccount.checkTransactionLimits(
          BASE.ACCOUNT.ACCOUNT1.ID,
          101, // Exceeds daily discharge limit of 301
          toBytes32(`checkDischarge`),
        )
        assert.equal(result.err, ERR.FINACCOUNT.EXCEEDED_DAILY_DISCHARGE_LIMIT)
      })

      it('should return false and error when exceed transfer limit', async () => {
        const result = await financialZoneAccount.checkTransactionLimits(
          BASE.ACCOUNT.ACCOUNT1.ID,
          201, // Exceeds transfer limit of 200
          toBytes32(`checkTransfer`),
        )
        assert.equal(result.err, ERR.FINACCOUNT.EXCEEDED_TRANSFER_LIMIT)
      })

      it('should return false and error when exceed daily transfer cumulative limit', async () => {
        // 200をtransferする
        await financialZoneAccountFuncs.syncTransfer({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, 200, BASE.TRACE_ID],
        })
        const result = await financialZoneAccount.checkTransactionLimits(
          BASE.ACCOUNT.ACCOUNT1.ID,
          101, // Exceeds daily transfer limit of 301
          toBytes32(`checkTransfer`),
        )
        assert.equal(result.err, ERR.FINACCOUNT.EXCEEDED_DAILY_TRANSFER_LIMIT)
      })
    })
  })
})
