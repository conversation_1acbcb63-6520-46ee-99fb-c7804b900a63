import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { FinancialZoneAccountInstance, IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import { financialZoneAccountFuncs } from '@test/FinancialZoneAccount/helpers/function'
import { FinancialZoneAccountContractType } from '@test/FinancialZoneAccount/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('addAccountLimit()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let financialZoneAccount: FinancialZoneAccountInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, financialZoneAccount } =
      await contractFixture<FinancialZoneAccountContractType>())
  }

  const setupBasicRoles = async () => {
    await providerFuncs.addProvider({ provider, accounts })
    await providerFuncs.addProviderRole({ provider, accounts })
    await providerFuncs.addToken({ provider, accounts })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await validatorFuncs.addValidatorRole({ validator, accounts })
  }

  const setupAccount = async () => {
    await validatorFuncs.addAccount({ validator, accounts })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('provider, providerRole, issuer, validator, account, tokenが登録されている状態', () => {
      before(async () => {
        await setupBasicRoles()
        await setupAccount()
      })
      it('アカウント限度額追加できること', async () => {
        const limitValues = {
          mint: 1300,
          burn: 1400,
          charge: 1200,
          discharge: 1600,
          transfer: 1100,
          cumulative: {
            total: 5000,
            mint: 1000,
            burn: 1000,
            charge: 1000,
            discharge: 1000,
            transfer: 1000,
          },
        }
        await financialZoneAccountFuncs.addAccountLimit({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, limitValues, BASE.TRACE_ID],
        })
        const result = await validatorFuncs.getAccount({
          validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        assert.equal(Number(result.accountData.transferLimit), limitValues.transfer)
        assert.equal(Number(result.accountData.chargeLimit), limitValues.charge)
        assert.equal(Number(result.accountData.mintLimit), limitValues.mint)
        assert.equal(Number(result.accountData.burnLimit), limitValues.burn)
        assert.equal(Number(result.accountData.cumulativeLimit), limitValues.cumulative.total)
      })
    })
  })
  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('共通領域Accountが登録されていない状態', () => {
      before(async () => {
        await setupBasicRoles()
      })
    })
  })
})
