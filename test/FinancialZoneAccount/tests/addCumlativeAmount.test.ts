import '@nomicfoundation/hardhat-chai-matchers'
import { anyValue } from '@nomicfoundation/hardhat-chai-matchers/withArgs'
import '@nomicfoundation/hardhat-ethers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { FinancialZoneAccountInstance, TokenInstance } from '@test/common/types'
import { FinancialZoneAccountContractType } from '@test/FinancialZoneAccount/helpers/types'
import { expect } from 'chai'
import { ethers } from 'hardhat'
import { before } from 'mocha'

describe('addCumlativeAmount()', () => {
  let financialZoneAccount: FinancialZoneAccountInstance
  let token: TokenInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, financialZoneAccount, token } = await contractFixture<FinancialZoneAccountContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('cumulativeAmount run normally', () => {
      it('should run normally when accountLimitDataMapping[key].cumulativeDate == currentDay', async () => {
        // Hack: Fake call from Token contract, because there is no implementation of addCumlativeAmount in Token contract
        // Unreachable: No implementation of addCumlativeAmount in Token contract, this function is never call
        // #TODO: This must be changed after addCumlativeAmount function is implemented in Token contract
        await helpers.impersonateAccount(await token.getAddress())
        await helpers.setBalance(await token.getAddress(), 100n ** 18n)
        const fakeToken = await ethers.getSigner(await token.getAddress())
        await financialZoneAccount.connect(fakeToken).addCumlativeAmount(BASE.ACCOUNT.ACCOUNT0.ID, 100, BASE.TRACE_ID)
        const result = await financialZoneAccount
          .connect(fakeToken)
          .addCumlativeAmount(BASE.ACCOUNT.ACCOUNT0.ID, 100, BASE.TRACE_ID)

        const expectParams = {
          accountId: BASE.ACCOUNT.ACCOUNT0.ID,
          amount: 100,
          traceId: BASE.TRACE_ID,
        }
        await expect(result)
          .to.emit(financialZoneAccount, 'AddCumulativeAmount')
          .withArgs(expectParams.accountId, expectParams.amount, anyValue, anyValue, expectParams.traceId)
      })
    })
  })

  // addCumlativeAmount のテストは token から呼ばれるため token のテストで実施する, ここでは呼び出し元検証のみ行う
  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('呼び出し元がTokenではない場合、エラーがスローされること', async () => {
        const result = financialZoneAccount
          .connect(accounts[1])
          .addCumlativeAmount(BASE.ACCOUNT.ACCOUNT0.ID, 100, BASE.TRACE_ID)
        await expect(result).to.be.revertedWith(ERR.TOKEN.NOT_TOKEN_CONTRACT)
      })
    })
  })
})
