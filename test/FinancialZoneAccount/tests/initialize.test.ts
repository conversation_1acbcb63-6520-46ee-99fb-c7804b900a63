import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { FinancialZoneAccountInstance } from '@test/common/types'
import { FinancialZoneAccountContractType } from '@test/FinancialZoneAccount/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('initialize()', () => {
  let financialZoneAccount: FinancialZoneAccountInstance

  const setupFixture = async () => {
    ;({ financialZoneAccount } = await contractFixture<FinancialZoneAccountContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    it('should revert when initialized', async () => {
      const result = financialZoneAccount.initialize(await financialZoneAccount.getAddress())
      await expect(result).to.be.revertedWith(ERR.INITIALIZER.ALREADY_INIT)
    })
  })
})
