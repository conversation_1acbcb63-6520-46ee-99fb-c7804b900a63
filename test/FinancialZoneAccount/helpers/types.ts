import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccountInstance,
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  EventReturnType,
  FinancialZoneAccountInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { ContractTransactionResponse } from 'ethers'

export type FinancialZoneAccountContractType = {
  accounts: SignerWithAddress[]
  provider: ProviderInstance
  issuer: IssuerInstance
  validator: ValidatorInstance
  account: AccountInstance
  financialZoneAccount: FinancialZoneAccountInstance
  token: TokenInstance
  contractManager: ContractManagerInstance
  businessZoneAccount: BusinessZoneAccountInstance
}

type FinancialZoneAccountType = { finAccount: FinancialZoneAccountInstance }

export type FuncParamsType = {
  version: FinancialZoneAccountType
  addAccountLimit: FinancialZoneAccountType & {
    params: Parameters<FinancialZoneAccountInstance['addAccountLimit']>
  }
  modAccountLimit: FinancialZoneAccountType & {
    params: Parameters<FinancialZoneAccountInstance['modAccountLimit']>
  }
  syncCumulativeReset: FinancialZoneAccountType & {
    params: Parameters<FinancialZoneAccountInstance['syncCumulativeReset']>
  }
  addCumulativeAmount: FinancialZoneAccountType & {
    params: Parameters<FinancialZoneAccountInstance['addCumlativeAmount']>
  }
  subtractCumulativeAmount: FinancialZoneAccountType & {
    params: Parameters<FinancialZoneAccountInstance['subtractCumulativeAmount']>
  }
  syncMint: FinancialZoneAccountType & {
    params: Parameters<FinancialZoneAccountInstance['syncMint']>
  }
  syncBurn: FinancialZoneAccountType & {
    params: Parameters<FinancialZoneAccountInstance['syncBurn']>
  }
  syncCharge: FinancialZoneAccountType & {
    params: Parameters<FinancialZoneAccountInstance['syncCharge']>
  }

  syncDischarge: FinancialZoneAccountType & {
    params: Parameters<FinancialZoneAccountInstance['syncDischarge']>
  }
  syncTransfer: FinancialZoneAccountType & {
    params: Parameters<FinancialZoneAccountInstance['syncTransfer']>
  }
  hasAccount: FinancialZoneAccountType & {
    params: Parameters<FinancialZoneAccountInstance['hasAccount']>
  }
  getAccountLimitData: FinancialZoneAccountType & {
    params: Parameters<FinancialZoneAccountInstance['getAccountLimitData']>
  }
  checkTransfer: FinancialZoneAccountType & {
    params: Parameters<FinancialZoneAccountInstance['checkTransfer']>
  }
}

type FuncReturnType = {
  version: string
  addAccountLimit: ContractTransactionResponse
  modAccountLimit: ContractTransactionResponse
  syncCumulativeReset: ContractTransactionResponse
  addCumulativeAmount: ContractTransactionResponse
  subtractCumulativeAmount: ContractTransactionResponse
  syncMint: ContractTransactionResponse
  syncBurn: ContractTransactionResponse
  syncCharge: ContractTransactionResponse
  syncDischarge: ContractTransactionResponse
  syncTransfer: ContractTransactionResponse
  hasAccount: EventReturnType['FinancialZoneAccount']['HasAccount']
  getAccountLimitData: ContractTransactionResponse
  checkTransfer: EventReturnType['FinancialZoneAccount']['CheckTransfer']
}

export type FinancialZoneAccountFunctionType = {
  [K in keyof FuncParamsType]: (args: FuncParamsType[K]) => Promise<FuncReturnType[K]>
}
