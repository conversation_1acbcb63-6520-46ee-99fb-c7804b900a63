import { castReturnType } from '@test/common/utils'
import { FinancialZoneAccountFunctionType } from './types'

/**
 * financialZoneAccountのイベントを呼ぶ関数を持つobject
 */
export const financialZoneAccountFuncs: FinancialZoneAccountFunctionType = {
  version: ({ finAccount }) => {
    return castReturnType(finAccount.version())
  },

  addAccountLimit: ({ finAccount, params }) => {
    return castReturnType(finAccount.addAccountLimit(...params))
  },

  modAccountLimit: ({ finAccount, params }) => {
    return castReturnType(finAccount.modAccountLimit(...params))
  },

  syncCumulativeReset: ({ finAccount, params }) => {
    return castReturnType(finAccount.syncCumulativeReset(...params))
  },

  addCumulativeAmount: ({ finAccount, params }) => {
    return castReturnType(finAccount.addCumlativeAmount(...params))
  },

  subtractCumulativeAmount: ({ finAccount, params }) => {
    return castReturnType(finAccount.subtractCumulativeAmount(...params))
  },

  syncMint: ({ finAccount, params }) => {
    return castReturnType(finAccount.syncMint(...params))
  },

  syncBurn: ({ finAccount, params }) => {
    return castReturnType(finAccount.syncBurn(...params))
  },

  syncCharge: ({ finAccount, params }) => {
    return castReturnType(finAccount.syncCharge(...params))
  },

  syncDischarge: ({ finAccount, params }) => {
    return castReturnType(finAccount.syncDischarge(...params))
  },

  syncTransfer: ({ finAccount, params }) => {
    return castReturnType(finAccount.syncTransfer(...params))
  },

  hasAccount: ({ finAccount, params }) => {
    return castReturnType(finAccount.hasAccount(...params))
  },

  getAccountLimitData: ({ finAccount, params }) => {
    return castReturnType(finAccount.getAccountLimitData(...params))
  },

  checkTransfer: ({ finAccount, params }) => {
    return castReturnType(finAccount.checkTransfer(...params))
  },
}
