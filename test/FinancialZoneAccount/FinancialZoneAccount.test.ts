import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'

describe('FinancialZoneAccount', () => {
  require('./tests/version.test')
  require('./tests/initialize.test')
  require('./tests/addAccountLimit.test')
  require('./tests/modAccountLimit.test')
  require('./tests/cumulativeReset.test')
  require('./tests/syncCumulativeReset.test')
  require('./tests/addCumlativeAmount.test')
  require('./tests/subtractCumulativeAmount.test')
  require('./tests/syncMint.test')
  require('./tests/syncBurn.test')
  require('./tests/syncCharge.test')
  require('./tests/syncDischarge.test')
  require('./tests/syncTransfer.test')
  require('./tests/hasAccount.test')
  require('./tests/getAccountLimitData.test')
  require('./tests/getJSTDay.test')
  require('./tests/convertJSTDay.test')
  require('./tests/checkCharge.test')
  require('./tests/checkDischarge.test')
  require('./tests/checkTransfer.test')
  require('./tests/checkCumulativeLimit.test')
})
