import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { businessZoneAccountFuncs } from '@test/BusinessZoneAccount/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  FinancialCheckInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { assertEqualForEachField, getDeadline } from '@test/common/utils'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { financialCheckFuncs } from '@test/FinancialCheck/helpers/function'
import { FinancialCheckContractType } from '@test/FinancialCheck/helpers/types'
import { ibcTokenFuncs } from '@test/IBCToken/helpers/function'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'

describe('checkExchange()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let token: TokenInstance
  let financialCheck: FinancialCheckInstance
  let contractManager: ContractManagerInstance
  let ibcToken: IBCTokenInstance
  let businessZoneAccount: BusinessZoneAccountInstance
  let accounts: SignerWithAddress[]
  let ibcAddress: SignerWithAddress
  let ibcAddressString: string
  let params: any[]

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, token, financialCheck, contractManager, ibcToken, businessZoneAccount } =
      await contractFixture<FinancialCheckContractType>())
  }

  const setupIbcAddress = async () => {
    ibcAddress = await accounts[0]
    ibcAddressString = await ibcAddress.getAddress()
  }

  const setupAccountParams = async (count = 4) => {
    const pramsAccounts = Object.values(BASE.ACCOUNT)
      .filter((v) => typeof v === 'object')
      .slice(0, count)
    params = pramsAccounts.map((v, i) => {
      return {
        accountId: v.ID,
        accountName: v.NAME,
        accountEoa: accounts[i].getAddress(),
        accountApprovalAll: [{ spenderId: pramsAccounts[i].ID, amount: 100 }],
      }
    })
  }

  const setupBasicRoles = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({
      provider,
      accounts,
      options: {
        providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
      },
    })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await validatorFuncs.addValidatorRole({
      validator,
      accounts,
      options: {
        validatorEoa: BASE.VALID.EOA_ADDRESS,
      },
    })
    await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
  }

  const setupAccounts = async () => {
    const deadline = await getDeadline()
    for (const v of params) {
      await validatorFuncs.addAccount({
        validator,
        accounts,
        options: {
          validatorId: BASE.VALID.VALID0.ID,
          accountId: v.accountId,
          accountName: v.accountName,
        },
      })
      await issuerFuncs.addAccountRole({
        issuer,
        accounts,
        options: {
          issuerId: BASE.ISSUER.ISSUER0.ID,
          accountId: v.accountId,
          deadline: deadline + 60,
        },
      })
    }
  }

  const setupTokenBalance = async () => {
    await tokenFuncs.mint({
      token,
      accounts,
      amount: 1000,
      options: {
        accountId: BASE.ACCOUNT.ACCOUNT0.ID,
      },
    })
    await tokenFuncs.mint({
      token,
      accounts,
      amount: 1000,
      options: {
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
      },
    })
  }

  const setupIbcApps = async () => {
    await contractManagerFuncs.setIbcApp({
      contractManager,
      accounts,
      ibcAddress: ibcAddressString,
      ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
    })
    await contractManagerFuncs.setIbcApp({
      contractManager,
      accounts,
      ibcAddress: ibcAddressString,
      ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
    })
  }

  const setupBusinessZone = async () => {
    await businessZoneAccountFuncs.syncBusinessZoneStatus({
      businessZoneAccount,
      accounts,
      options: {
        accountId: BASE.ACCOUNT.ACCOUNT0.ID,
        zoneId: BASE.ZONE_ID.ID1,
      },
    })
    await businessZoneAccountFuncs.syncBusinessZoneStatus({
      businessZoneAccount,
      accounts,
      options: {
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
        zoneId: BASE.ZONE_ID.ID1,
      },
    })
    await validatorFuncs.setActiveBusinessAccountWithZone({
      validator,
      accounts,
      options: {
        validatorId: BASE.VALID.VALID0.ID,
        zoneId: BASE.ZONE_ID.ID1,
        accountId: BASE.ACCOUNT.ACCOUNT0.ID,
      },
    })
    await validatorFuncs.setActiveBusinessAccountWithZone({
      validator,
      accounts,
      options: {
        validatorId: BASE.VALID.VALID0.ID,
        zoneId: BASE.ZONE_ID.ID1,
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
      },
    })
  }

  const setupEscrowBalance = async () => {
    await ibcTokenFuncs.transferToEscrow({
      ibcToken,
      from: ibcAddress,
      amount: 300,
      options: {
        fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
        toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
      },
    })
    await ibcTokenFuncs.transferToEscrow({
      ibcToken,
      from: ibcAddress,
      amount: 300,
      options: {
        fromAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
        toAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
      },
    })
  }

  const setupNormalData = async () => {
    await setupIbcAddress()
    await setupAccountParams()
    await setupBasicRoles()
    await setupAccounts()
    await setupTokenBalance()
    await setupIbcApps()
    await setupBusinessZone()
    await setupEscrowBalance()
  }

  const setupNotNormalData = async () => {
    await setupIbcAddress()
    await setupAccountParams(10)
    await setupBasicRoles()
    await setupAccounts()
    await setupTokenBalance()
    await setupIbcApps()
    await setupBusinessZone()
  }
  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('アカウントが登録されている状態', () => {
      before(async () => {
        await setupNormalData()
      })

      it('チャージ可能かどうか確認できること', async () => {
        const result = await financialCheckFuncs.checkExchange({
          financialCheck,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromZoneId: BASE.ZONE_ID.ID0,
          toZoneId: BASE.ZONE_ID.ID1,
          amount: 100,
        })
        const expected = {
          success: true,
          err: '',
        }
        assertEqualForEachField(result, expected)
      })

      it('Bizからのチャージ可能かどうか確認できること', async () => {
        const result = await financialCheckFuncs.checkExchange({
          financialCheck,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromZoneId: BASE.ZONE_ID.ID1,
          toZoneId: BASE.ZONE_ID.ID0,
          amount: 100,
        })
        const expected = {
          success: true,
          err: '',
        }
        assertEqualForEachField(result, expected)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('アカウントが登録されている状態', () => {
      before(async () => {
        await setupNotNormalData()
      })

      it('fromZoneIdとtoZoneIdが同一である場合、エラーが返却されること', async () => {
        const result = await financialCheckFuncs.checkExchange({
          financialCheck,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromZoneId: BASE.ZONE_ID.ID0,
          toZoneId: BASE.ZONE_ID.ID0,
          amount: 100,
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_INVALID_VAL,
        }
        assertEqualForEachField(result, expected)
      })

      it('限度額を超えている場合、エラーが返却されること', async () => {
        const result = await financialCheckFuncs.checkExchange({
          financialCheck,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromZoneId: BASE.ZONE_ID.ID0,
          toZoneId: BASE.ZONE_ID.ID1,
          amount: 1000000,
        })
        const expected = {
          success: false,
          err: ERR.FINACCOUNT.EXCEEDED_CHARGE_LIMIT,
        }
        assertEqualForEachField(result, expected)
      })
    })

    describe('account is not registered', () => {
      it('should return false and err message when accountId is not registered', async () => {
        const result = await financialCheckFuncs.checkExchange({
          financialCheck,
          accountId: BASE.ACCOUNT.ACCOUNT11.ID,
          fromZoneId: BASE.ZONE_ID.ID0,
          toZoneId: BASE.ZONE_ID.ID1,
          amount: 100,
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_STATUS_INVALID,
        }
        assertEqualForEachField(result, expected)
      })

      it('should return false and err message when accountId is not valid', async () => {
        const result = await financialCheckFuncs.checkExchange({
          financialCheck,
          accountId: BASE.ACCOUNT.EMPTY.ID,
          fromZoneId: BASE.ZONE_ID.ID0,
          toZoneId: BASE.ZONE_ID.ID1,
          amount: 100,
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_STATUS_INVALID,
        }
        assertEqualForEachField(result, expected)
      })
    })

    describe('account balance and status check', () => {
      before(async () => {
        await setupIbcAddress()
        await setupAccountParams(10)
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT5.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT5.ID,
            zoneId: BASE.ZONE_ID.ID0,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator,
          accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT5.ID,
          },
        })
      })

      it('should return false and err message when accountId is not enough balance on Financial Zone', async () => {
        const result = await financialCheckFuncs.checkExchange({
          financialCheck,
          accountId: BASE.ACCOUNT.ACCOUNT5.ID,
          fromZoneId: BASE.ZONE_ID.ID1,
          toZoneId: BASE.ZONE_ID.ID0,
          amount: 100,
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.BALANCE_NOT_ENOUGH,
        }
        assertEqualForEachField(result, expected)
      })

      it('should return false and err message when accountId is disabled on Financial Zone', async () => {
        await validatorFuncs.setBizZoneTerminated({
          validator,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT5.ID,
          },
        })

        const result = await financialCheckFuncs.checkExchange({
          financialCheck,
          accountId: BASE.ACCOUNT.ACCOUNT5.ID,
          fromZoneId: BASE.ZONE_ID.ID1,
          toZoneId: BASE.ZONE_ID.ID0,
          amount: 100,
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_STATUS_INVALID,
        }
        assertEqualForEachField(result, expected)
      })

      it('should return false and err message when accountId is disabled on toZoneId on other Zone ', async () => {
        const result = await financialCheckFuncs.checkExchange({
          financialCheck,
          accountId: BASE.ACCOUNT.ACCOUNT5.ID,
          fromZoneId: BASE.ZONE_ID.ID0,
          toZoneId: BASE.ZONE_ID.ID1,
          amount: 100,
        })
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_STATUS_INVALID,
        }
        assertEqualForEachField(result, expected)
      })
    })
  })
})
