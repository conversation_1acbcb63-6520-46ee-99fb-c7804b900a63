import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { FinancialCheckInstance, IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import { assertEqualForEachField, getDeadline } from '@test/common/utils'
import { financialCheckFuncs } from '@test/FinancialCheck/helpers/function'
import { FinancialCheckContractType } from '@test/FinancialCheck/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { before } from 'mocha'

describe('getIssuerWithZone()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let financialCheck: FinancialCheckInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ provider, issuer, validator, financialCheck, accounts } = await contractFixture<FinancialCheckContractType>())
  }

  const setupAccountParams = async () => {
    const pramsAccounts = Object.values(BASE.ACCOUNT)
      .filter((v) => typeof v === 'object')
      .slice(0, 4)
    return pramsAccounts.map((v, i) => {
      return {
        accountId: v.ID,
        accountName: v.NAME,
        accountEoa: accounts[i].getAddress(),
        accountApprovalAll: [{ spenderId: pramsAccounts[i].ID, amount: 100 }],
      }
    })
  }

  const setupBasicRoles = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({
      provider,
      accounts,
      options: {
        providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
      },
    })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await validatorFuncs.addValidatorRole({
      validator,
      accounts,
      options: {
        validatorEoa: BASE.VALID.EOA_ADDRESS,
      },
    })
    await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
  }

  const setupAccounts = async (params: any[]) => {
    const deadline = await getDeadline()
    for (const v of params) {
      await validatorFuncs.addAccount({
        validator,
        accounts,
        options: {
          validatorId: BASE.VALID.VALID0.ID,
          accountId: v.accountId,
          accountName: v.accountName,
        },
      })
      await issuerFuncs.addAccountRole({
        issuer,
        accounts,
        options: {
          issuerId: BASE.ISSUER.ISSUER0.ID,
          accountId: v.accountId,
          deadline: deadline + 60,
        },
      })
    }
  }

  const setupAdditionalIssuers = async () => {
    await issuerFuncs.addIssuer({
      issuer,
      accounts,
      options: {
        issuerId: BASE.ISSUER.ISSUER1.ID,
        name: BASE.ISSUER.ISSUER1.NAME,
        bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
      },
    })
    await issuerFuncs.addIssuer({
      issuer,
      accounts,
      options: {
        issuerId: BASE.ISSUER.ISSUER2.ID,
        name: BASE.ISSUER.ISSUER2.NAME,
        bankCode: BASE.ISSUER.ISSUER2.BANK_CODE,
      },
    })
    await issuerFuncs.addIssuer({
      issuer,
      accounts,
      options: {
        issuerId: BASE.ISSUER.ISSUER3.ID,
        name: BASE.ISSUER.ISSUER3.NAME,
        bankCode: BASE.ISSUER.ISSUER3.BANK_CODE,
      },
    })
    await issuerFuncs.addIssuer({
      issuer,
      accounts,
      options: {
        issuerId: BASE.ISSUER.ISSUER4.ID,
        name: BASE.ISSUER.ISSUER4.NAME,
        bankCode: BASE.ISSUER.ISSUER4.BANK_CODE,
      },
    })
  }

  const setupBusinessZones = async () => {
    await providerFuncs.addBizZone({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID1,
        zoneName: BASE.ZONE_NAME.NAME1,
      },
    })
    await providerFuncs.addBizZone({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID2,
        zoneName: BASE.ZONE_NAME.NAME2,
      },
    })
  }

  const setupIssuerZoneMapping = async () => {
    // Zone ID1 mappings
    await issuerFuncs.addBizZoneToIssuer({
      issuer,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID1,
        issuerId: BASE.ISSUER.ISSUER0.ID,
      },
    })
    await issuerFuncs.addBizZoneToIssuer({
      issuer,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID1,
        issuerId: BASE.ISSUER.ISSUER1.ID,
      },
    })
    // Zone ID2 mappings
    await issuerFuncs.addBizZoneToIssuer({
      issuer,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID2,
        issuerId: BASE.ISSUER.ISSUER0.ID,
      },
    })
    await issuerFuncs.addBizZoneToIssuer({
      issuer,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID2,
        issuerId: BASE.ISSUER.ISSUER1.ID,
      },
    })
    await issuerFuncs.addBizZoneToIssuer({
      issuer,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID2,
        issuerId: BASE.ISSUER.ISSUER2.ID,
      },
    })
    await issuerFuncs.addBizZoneToIssuer({
      issuer,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID2,
        issuerId: BASE.ISSUER.ISSUER3.ID,
      },
    })
    await issuerFuncs.addBizZoneToIssuer({
      issuer,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID2,
        issuerId: BASE.ISSUER.ISSUER4.ID,
      },
    })
  }

  const setupBasicIssuerZoneMapping = async () => {
    await issuerFuncs.addBizZoneToIssuer({
      issuer,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID1,
        issuerId: BASE.ISSUER.ISSUER0.ID,
      },
    })
    await issuerFuncs.addBizZoneToIssuer({
      issuer,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID1,
        issuerId: BASE.ISSUER.ISSUER1.ID,
      },
    })
  }
  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('アカウントが登録されている状態', () => {
      let params

      before(async () => {
        params = await setupAccountParams()
        await setupBasicRoles()
        await setupAdditionalIssuers()
        await setupAccounts(params)
        await setupBusinessZones()
        await setupIssuerZoneMapping()
      })

      it('ゾーンに紐付くイシュア情報を取得できること', async () => {
        const result = await financialCheckFuncs.getIssuerWithZone({
          financialCheck,
          params: [BASE.ZONE_ID.ID1, 0, 100],
        })
        const expected = {
          issuers: [
            {
              issuerId: BASE.ISSUER.ISSUER0.ID,
              name: BASE.ISSUER.ISSUER0.NAME,
              bankCode: BASE.ISSUER.ISSUER0.BANK_CODE,
            },
            {
              issuerId: BASE.ISSUER.ISSUER1.ID,
              name: BASE.ISSUER.ISSUER1.NAME,
              bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
            },
          ],
          totalCount: 2,
          err: '',
        }
        result.issuers.forEach((v, i) => {
          assertEqualForEachField(v, {
            issuerId: expected.issuers[i].issuerId,
            name: expected.issuers[i].name,
            bankCode: String(expected.issuers[i].bankCode),
          })
        })
        await expect(result.totalCount).to.be.equal(expected.totalCount)
        await expect(result.err).to.be.equal(expected.err)
      })

      it('オフセットを指定した際に、ゾーンに紐付くイシュア情報を取得できること', async () => {
        const result = await financialCheckFuncs.getIssuerWithZone({
          financialCheck,
          params: [BASE.ZONE_ID.ID2, 2, 100],
        })
        const expected = {
          issuers: [
            {
              issuerId: BASE.ISSUER.ISSUER2.ID,
              name: BASE.ISSUER.ISSUER2.NAME,
              bankCode: BASE.ISSUER.ISSUER2.BANK_CODE,
            },
            {
              issuerId: BASE.ISSUER.ISSUER3.ID,
              name: BASE.ISSUER.ISSUER3.NAME,
              bankCode: BASE.ISSUER.ISSUER3.BANK_CODE,
            },
            {
              issuerId: BASE.ISSUER.ISSUER4.ID,
              name: BASE.ISSUER.ISSUER4.NAME,
              bankCode: BASE.ISSUER.ISSUER4.BANK_CODE,
            },
          ],
          totalCount: 5,
          err: '',
        }
        result.issuers.forEach((v, i) => {
          assertEqualForEachField(v, {
            issuerId: expected.issuers[i].issuerId,
            name: expected.issuers[i].name,
            bankCode: String(expected.issuers[i].bankCode),
          })
        })
        await expect(result.totalCount).to.be.equal(expected.totalCount)
        await expect(result.err).to.be.equal(expected.err)
      })
      it('オフセットとリミットを指定した際に、ゾーンに紐付くイシュア情報を取得できること', async () => {
        const result = await financialCheckFuncs.getIssuerWithZone({
          financialCheck,
          params: [BASE.ZONE_ID.ID2, 4, 1],
        })
        const expected = {
          issuers: [
            {
              issuerId: BASE.ISSUER.ISSUER4.ID,
              name: BASE.ISSUER.ISSUER4.NAME,
              bankCode: BASE.ISSUER.ISSUER4.BANK_CODE,
            },
          ],
          totalCount: 5,
          err: '',
        }
        result.issuers.forEach((v, i) => {
          assertEqualForEachField(v, {
            issuerId: expected.issuers[i].issuerId,
            name: expected.issuers[i].name,
            bankCode: String(expected.issuers[i].bankCode),
          })
        })
        await expect(result.totalCount).to.be.equal(expected.totalCount)
        await expect(result.err).to.be.equal(expected.err)
      })
    })
  })
  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('アカウントが登録されている状態', () => {
      let params

      before(async () => {
        params = await setupAccountParams()
        await setupBasicRoles()
        await issuerFuncs.addIssuer({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER1.ID,
            name: BASE.ISSUER.ISSUER1.NAME,
            bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
          },
        })
        await setupAccounts(params)
        await providerFuncs.addBizZone({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })
        await setupBasicIssuerZoneMapping()
      })

      it('ゾーンに紐付くイシュア情報が存在しない場合、取得に失敗すること', async () => {
        const result = await financialCheckFuncs.getIssuerWithZone({
          financialCheck,
          params: [BASE.ZONE_ID.ID0, 0, 100],
        })
        const expected = {
          issuers: [{}],
          totalCount: 0,
          err: '',
        }
        result.issuers.forEach((v, i) => {
          assertEqualForEachField(v, {})
        })
        await expect(result.totalCount).to.be.equal(expected.totalCount)
        await expect(result.err).to.be.equal(expected.err)
      })

      it('limitが取得上限(100件)より大きい場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 101
        const result = await financialCheckFuncs.getIssuerWithZone({
          financialCheck,
          params: [BASE.ZONE_ID.ID1, offset, limit],
        })
        assertEqualForEachField(result, {
          totalCount: 0,
          err: ERR.ISSUER.ISSUER_TOO_LARGE_LIMIT,
        })
      })

      it('offsetが登録されている件数以上の場合、エラーが返されること', async () => {
        const offset = 20
        const limit = 20
        const result = await financialCheckFuncs.getIssuerWithZone({
          financialCheck,
          params: [BASE.ZONE_ID.ID1, offset, limit],
        })
        assertEqualForEachField(result, {
          totalCount: 0,
          err: ERR.ISSUER.ISSUER_OFFSET_OUT_OF_INDEX,
        })
      })
    })
  })
})
