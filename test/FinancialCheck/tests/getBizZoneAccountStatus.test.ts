import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { businessZoneAccountFuncs } from '@test/BusinessZoneAccount/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  FinancialCheckInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { assertEqualForEachField, getDeadline } from '@test/common/utils'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { financialCheckFuncs } from '@test/FinancialCheck/helpers/function'
import { FinancialCheckContractType } from '@test/FinancialCheck/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'

describe('getBizZoneAccountStatus()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let token: TokenInstance
  let financialCheck: FinancialCheckInstance
  let contractManager: ContractManagerInstance
  let businessZoneAccount: BusinessZoneAccountInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, token, financialCheck, contractManager, businessZoneAccount } =
      await contractFixture<FinancialCheckContractType>())
  }

  const setupIbcAddress = async () => {
    const ibcAddress = await accounts[0]
    const ibcAddressString = await ibcAddress.getAddress()
    return { ibcAddress, ibcAddressString }
  }

  const setupAccountParams = async () => {
    const pramsAccounts = Object.values(BASE.ACCOUNT)
      .filter((v) => typeof v === 'object')
      .slice(0, 4)
    return pramsAccounts.map((v, i) => {
      return {
        accountId: v.ID,
        accountName: v.NAME,
        accountEoa: accounts[i],
        accountApprovalAll: [{ spenderId: pramsAccounts[i].ID, amount: 100 }],
      }
    })
  }

  const setupBasicRoles = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({
      provider,
      accounts,
      options: {
        providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
      },
    })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await validatorFuncs.addValidatorRole({
      validator,
      accounts,
      options: {
        validatorEoa: BASE.VALID.EOA_ADDRESS,
      },
    })
    await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
  }

  const setupAccounts = async (params: any[]) => {
    const deadline = await getDeadline()
    for (const v of params) {
      await validatorFuncs.addAccount({
        validator,
        accounts,
        options: {
          validatorId: BASE.VALID.VALID0.ID,
          accountId: v.accountId,
          accountName: v.accountName,
        },
      })
      await issuerFuncs.addAccountRole({
        issuer,
        accounts,
        options: {
          issuerId: BASE.ISSUER.ISSUER0.ID,
          accountId: v.accountId,
          deadline: deadline + 60,
        },
      })
    }
  }

  const setupTokenBalance = async () => {
    await tokenFuncs.mint({
      token,
      accounts,
      amount: 1000,
      options: {
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
      },
    })
  }

  const setupIbcAppsAndBusinessZone = async (ibcAddressString: string) => {
    await contractManagerFuncs.setIbcApp({
      contractManager,
      accounts,
      ibcAddress: ibcAddressString,
      ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
    })
    await providerFuncs.addBizZone({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID1,
        zoneName: BASE.ZONE_NAME.NAME1,
      },
    })
  }

  const setupBusinessZoneStatus = async () => {
    await businessZoneAccountFuncs.syncBusinessZoneStatus({
      businessZoneAccount,
      accounts,
      options: {
        accountId: BASE.ACCOUNT.ACCOUNT2.ID,
        zoneId: BASE.ZONE_ID.ID1,
      },
    })
    await validatorFuncs.setActiveBusinessAccountWithZone({
      validator,
      accounts,
      options: {
        validatorId: BASE.VALID.VALID0.ID,
        zoneId: BASE.ZONE_ID.ID1,
        accountId: BASE.ACCOUNT.ACCOUNT2.ID,
      },
    })
  }

  describe('正常系', () => {
    let ibcAddress
    let ibcAddressString
    before(async () => {
      await setupFixture()
      const ibcSetup = await setupIbcAddress()
      ibcAddress = ibcSetup.ibcAddress
      ibcAddressString = ibcSetup.ibcAddressString
    })

    describe('アカウントが登録されている状態', () => {
      before(async () => {
        const params = await setupAccountParams()
        await setupBasicRoles()
        await setupAccounts(params)
        await setupTokenBalance()
        await setupIbcAppsAndBusinessZone(ibcAddressString)
        await setupBusinessZoneStatus()
      })

      it('Bizアカウントのステータスを取得できること', async () => {
        const result = await financialCheckFuncs.getBizZoneAccountStatus({
          financialCheck,
          params: [BASE.ACCOUNT.ACCOUNT2.ID, BASE.ZONE_ID.ID1],
        })
        const expected = {
          accountStatus: BASE.STATUS.ACTIVE,
        }
        assertEqualForEachField(result, expected)
      })
    })
  })

  describe('準正常系', () => {
    let ibcAddress
    let ibcAddressString
    before(async () => {
      await setupFixture()
      const ibcSetup = await setupIbcAddress()
      ibcAddress = ibcSetup.ibcAddress
      ibcAddressString = ibcSetup.ibcAddressString
    })

    describe('アカウントが登録されている状態', () => {
      before(async () => {})

      it('存在しないIDの場合、エラーが返却されること', async () => {
        const result = await financialCheckFuncs.getBizZoneAccountStatus({
          financialCheck,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, BASE.ZONE_ID.ID1],
        })
        const expected = {
          accountStatus: BASE.EMPTY.ID,
          err: ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST,
        }
        assertEqualForEachField(result, expected)
      })

      it('ZoneIdが未入力の場合、エラーが返却されること', async () => {
        const result = await financialCheckFuncs.getBizZoneAccountStatus({
          financialCheck,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, BASE.ZONE_ID.EMPTY_ID],
        })
        const expected = {
          accountStatus: BASE.EMPTY.ID,
          err: ERR.ACCOUNT.ACCOUNT_INVALID_VAL,
        }
        assertEqualForEachField(result, expected)
      })

      it('AccountIdが未入力の場合、エラーが返却されること', async () => {
        const result = await financialCheckFuncs.getBizZoneAccountStatus({
          financialCheck,
          params: [BASE.ACCOUNT.EMPTY.ID, BASE.ZONE_ID.ID1],
        })
        const expected = {
          accountStatus: BASE.EMPTY.ID,
          err: ERR.ACCOUNT.ACCOUNT_INVALID_VAL,
        }
        assertEqualForEachField(result, expected)
      })
    })
  })
})
