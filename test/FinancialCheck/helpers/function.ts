import { BASE } from '@test/common/consts'
import { castReturnType, toBytes32 } from '@test/common/utils'
import { FunctionType } from './types'
import privateKey from '@/privateKey'

/**
 * FinancialCheckのイベントを呼ぶ関数を持つobject
 */
export const financialCheckFuncs: FunctionType = {
  version: ({ financialCheck }) => {
    return castReturnType(financialCheck.version())
  },
  checkTransaction: async ({ options = {}, tokenOptions = {}, ...args }) => {
    const { accountSignature, eoaKey = BASE.EOA.VALID1 } = options
    const { miscValue1 = toBytes32('0'), miscValue2 = '0' } = tokenOptions
    const _accountSignature =
      accountSignature ??
      privateKey.sig(
        args.sigInfo.signer,
        ['bytes32', 'bytes32', 'bytes32', 'uint256', 'uint256'],
        [args.sendAccountId, args.fromAccountId, args.toAccountId, args.amount, BASE.ACCOUNT_SIG_MSG.TRANSFER],
      )[0]

    return castReturnType(
      args.financialCheck.checkTransaction(
        args.zoneId,
        args.sendAccountId,
        args.fromAccountId,
        args.toAccountId,
        args.fromAccountIssuerId,
        args.amount,
        miscValue1,
        miscValue2,
        _accountSignature,
        args.sigInfo.info,
      ),
    )
  },

  checkExchange: async (args) => {
    return castReturnType(
      args.financialCheck.checkExchange(args.accountId, args.fromZoneId, args.toZoneId, args.amount),
    )
  },

  checkSyncAccount: async ({ options = {}, ...args }) => {
    const { accountSignature } = options
    const _accountSignature =
      accountSignature ??
      privateKey.sig(args.sigInfo.signer, ['bytes32', 'uint256'], [args.accountId, BASE.ACCOUNT_SIG_MSG.SYNCHRONOUS])[0]

    return castReturnType(
      args.financialCheck.checkSyncAccount(
        args.validatorId,
        args.accountId,
        args.zoneId,
        args.accountStatus,
        _accountSignature,
        args.sigInfo.info,
      ),
    )
  },

  checkFinAccountStatus: ({ financialCheck, params }) => {
    return castReturnType(financialCheck.checkFinAccountStatus(...params))
  },

  getAccountLimit: ({ financialCheck, params }) => {
    return castReturnType(financialCheck.getAccountLimit(...params))
  },

  getBizZoneAccountStatus: ({ financialCheck, params }) => {
    return castReturnType(financialCheck.getBizZoneAccountStatus(...params))
  },

  getIssuerWithZone: ({ financialCheck, params }) => {
    return castReturnType(financialCheck.getIssuerWithZone(...params))
  },
}
