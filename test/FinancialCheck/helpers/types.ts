import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccessCtrlInstance,
  AccountInstance,
  BusinessZoneAccountInstance,
  CheckTransactionTokenOption,
  ContractCallOption,
  ContractManagerInstance,
  EventReturnType,
  FinancialCheckInstance,
  FinancialZoneAccountInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  TransferableMock1Instance,
  TransferableMock2Instance,
  TransferableMock3Instance,
  TransferProxyInstance,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { PromiseType } from 'utility-types'

export type FinancialCheckContractType = {
  accounts: SignerWithAddress[]
  provider: ProviderInstance
  issuer: IssuerInstance
  validator: ValidatorInstance
  account: AccountInstance
  token: TokenInstance
  financialCheck: FinancialCheckInstance
  transferProxy: TransferProxyInstance
  contractManager: ContractManagerInstance
  customTransfer1: TransferableMock1Instance
  customTransfer2: TransferableMock2Instance
  customTransfer3: TransferableMock3Instance
  accessCtrl: AccessCtrlInstance
  ibcToken: IBCTokenInstance
  businessZoneAccount: BusinessZoneAccountInstance
  financialZoneAccount: FinancialZoneAccountInstance
}

type FinancialCheckType = { financialCheck: FinancialCheckInstance }

type AccountSignatureType = Partial<
  {
    accountSignature: Parameters<FinancialCheckInstance['checkSyncAccount']>[4]
  } & ContractCallOption
>

type SigInfoType = PromiseType<ReturnType<typeof utils.siginfoGenerator>>

export type CheckTransactionType = FinancialCheckType & {
  zoneId: number
  sendAccountId: string
  fromAccountId: string
  toAccountId: string
  fromAccountIssuerId: string
  amount: number
  sigInfo: PromiseType<ReturnType<typeof utils.siginfoGenerator>>
  options?: Partial<
    {
      accountSignature: Parameters<FinancialCheckInstance['checkTransaction']>[5]
    } & ContractCallOption
  >
  tokenOptions?: Partial<CheckTransactionTokenOption>
}

export type FuncParamsType = {
  version: FinancialCheckType
  checkTransaction: FinancialCheckType & {
    zoneId: number
    sendAccountId: string
    fromAccountId: string
    toAccountId: string
    fromAccountIssuerId: string
    amount: number
    sigInfo: PromiseType<ReturnType<typeof utils.siginfoGenerator>>
    options?: Partial<
      {
        accountSignature: Parameters<FinancialCheckInstance['checkTransaction']>[8]
      } & ContractCallOption
    >
    tokenOptions?: Partial<CheckTransactionTokenOption>
  }
  checkExchange: FinancialCheckType & {
    accountId: string
    fromZoneId: number
    toZoneId: number
    amount: number
  }
  checkSyncAccount: FinancialCheckType & {
    validatorId: string
    accountId: string
    zoneId: number
    accountStatus: string
    sigInfo: SigInfoType
    options?: AccountSignatureType
  }
  checkFinAccountStatus: FinancialCheckType & {
    params: Parameters<FinancialCheckInstance['checkFinAccountStatus']>
  }
  getAccountLimit: FinancialCheckType & {
    params: Parameters<FinancialCheckInstance['getAccountLimit']>
  }
  getBizZoneAccountStatus: FinancialCheckType & {
    params: Parameters<FinancialCheckInstance['getBizZoneAccountStatus']>
  }
  getIssuerWithZone: FinancialCheckType & {
    params: Parameters<FinancialCheckInstance['getIssuerWithZone']>
  }
}

type FuncReturnType = {
  version: string
  checkTransaction: EventReturnType['FinancialCheck']['CheckTransaction']
  checkExchange: EventReturnType['FinancialCheck']['CheckExchange']
  checkSyncAccount: EventReturnType['FinancialCheck']['CheckSyncAccount']
  checkFinAccountStatus: EventReturnType['FinancialCheck']['CheckFinAccountStatus']
  getAccountLimit: EventReturnType['FinancialCheck']['GetAccountLimit']
  getBizZoneAccountStatus: EventReturnType['FinancialCheck']['GetBizZoneAccountStatus']
  getIssuerWithZone: EventReturnType['FinancialCheck']['GetIssuerWithZone']
}

export type FunctionType = {
  [K in keyof FuncParamsType]: (args: FuncParamsType[K]) => Promise<FuncReturnType[K]>
}
