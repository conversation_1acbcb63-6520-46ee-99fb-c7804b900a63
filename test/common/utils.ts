import { time } from '@nomicfoundation/hardhat-network-helpers'
import { EventType, PacketType, StrictPropertyCheck } from '@test/common/types'
import BigNumber from 'bignumber.js'
import BN from 'bn.js'
import chalk from 'chalk'
import _ from 'lodash'
import secp256k1 from 'secp256k1'
import truffleAssertions from 'truffle-assertions'
import Web3 from 'web3'

declare let assert: Chai.Assert
declare let web3: Web3

export const getLatestBlockTimestamp = async () => await time.latest()

// hardhatのテストネットワークにおける現在時刻が、Date.now()で取得したPCの現在時刻よりも進んでしまっているため、署名検証時に"sig timeout"が発生する
// "sig timeout"を回避するためテストネットワークの時刻からDeadlineを生成している
export const getDeadline = async (deadline?: number) => deadline ?? (await getLatestBlockTimestamp()) + 100000 // DCPF-25730 fix sig timeout error

export const getExceededDeadline = async () => (await getLatestBlockTimestamp()) - 100

export const getJSTDay = async () => Math.floor(((await getLatestBlockTimestamp()) + 32400) / 86400) * 86400 - 32400

export const toBytes = (str: string): string => Web3.utils.asciiToHex(str)

export const toBytes32 = (str: string): string => Web3.utils.asciiToHex(str).padEnd(66, '0') // 66 = 2 (0x) + 32 * 2

export const toAddress = (str: string): string => Web3.utils.asciiToHex(str).padEnd(42, '0') // 42 = 2 (0x) + 20 * 2

export const toHex = (num: number): string => Web3.utils.padLeft(Web3.utils.toHex(num), 64)

/* eslint-disable @typescript-eslint/no-explicit-any */
export const castReturnType = <T>(obj: any): T => obj as T

import * as privateKey from '@/privateKey'

/**
 * expectedが持つキーについて、その値がexpectedとactualで等しいことを確認する
 * actualのみに存在するkeyに関しては無視される
 * @param actual 確認対象のオブジェクト
 * @param expected 確認したいkey-valueを持つオブジェクト
 */
export const assertEqualForEachField = <T extends object>(actual: T, expected: Partial<T>) => {
  const assertEqual = (key: string, actual: any, expected: any) => {
    const msg = `${key} is not equal`
    const tolerance = 100000 // 100ms
    if (key.endsWith('At')) {
      const diff = Math.abs(parseInt(actual) - parseInt(expected))
      assert.ok(diff <= tolerance, `${msg}: expected ${expected} to be within ${tolerance}ms of ${actual}`) // blocktimestampを利用した値の比較の際、どうしても誤差が発生するため許容範囲内の値であれば問題ないとする
      return
    }
    if (BN.isBN(actual) && (typeof expected === 'number' || BN.isBN(expected))) {
      assert.strictEqual(actual.toString(), expected.toString(), msg)
      return
    }
    assert.strictEqual(actual, expected, msg)
  }

  const assertContains = (key: string, actual: unknown, expected: unknown) => {
    if (Array.isArray(actual) && Array.isArray(expected)) {
      expected.forEach((expElem) => {
        assert.ok(
          actual.some((actElem) => assert.deepEqual(actElem, expElem)),
          `${key} does not contain the expected element`,
        )
      })
      return
    }
    if (typeof actual === 'object' && typeof expected === 'object') {
      Object.entries(expected as object).forEach(([nestedKey, nestedValue]) => {
        assert.ok(nestedKey in (actual as object), `${key}.${nestedKey} is missing`)
        assertContains(`${key}.${nestedKey}`, (actual as any)[nestedKey], nestedValue)
      })
      return
    }
    assertEqual(key, actual, expected)
  }

  Object.entries(expected).forEach(([k, _expected]) => {
    const _actual = actual[k]
    assertContains(k, _actual, _expected)
  })
}

type EventParams<T extends keyof EventType> = Omit<EventType[T]['args'], number | 'regionId' | 'limitAmounts'> & {
  regionId?: number
  limitAmounts?: number[]
}
export const assertEventEmitted = <T extends keyof EventType, S extends EventParams<T>>(
  tx: unknown,
  eventType: T,
  expected: Partial<StrictPropertyCheck<S, EventParams<T>, ''>>,
) => {
  truffleAssertions.eventEmitted(tx, eventType, (ev) => {
    assertEqualForEachField(ev, expected)
    return true
  })
}

/**
 * オブジェクトのキーを取得（数字のプロパティを削除）
 *
 * コントラクト関数の返却オブジェクトには、同じ値で二つのプロパティ名が存在する（名前がついたものと数字のもの）。
 * @param returnValueByContract コントラクト関数が返却したオブジェクト
 * @returns オブジェクトのキー（数字のプロパティを削除）
 */
export const getPropsWithoutNumberProps = <T extends {}>(returnValueByContract: T): (keyof T)[] =>
  (Object.keys(returnValueByContract) as (keyof T)[]).filter((key) => typeof key === 'string' && !/^[0-9]+$/.test(key))

/**
 *
 * 署名生成用ツール
 *
 */
export const oneTimePrivateKey = (SKa: string, SKc: string, NN: string): { bSKo: BigNumber; bSKc: BigNumber } => {
  const bSKa = new BigNumber(SKa)
  const bSKc = new BigNumber(SKc)
  const n = new BigNumber(NN)
  const bSKo = bSKa.add(bSKc).mod(n)
  return { bSKo, bSKc }
}

export const oneTimePublicKey = (bSKo: string): string => {
  const _sko = Buffer.from(web3.utils.toHex(bSKo).replace('0x', ''), 'hex')
  const _pko = secp256k1.publicKeyCreate(_sko)
  const pko = '0x' + Buffer.from(_pko).toString('hex')
  return pko
}

export const genPublicKey = (bSKc: string): string => {
  const _bSKc = web3.utils.padLeft(web3.utils.toHex(bSKc), 64)
  const _skc = Buffer.from(_bSKc.replace('0x', ''), 'hex')
  const _pkc = secp256k1.publicKeyCreate(_skc)
  const pkc = '0x' + Buffer.from(_pkc).toString('hex')
  return pkc
}

export const genSigcpt = (pkc: string, pt: BigNumber, validPk: string): string[] => {
  const signerSki = web3.utils.toHex(validPk).replace('0x', '')
  const sigSki = privateKey.sig(signerSki, ['bytes', 'uint256'], [pkc, pt])
  return sigSki
}

export const siginfoGenerator = async (
  SKc: string,
  SKa: string,
  NN: string,
  validPk: string,
  pt: BigNumber,
): Promise<{ info: string; signer: string }> => {
  const { bSKo, bSKc } = oneTimePrivateKey(SKa, SKc, NN)
  const pko = oneTimePublicKey(bSKo)
  const pkc = genPublicKey(bSKc)
  const sigSki = genSigcpt(pkc, pt, validPk)

  // info
  const info = web3.eth.abi.encodeParameters(['bytes', 'bytes', 'uint256', 'bytes'], [pko, pkc, pt, sigSki[0]])

  return { info, signer: web3.utils.toHex(bSKo).replace('0x', '') }
}

export const eccAddParamGenerator = async (
  SKc: number,
  SKa: string,
  NN: string,
): Promise<{
  addressOfSka: string
  PKo: string
  PKoxCompressFlag: string
  PKc: string
  PKcxCompressFlag: string
}> => {
  const splitPk = (pk) => ({
    PKxCompressFlag: pk.slice(0, 4),
    PK: `0x${pk.slice(4)}`,
  })

  const account = web3.eth.accounts.privateKeyToAccount(SKa)
  const addressOfSka = account.address
  const { bSKo, bSKc } = oneTimePrivateKey(SKa, SKc.toString(), NN)
  const pko = oneTimePublicKey(bSKo)
  const splitPKo = splitPk(pko)
  const pkc = genPublicKey(bSKc)
  const splitPKc = splitPk(pkc)

  return {
    addressOfSka: addressOfSka,
    PKo: splitPKo.PK,
    PKoxCompressFlag: splitPKo.PKxCompressFlag,
    PKc: splitPKc.PK,
    PKcxCompressFlag: splitPKc.PKxCompressFlag,
  }
}

export const logSection = (
  title: string,
  inputs: Array<[string, string | number]> = [],
  outputs: Array<[string, string | number]> = [],
): void => {
  console.log(chalk.bold.underline(`\n${title}`))
  if (inputs.length > 0) {
    console.log(chalk.dim.bold('<INPUT>'))
    inputs.forEach(([label, value]) => console.log(chalk.green(` ${label}:`), value))
  }
  if (outputs.length > 0) {
    console.log(chalk.dim.bold('<OUTPUT>'))
    outputs.forEach(([label, value]) => console.log(chalk.green(` ${label}:`), value))
  }
}

export const genPacketData = (data, srcPort, srcChannel, timeoutHeight) => {
  const packet: PacketType['packet'] = {
    sequence: BigInt(1),
    source_port: srcPort,
    source_channel: srcChannel,
    destination_port: srcPort,
    destination_channel: srcChannel,
    data: data,
    timeout_height: {
      revision_number: 0,
      revision_height: BigInt(timeoutHeight),
    },
    timeout_timestamp: 0,
  }
  return packet as any
}

export const assertEqualBn = (a: number | number[] | BN, b: number | number[] | BN): void => {
  const arrayA = Array.isArray(a) ? a : [a]
  const arrayB = Array.isArray(b) ? b : [b]
  const arrayStrA = arrayA.map((v) => (typeof v === 'number' ? Web3.utils.toBN(v).toString(10) : v.toString(10)))
  const arrayStrB = arrayB.map((v) => (typeof v === 'number' ? Web3.utils.toBN(v).toString(10) : v.toString(10)))
  assert.equal(_.isEqual(arrayStrA, arrayStrB), true)
}

export const assertEqualBytes32 = (a: string | string[], b: string | string[]): void => {
  const arrayA = Array.isArray(a) ? a : [a]
  const arrayB = Array.isArray(b) ? b : [b]
  const arrayStrA = arrayA.map((v) => Web3.utils.asciiToHex(v).padEnd(66, '0'))
  const arrayStrB = arrayB.map((v) => Web3.utils.asciiToHex(v).padEnd(66, '0'))
  assert.equal(_.isEqual(arrayStrA, arrayStrB), true)
}
