import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { ROLLED_EOA_KEY } from '@test/AccessCtrl/helpers/constant'
import { contractInitialize } from '@test/AccessCtrl/helpers/contractInitialize'
import { accessCtrlFuncs } from '@test/AccessCtrl/helpers/function'
import { AccessCtrlContractType } from '@test/AccessCtrl/helpers/types'
import { genRoleByPrefix } from '@test/AccessCtrl/helpers/utils'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccessCtrlInstance } from '@test/common/types'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('delIssuerRole()', () => {
  const issuerId = BASE.ISSUER.ISSUER0.ID

  let sender
  let accessCtrl: AccessCtrlInstance
  let accounts: SignerWithAddress[]
  let role: string
  let rolledAccount

  const setupFixture = async () => {
    ;({ accounts } = await contractFixture<AccessCtrlContractType>())
  }

  const initData = async () => {
    sender = await accounts[7]
    ;({ accessCtrl } = await contractInitialize({
      accounts,
      customAddress: { issuer: await sender.getAddress() },
    }))
    rolledAccount = await accounts[2]
    role = await genRoleByPrefix({
      accessCtrl,
      getRolePrefixFuncName: 'ROLE_PREFIX_ISSUER',
      id: issuerId,
    })
    await accessCtrlFuncs.addRoleByIssuer({
      accessCtrl,
      accounts,
      issuerId,
      role,
      account: await rolledAccount.getAddress(),
      options: {
        sender,
      },
    })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
      await initData()
    })

    describe('アカウントにIssuer権限が付与されている状態', () => {
      it('Issuer権限のないアカウントを指定した場合、RoleRevokedイベントが発火されないこと', async () => {
        const tx = await accessCtrlFuncs.delIssuerRole({
          accessCtrl,
          accounts,
          issuerId,
          account: await accounts[9].getAddress(),
        })

        await expect(tx).to.not.emit(accessCtrl, 'RoleRevoked')
      })

      it('権限が削除されること', async () => {
        const tx = await accessCtrlFuncs.delIssuerRole({
          accessCtrl,
          accounts,
          issuerId,
          account: await rolledAccount.getAddress(),
        })

        const expectParams = {
          role,
          account: await rolledAccount.getAddress(),
          sender: await accounts[ROLLED_EOA_KEY.DEFAULT_ADMIN].getAddress(),
        }
        await expect(tx)
          .to.emit(accessCtrl, 'RoleRevoked')
          .withArgs(...Object.values(expectParams))
        assert.notOk(
          await accessCtrlFuncs.hasRole({
            accessCtrl,
            role,
            account: await rolledAccount.getAddress(),
          }),
        )
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
      await initData()
    })

    describe('アカウントにIssuer権限が付与されている状態', () => {
      it('呼び出し元がDEFAULT_ADMIN権限ではない場合、エラーがスローされること', async () => {
        const result = accessCtrlFuncs.delIssuerRole({
          accessCtrl,
          accounts,
          issuerId,
          account: await rolledAccount.getAddress(),
          options: { sender: accounts[ROLLED_EOA_KEY.NOT_ADMIN] },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_ROLE)
      })
    })
  })
})
