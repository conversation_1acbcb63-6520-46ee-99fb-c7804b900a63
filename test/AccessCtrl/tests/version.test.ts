import '@nomicfoundation/hardhat-chai-matchers'
import { accessCtrlFuncs } from '@test/AccessCtrl/helpers/function'
import { AccessCtrlContractType } from '@test/AccessCtrl/helpers/types'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccessCtrlInstance } from '@test/common/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('version()', () => {
  let accessCtrl: AccessCtrlInstance

  const setupFixture = async () => {
    ;({ accessCtrl } = await contractFixture<AccessCtrlContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    it('versionが取得できること', async () => {
      assert.equal(await accessCtrlFuncs.version({ accessCtrl: accessCtrl }), BASE.APP.VERSION, 'version')
    })
  })
})
