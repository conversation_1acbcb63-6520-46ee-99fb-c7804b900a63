import { accessCtrlFuncs } from '@test/AccessCtrl/helpers/function'
import { AccessCtrlInstance } from '@test/common/types'

export const genRoleByPrefix = async ({
  accessCtrl,
  getRolePrefixFuncName,
  id,
}: {
  accessCtrl: AccessCtrlInstance
  getRolePrefixFuncName: keyof Pick<
    AccessCtrlInstance,
    'ROLE_PREFIX_PROV' | 'ROLE_PREFIX_ACCOUNT' | 'ROLE_PREFIX_ISSUER' | 'ROLE_PREFIX_VALIDATOR'
  >
  id: string
}) => {
  const role_prefix = await accessCtrl[getRolePrefixFuncName]()
  const params = {
    accessCtrl,
    prefix: role_prefix,
    id: id,
  }
  return await accessCtrlFuncs.calcRole(params)
}
