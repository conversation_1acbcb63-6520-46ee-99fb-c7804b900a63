import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { AccessCtrlInstance, ContractCallOption, EventParamOptionType, EventReturnType } from '@test/common/types'
import { ContractTransactionResponse } from 'ethers'

export type AccessCtrlContractType = AccessCtrlType & {
  accounts: SignerWithAddress[]
}

type AccessCtrlType = {
  accessCtrl: AccessCtrlInstance
}

type CommonOptions = Partial<
  ContractCallOption & {
    sender?: SignerWithAddress
    sig?: string[]
    deadline?: number
    eoaKey?: number
    hash?: string
  }
>

type BaseAccountType = {
  account: string
}

type BaseRoleType = BaseAccountType & {
  role: string
}

type BaseOptionsType = {
  options?: CommonOptions
}

type BaseSenderType = {
  from: SignerWithAddress
}

type WithAccountsType = {
  accounts: SignerWithAddress[]
}

export type FuncParamsType = {
  version: AccessCtrlType
  calcRole: AccessCtrlType & { prefix: string; id: string }
  checkAdminRole: AccessCtrlType &
    BaseAccountType & {
      options?: Partial<EventParamOptionType['AccessCtrl']['CheckAdminRoleOption'] & ContractCallOption>
    }
  checkRole: AccessCtrlType &
    BaseRoleType & {
      options?: Partial<EventParamOptionType['AccessCtrl']['CheckRoleOption'] & ContractCallOption>
    }
  hasRole: AccessCtrlType & BaseRoleType
  addAdminRole: AccessCtrlType & BaseSenderType & BaseAccountType & BaseOptionsType
  addRole: AccessCtrlType & BaseSenderType & BaseAccountType & { role: string } & BaseOptionsType
  addRoleByProv: AccessCtrlType & BaseRoleType & WithAccountsType & { providerId: string } & BaseOptionsType
  addRoleByIssuer: AccessCtrlType & BaseRoleType & WithAccountsType & { issuerId: string } & BaseOptionsType
  addRoleByValidator: AccessCtrlType & BaseRoleType & WithAccountsType & { validatorId: string } & BaseOptionsType
  delAdminRole: AccessCtrlType & BaseAccountType & WithAccountsType & BaseOptionsType
  delProviderRole: AccessCtrlType & BaseAccountType & WithAccountsType & { providerId: string } & BaseOptionsType
  delIssuerRole: AccessCtrlType & BaseAccountType & WithAccountsType & { issuerId: string } & BaseOptionsType
  delValidatorRole: AccessCtrlType & BaseAccountType & WithAccountsType & { validatorId: string } & BaseOptionsType
}

type FuncReturnType = {
  version: string
  calcRole: EventReturnType['AccessCtrl']['CalcRole']
  checkAdminRole: EventReturnType['AccessCtrl']['CheckAdminRole']
  checkRole: EventReturnType['AccessCtrl']['CheckRole']
  hasRole: EventReturnType['AccessCtrl']['HasRole']
  addAdminRole: ContractTransactionResponse
  addRole: ContractTransactionResponse
  addRoleByProv: ContractTransactionResponse
  addRoleByIssuer: ContractTransactionResponse
  addRoleByValidator: ContractTransactionResponse
  delAdminRole: ContractTransactionResponse
  delProviderRole: ContractTransactionResponse
  delIssuerRole: ContractTransactionResponse
  delValidatorRole: ContractTransactionResponse
}

export type FunctionType = {
  [K in keyof FuncParamsType]: (args: FuncParamsType[K]) => Promise<FuncReturnType[K]>
}
