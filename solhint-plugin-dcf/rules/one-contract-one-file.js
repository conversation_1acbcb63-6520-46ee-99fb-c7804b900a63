const BaseChecker = require("solhint/lib/rules/base-checker");

const ruleId = "one-contract-one-file";
const meta = {
  type: "best-practises",

  docs: {
    description: "",
    category: "",
  },

  isDefault: true,
  recommended: true,
  defaultSetup: ["error"],
  schema: null,
};

class OneContractOneFile extends BaseChecker {
  constructor(reporter) {
    super(reporter, ruleId, meta);
  }

  SourceUnit(node) {
    this._findContract(node);
  }

  _findContract(node) {
    const contractCount = node.children.reduce((count, child) => {
      if (child.type === "ContractDefinition") {
        return count + 1;
      }
      return count;
    }, 0);

    if (contractCount > 1) {
      this.error(
        node,
        "There are more than one contract or library in one file."
      );
    }
  }
}

module.exports = OneContractOneFile;
