// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "./Struct.sol";

/**
 * @dev Accountコントラクト
 */
interface IAccount {
    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    // 共通領域 Account登録
    function addAccount(
        bytes32 accountId,
        string memory accountName,
        bytes32 validatorId
    ) external;

    //AccountのRole追加
    function addAccountRole(
        bytes32 accountId,
        address accountEoa,
        bytes32 traceId
    ) external;

    // @dev アカウント名変更
    function modAccount(
        bytes32 accountId,
        string memory accountName,
        bytes32 traceId
    ) external;

    //Accountの有効化/凍結
    function setAccountStatus(
        bytes32 accountId,
        bytes32 accountStatus,
        bytes32 reasonCode,
        bytes32 traceId
    ) external;

    //Accountの解約
    function setTerminated(
        bytes32 accountId,
        bytes32 reasonCode,
        bytes32 traceId
    ) external;

    // 連携済みzone情報の追加
    function addZone(
        bytes32 accountId,
        uint16 zoneId,
        bytes32 traceId
    ) external;

    //Mint
    // 呼び出し元のTokenで残高をemitするため、残高のreturnを行う
    function mint(bytes32 accountId, uint256 amount) external returns (uint256);

    //Burn
    // 呼び出し元のTokenで残高をemitするため、残高のreturnを行う
    function burn(bytes32 accountId, uint256 amount) external returns (uint256);

    //transferFrom
    function calcBalance(
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint256 amount
    ) external returns (uint256 fromAccountBalance, uint256 toAccountBalance);

    // Allowanceの減額を行う
    function calcAllowance(
        bytes32 ownerId,
        bytes32 spenderId,
        uint256 amount
    ) external;

    //editBalance
    function editBalance(
        bytes32 accountId,
        uint256 amount,
        uint256 calcPattern
    ) external returns (uint256 balance);

    //Approve
    function approve(
        bytes32 ownerId,
        bytes32 spenderId,
        uint256 amount
    ) external;

    function setAccountAll(
        AccountsAll memory accountsAll,
        uint256 deadline,
        bytes memory signature
    ) external;

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    //AccountID存在確認
    function hasAccount(bytes32 accountId) external view returns (bool success, string memory err);

    //Accountの有効性確認
    function isActivated(bytes32 accountId) external view returns (bool success, string memory err);

    //IndexよりAccountIDを出力する
    function getAccountId(uint256 index)
        external
        view
        returns (bytes32 accountId, string memory err);

    //IDより対象Accountのステータスを出力する
    function getAccount(bytes32 accountId)
        external
        view
        returns (AccountDataWithoutZoneId memory accountData, string memory err);

    //IDより対象アカウントの全データを主力する
    function getAccountAll(bytes32 accountId)
        external
        view
        returns (AccountDataAll memory accountDataAll, string memory err);

    // 移転先のアカウント情報を取得する
    function getDestinationAccount(bytes32 accountId)
        external
        view
        returns (string memory accountName, string memory err);

    //IDより対象Accountに紐づけられているValiadtorIDを取得する
    function getValidatorIdByAccountId(bytes32 accountId)
        external
        view
        returns (bytes32 validatorId, string memory err);

    // IDより対象Accountの連携済みゾーン情報を出力する
    function getZoneByAccountId(bytes32 accountId) external view returns (ZoneData[] memory zones);

    /**
     * @dev アカウントの限度額情報を取得する
     *
     * @param validatorId validatorId
     * @param accountId accountId
     * @return accountLimitData
     * @return err
     */
    function getAccountLimit(bytes32 validatorId, bytes32 accountId)
        external
        view
        returns (FinancialZoneAccountData memory accountLimitData, string memory err);

    //総Account数
    function getAccountCount() external view returns (uint256 count);

    //Accountの解約確認
    function isTerminated(bytes32 accountId)
        external
        view
        returns (bool terminated, string memory err);

    function getAllowance(bytes32 ownerId, bytes32 spenderId)
        external
        view
        returns (
            uint256 allowance,
            uint256 approvedAt,
            string memory err
        );

    function getAllowanceList(
        bytes32 ownerId,
        uint256 offset,
        uint256 limit
    )
        external
        view
        returns (
            AccountApprovalAll[] memory approvalData,
            uint256 totalCount,
            string memory err
        );

    // AccountのBalance取得
    function balanceOf(bytes32 accountId)
        external
        view
        returns (uint256 balance, string memory err);

    // Accountの凍結状態を確認
    function isFrozen(bytes32 accountId) external view returns (bool frozen, string memory err);

    // AccountのBalanceを強制償却
    function forceBurn(bytes32 accountId, bytes32 traceId) external;

    // AccountのBalanceを部分的に強制償却
    function partialForceBurn(
        bytes32 accountId,
        uint256 burnedAmount,
        uint256 burnedBalance,
        bytes32 traceId
    ) external;

    function getAccountsAll(uint256 index) external view returns (AccountsAll memory account);

    /**
     * @dev 残高照会後のイベントを発行する
     * @param fromAccountId 送金元アカウントID（照会対象）
     * @param toAccountId 送金先アカウントID（照会対象）
     * @param traceId トレースID（イベントの追跡用）
     */
    function emitAfterBalance(
        bytes32 fromAccountId,
        bytes32 toAccountId,
        bytes32 traceId
    ) external;

    ///////////////////////////////////
    // events
    ///////////////////////////////////

    // AccountEnabled
    event AccountEnabled(
        bytes32 indexed accountId, //アカウントID
        bytes32 accountStatus, //アカウントステータス
        bytes32 reasonCode, //停止理由
        bytes32 traceId //トレースID
    );
    // AccountのEoa追加
    event AddAccountRole(
        bytes32 indexed accountId,
        address accountEoa,
        bytes32 traceId //トレースID
    );
    // Accountの解約フラグの確認
    event AccountTerminated(
        bytes32 accountId,
        bytes32 reasonCode, //停止理由
        bytes32 traceId //トレースID
    );
    // Accountの名称変更
    event ModAccount(bytes32 accountId, string accountName, bytes32 traceId);
    // Accountに連携したゾーンの追加
    event AddZone(
        bytes32 indexed accountId, //アカウントID
        uint16 zoneId, //ゾーンID
        bytes32 traceId //トレースID
    );
    event ForceBurn(
        bytes32 validatorId,
        bytes32 accountId,
        bytes32 traceId,
        uint256 totalBalance,
        uint256 burnedAmount,
        uint256 burnedBalance,
        ForceDischarge[] forceDischarge
    );

    /**
     * @dev 残高照会後に発行されるイベント
     * @param fromAfterBalance 送金元アカウントの全ゾーン残高配列（FinZone→BizZone昇順）
     * @param toAfterBalance 送金先アカウントの全ゾーン残高配列（FinZone→BizZone昇順）
     * @param traceId 照会操作の追跡用ID
     */
    event AfterBalance(
        AllBalanceData[] fromAfterBalance,
        AllBalanceData[] toAfterBalance,
        bytes32 traceId
    );
}
