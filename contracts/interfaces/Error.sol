// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

/**
 * @dev Error文字列
 */
library Error {
    // solhint-disable state-visibility

    //
    // 01000～01999: AccessCtrl
    //
    // string constant ACTRL_BEGIN = "1000";
    /// @dev Error: 署名タイムアウト
    string constant ACTRL_SIG_TIMEOUT = "1001:sig timeout";
    /// @dev Error: roleが不正
    string constant ACTRL_BAD_ROLE = "1003:bad role";
    /// @dev Error: 署名長が不正(署名の不正はtry-catchで捕捉できないため定義しない)
    string constant ACTRL_BAD_SIG = "1004:bad sig";
    //
    /// @dev Error: 処理を実行する権限が無い
    string constant ACTRL_NOT_ROLE = "1101:not role";
    /// @dev Error: 値が不正
    string constant ACTRL_INVALID_VAL = "1992:accessctrl invalid value";
    /// @dev Error: 署名復元したアドレスがAdmin権限を持たない(hash不一致の可能性もある)
    string constant ACTRL_NOT_ADMIN_ROLE = "1995:accessctrl not admin";
    //
    // string constant ACTRL_END = "1999";

    //
    // 02000～02999: Provider
    //
    // string constant PROV_BEGIN = "2000";
    /// @dev Error: 登録済みプロバイダID
    string constant PROV_ID_EXIST = "2001:exist providerID";
    /// @dev Error: 未登録プロバイダID
    string constant PROV_ID_NOT_EXIST = "2002:not exist";
    /// @dev Error: 登録済みアドレス
    string constant PROV_ADDR_EXIST = "2003:exist addr";
    /// @dev Error: 無効プロバイダ
    string constant PROV_DISABLED = "2004:disabled";
    /// @dev Error: 登録済みcurrID
    string constant PROV_CURRID_EXIST = "2005:exist currID";
    /// @dev Error: ProviderIDではない
    string constant NOT_PROVIDER_ID = "2006:not provider id";
    //
    /// @dev Error: 処理を実行する権限が無い
    string constant PROV_NOT_ROLE = "2101:not prov";
    /// @dev Error: プロバイダ未登録
    string constant PROV_NOT_EXIST = "2102:provider not exist";
    /// @dev Error: ゾーン未登録
    string constant ZONE_NOT_EXIST = "2103:zone not exist";
    /// @dev Error: 呼び出し元がProviderのコントラクトではない
    string constant NOT_PROVIDER_CONTRACT = "2104:not provider contract";
    /// @dev Error: 値が不正
    string constant PROV_INVALID_VAL = "2992:provider invalid value";
    /// @dev Error: 署名復元したアドレスがAdmin権限を持たない(hash不一致の可能性もある)
    string constant PROV_NOT_ADMIN_ROLE = "2995:provider not admin";
    //
    // string constant PROV_END = "2999";

    //
    // 03000～03999: Issuer
    //
    // string constant ISSUER_BEGIN = "3000";
    /// @dev Error: 登録済み発行者ID
    string constant ISSUER_ID_EXIST = "3001:exist id";
    /// @dev Error: 未登録発行者ID
    string constant ISSUER_ID_NOT_EXIST = "3002:not exist";
    /// @dev Error: 登録済みアドレス
    string constant ISSUER_ADDR_EXIST = "3003:exist addr";
    /// @dev Error: 無効発行者
    string constant ISSUER_DISABLED = "3004:disabled";
    /// @dev Error: 登録済みユーザID
    string constant ISSUER_ACCOUNT_EXIST = "3005:exist userID";
    /// @dev Error: 登録済みトークンID
    string constant ISSUER_TOKEN_EXIST = "3006:exist tokenID";
    /// @dev Error: 登録済みAccountID
    string constant ISSUER_HAS_THIS_ACCOUNT_ID = "3007:exist accountID";
    /// @dev Error: itemFlgs配列の要素数が不正
    string constant ITEMFLGS_INVALID_VAL = "3008:itemFlgs invalid count";
    /// @dev Error: limitAmount配列の要素数が不正
    string constant LIMITAMOUNT_INVALID_VAL = "3009:limitAmount invalid count";
    /// @dev Error: 同一のbankCodeが既にとうろくされている
    string constant ISSUER_EXIST_BANK_CODE = "3010:exist bank code";
    //
    /// @dev Error: 処理を実行する権限が無い
    string constant ISSUER_NOT_ROLE = "3101:not issuer";
    /// @dev Error: 範囲外を指定された
    string constant ISSUER_OUT_OF_INDEX = "3102:out of index";
    /// @dev Error: 一覧検索時のlimitとして最大取得件数を超える値が指定された
    string constant ISSUER_TOO_LARGE_LIMIT = "3103:too large limit";
    /// @dev Error: 一覧検索時のoffsetとして範囲外を指定された
    string constant ISSUER_OFFSET_OUT_OF_INDEX = "3104:out of index (offset)";
    /// @dev Error: 呼び出し元がIssuerのコントラクトではない
    string constant NOT_ISSUER_CONTRACT = "3105:not issuer contract";
    /// @dev Error: 値が不正
    string constant ISSUER_INVALID_VAL = "3992:issuer invalid value";
    /// @dev Error: 署名復元したアドレスがAdmin権限を持たない(hash不一致の可能性もある)
    string constant ISSUER_NOT_ADMIN_ROLE = "3995:issuer not admin";
    /// @dev Error: 署名復元したアドレスがAdmin権限を持たない(hash不一致の可能性もある)
    string constant ISSUER_EXCEED_REGISTER_LIMIT = "3996:exceed issuer register limit";
    /// @dev Error: 発行者がDecurretでない
    string constant ISSUER_NOT_DECURRET = "3999:issuer not decurret";

    //
    // string constant ISSUER_END = "3999";

    //
    // 06000～06999: Account
    //
    // string constant ACCOUNT_BEGIN = "6000";
    /// @dev Error: 登録済みアカウントID
    string constant ACCOUNT_ID_EXIST = "6001:exist id";
    /// @dev Error: 未登録アカウントID
    string constant ACCOUNT_ID_NOT_EXIST = "6002:not exist";
    /// @dev Error: 登録済みアドレス
    string constant ACCOUNT_ADDR_EXIST = "6003:exist addr";
    /// @dev Error: 無効アカウント
    string constant ACCOUNT_DISABLED = "6004:disabled";
    /// @dev Error: ownerId未登録 approve用
    string constant OWNER_NOT_EXIST = "6005:owner not exist";
    /// @dev Error: spenerId未登録 approve用
    string constant SPENDER_NOT_EXIST = "6006:spender not exist";
    /// @dev Error: Allowance額が送金額に届いていない際のエラー
    string constant ALLOWANCE_NOT_ENOUGH = "6007:allowance not enough";
    /// @dev Error: balance残高不足
    string constant BALANCE_NOT_ENOUGH = "6008:balance not enough";
    /// @dev Error: 本人確認登録なし
    string constant ACCOUNT_NOT_IDENTIFIED = "6009:account not identified";
    /// @dev Error: 解約済アカウント
    string constant ACCOUNT_TERMINATED = "6010:terminated account";
    /// @dev Error: 解約時に残高あり
    string constant ACCOUNT_BALANCE_EXIST = "6011:balance exist account";
    /// @dev Error: アカウント解約時にアカウントが凍結状態でない
    string constant ACCOUNT_NOT_FROZEN = "6012:account not frozen";
    /// @dev Error: アカウント解約時にアカウントの残高が0でない
    string constant ACCOUNT_BALANCE_NOT_ZERO = "6013:account balance not zero";
    /// @dev Error: アカウントステータスが強制償却済みでない
    string constant ACCOUNT_NOT_FORCE_BURNED = "6014:account not force burned";
    /// @dev Error: 許可額の設定値が上限を超えている
    string constant ACCOUNT_EXCEED_APPROVAL_LIMIT = "6015:exceed approval limit";
    /// @dev Error: アカウントは既に解約申し込み中か、解約済み
    string constant ACCOUNT_TERMINATING_OR_TERINATED =
        "6016:account already termiating or terminated";
    /// @dev Error: アカウントステータスが凍結状態、強制償却済みでない
    string constant ACCOUNT_NOT_FROZEN_OR_FORCE_BURNED = "6016:account not frozen or force burned";
    /// @dev Error: 強制償却時にアカウントの残高が足りない
    string constant ACCOUNT_INVALID_BURNED_AMOUNT = "6018:invalid burned amount";
    /// @dev Error: 強制償却時にアカウントの残高が指定した償却額と一致しない
    string constant ACCOUNT_INVALID_BURNED_BALANCE = "6019:invalid burned balance";
    /// @dev Error: 送信元アカウントIDが不正
    string constant SEND_ACCOUNT_IS_INVALID_VALUE = "6200:send account is invalid value";
    /// @dev Error: 送金元アカウントIDが不正
    string constant FROM_ACCOUNT_IS_INVALID_VALUE = "6201:from account is invalid value";
    /// @dev Error: 送金先アカウントIDが不正
    string constant TO_ACCOUNT_IS_INVALID_VALUE = "6202:to account is invalid value";
    /// @dev Error: 送信元アカウントIDが未登録
    string constant SEND_ACCOUNT_IS_NOT_EXIST = "6203:send account is not exist";
    /// @dev Error: 送金元アカウントIDが未登録
    string constant FROM_ACCOUNT_IS_NOT_EXIST = "6204:from account is not exist";
    /// @dev Error: 送金先アカウントIDが未登録
    string constant TO_ACCOUNT_IS_NOT_EXIST = "6205:to account is not exist";
    /// @dev Error: 送信元アカウントIDが無効
    string constant SEND_ACCOUNT_STATUS_IS_DISABLED = "6206:send account status is disabled";
    /// @dev Error: 送金元アカウントIDが無効
    string constant FROM_ACCOUNT_STATUS_IS_DISABLED = "6207:from account status is disabled";
    /// @dev Error: 送金先アカウントIDが無効
    string constant TO_ACCOUNT_STATUS_IS_DISABLED = "6208:to account status is disabled";
    /// @dev Error: 送金元と送信先アカウントのイシュアーが異なる
    string constant FROM_AND_TO_ACCOUNT_ISSUERS_ARE_DIFFERENT =
        "6209:from and to account issuers are different";

    //
    /// @dev Error: 処理を実行する権限が無い
    string constant ACCOUNT_NOT_ROLE = "6101:not user";
    /// @dev Error: 範囲外を指定された
    string constant ACCOUNT_OUT_OF_INDEX = "6102:out of index";
    /// @dev Error: 処理を実行する権限が無い
    string constant ACCOUNT_PROV_NOT_ROLE = "6103:not prov";
    /// @dev Error: 一覧検索時のlimitとして最大取得件数を超える値が指定された
    string constant ACCOUNT_TOO_LARGE_LIMIT = "6104:too large limit";
    /// @dev Error: 一覧検索時のoffsetとして範囲外を指定された
    string constant ACCOUNT_OFFSET_OUT_OF_INDEX = "6105:out of index (offset)";
    /// @dev Error: 値が不正
    string constant ACCOUNT_INVALID_VAL = "6992:account invalid value";
    // TODO: overflow, underflowは言語仕様としてチェックされるので不要
    /// @dev Error: 加算オーバーフロー
    string constant ACCOUNT_OVERFLOW = "6993:account overflow";
    /// @dev Error: 減算オーバーフロー
    string constant ACCOUNT_UNDERFLOW = "6994:account underflow";
    /// @dev Error: アカウント登録限界数
    string constant ACCOUNT_EXCEED_REGISTER_LIMIT = "6995:exceed account register limit";
    /// @dev Error: Accountの署名が不正
    string constant ACCOUNT_BAD_SIG = "6996:bad account sig";
    /// @dev Error: パラメータが存在しない。
    string constant ACCOUNT_INVALID_SIG = "6997:bad account sig";
    /// @dev Error: Adminでない
    string constant ACCOUNT_NOT_ADMIN = "6998:account not admin";
    /// @dev Error: Accountのstatusが不正
    string constant ACCOUNT_STATUS_INVALID = "20004:account status is invalid";
    //
    // string constant ACCOUNT_END = "6999";

    //
    // 07000～07999: Token
    //
    // string constant TOKEN_BEGIN = "7000";
    /// @dev Error: 登録済みトークンID
    string constant TOKEN_ID_EXIST = "7001:exist token id";
    /// @dev Error: 未登録トークンID
    string constant TOKEN_ID_NOT_EXIST = "7002:token id not exist";
    /// @dev Error: 登録済みアドレス
    string constant TOKEN_ADDR_EXIST = "7003:exist token addr";
    /// @dev Error: 無効トークン
    string constant TOKEN_DISABLED = "7004:token disabled";
    /// @dev Error: balance残高不足
    string constant TOKEN_BALANCE_NOT_ENOUGH = "7006:balance not enough";
    /// @dev Error: Tokenでの無効ユーザ
    string constant TOKEN_ACCOUNT_DISABLED = "7007:disabled account";
    /// @dev Error: Tokenでの本人未確認ユーザ
    string constant TOKEN_ACCOUNT_UNIDENTIFIED = "7008:not identified account";
    /// @dev Error: approve関係の失敗
    string constant TOKEN_APPROVE = "7009:approve";
    /// @dev Error: トークンIDの発行者ではない
    string constant TOKEN_ISSUER_UNKNOWN = "7010:unknown";
    /// @dev Error: 登録されているトークンIDではない
    string constant NOT_TOKEN_ID = "7011:not token id";
    /// @dev Error: 登録されているトークンIDではない
    string constant TOKEN_NOT_EXIST = "7011:token not exist";
    /// @dev Error: 送金金額が0
    string constant TOKEN_ZERO_AMOUNT = "7012:token zero amount";
    //
    /// @dev Error: 処理を実行する権限が無い
    string constant TOKEN_NOT_ROLE = "7101:not token";
    /// @dev Error: 範囲外を指定された
    string constant TOKEN_OUT_OF_INDEX = "7102:out of index";
    /// @dev Error: 処理を実行する権限が無い(issuer)
    string constant TOKEN_ISSUER_NOT_ROLE = "7103:not issuer";
    /// @dev Error: 処理を実行する権限が無い(currency)
    string constant TOKEN_CURR_NOT_ROLE = "7104:not currency";
    /// @dev Error: 呼び出し元がTokenコントラクトではない
    string constant NOT_TOKEN_CONTRACT = "7105:not token contract";
    /// @dev Error: 呼び出し元がAccountコントラクトではない
    string constant NOT_ACCOUNT_CONTRACT = "7106:not account contract";
    //
    /// @dev Error: 値が不正
    string constant TOKEN_INVALID_VAL = "7992:token invalid value";
    // TODO: overflow, underflowは言語仕様としてチェックされるので不要
    /// @dev Error: 加算オーバーフロー
    string constant TOKEN_OVERFLOW = "7993:token overflow";
    /// @dev Error: 減算オーバーフロー
    string constant TOKEN_UNDERFLOW = "7994:token underflow";

    //
    // string constant TOKEN_END = "7999";

    //
    // 08000～08999: Validator
    //
    // string constant VALID_BEGIN = "8000";
    /// @dev Error: 登録済み検証者ID
    string constant VALIDATOR_ID_EXIST = "8001:exist id";
    /// @dev Error: 未登録検証者ID
    string constant VALIDATOR_ID_NOT_EXIST = "8002:not exist";
    /// @dev Error: 登録済みアドレス
    string constant VALIDATOR_ADDR_EXIST = "8003:exist addr";
    /// @dev Error: 無効検証者
    string constant VALIDATOR_DISABLED = "8004:disabled";
    /// @dev Error: 検証者管理のアカウントIDが未登録
    string constant VALIDATOR_ACCOUNT_NOT_EXIST = "8005:validator account not exist";
    //
    /// @dev Error: 処理を実行する権限が無い
    string constant VALIDATOR_NOT_ROLE = "8101:not valid";
    /// @dev Error: 範囲外を指定された
    string constant VALIDATOR_OUT_OF_INDEX = "8102:out of index";
    /// @dev Error: 一覧検索時のlimitとして最大取得件数を超える値が指定された
    string constant VALIDATOR_TOO_LARGE_LIMIT = "8103:too large limit";
    /// @dev Error: 一覧検索時のoffsetとして範囲外を指定された
    string constant VALIDATOR_OFFSET_OUT_OF_INDEX = "8104:out of index (offset)";
    /// @dev Error: 呼び出し元がValidatorのコントラクトではない
    string constant NOT_VALIDATOR_CONTRACT = "8105:not validator contract";
    /// @dev Error: 値が不正
    string constant VALIDATOR_INVALID_VAL = "8992:validator invalid value";
    /// @dev Error: 署名復元したアドレスがAdmin権限を持たない(hash不一致の可能性もある)
    string constant VALIDATOR_NOT_ADMIN_ROLE = "8995:validator not admin";
    /// @dev Error: 署名復元したアドレスがAdmin権限を持たない(hash不一致の可能性もある)
    string constant VALIDATOR_EXCEED_REGISTER_LIMIT = "8996:exceed validator register limit";
    /// @dev Error: FromAccountとToAccountが同じ。
    string constant FROM_TO_SAME = "8999:from to are same";

    //
    // string constant VALID_END = "8999";

    //
    // 09000～09999: IBC
    //
    // string constant IBC_BEGIN = "9000";
    /// @dev Error: msg.senderが許可されていない
    string constant IBC_SENDER_NOT_AUTH = "9001:sender not authorized";
    /// @dev Error: パケットがタイムアウトした
    string constant IBC_PACKET_TIMED_OUT = "9002:packet timed out";
    /// @dev Error: パケットはすでに受信している
    string constant IBC_PACKET_ALWAYS_RECV = "9003:packet always received";
    /// @dev Error: msg.senderが許可されていない
    string constant NOT_IBC_CONTRACT = "9004:not ibc contract";
    //
    /// @dev Error: エスクローアカウントが登録されていない
    string constant IBC_APP_JPYTT_ESCROW_NOT_REG = "9101:escrow not registered";
    /// @dev Error: エスクローアカウントがすでに登録されている
    string constant IBC_APP_JPYTT_ESCROW_ALWAYS_REG = "9102:escrow always registered";
    /// @dev Error: 外部コントラクトのエラー
    string constant IBC_APP_JPYTT_EXTERNAL_CONTRACT_ERR = "9103:external contract error";
    /// @dev Error: 値が不正
    string constant IBC_INVALID_VAL = "9992:IBC invalid value";
    /// @dev Error: 署名復元したアドレスがAdmin権限を持たない(hash不一致の可能性もある)
    string constant IBC_NOT_ADMIN_ROLE = "9995:IBC not admin";
    /// @dev Error: 操作ゾーンが不適切(TODO: コード体系見直し時に再度採番)
    string constant FIN_ZONE_OP_NOT_ALLOWED = "09999:Fin zone operation denied";
    /// @dev Error: 設定するアカウントステータスが不適切(TODO: コード体系見直し時に再度採番)
    string constant INVALID_ACCOUNT_STATUS = "09999:Invalid account status";
    /// @dev Error: パケット送信元のゾーンが不適切(TODO: コード体系見直し時に再度採番)
    string constant NOT_ALLOWED_FROM_FIN_ZONE = "09999:Not allowed from financial zone";

    //
    // string constant VALID_END = "9999";

    // 10000～10999: TransferProxy
    //
    // string constant TRANSFERPROXY_BEGIN = "10000";
    /// @dev Error: CustomContractのAddressが未登録である
    string constant CUSTOM_CONTRACT_NOT_EXIST = "10001:custom contract not exist";
    /// @dev Error: CustomContractが既に登録されている
    string constant CUSTOM_CONTRACT_EXIST = "10002:custom contract already exist";
    //
    // string constant TRANSFERPROXY_END = "10999";

    // 11000～11999: FinancialToken TODO エラーコード整理
    //
    // string constant FINANCIAL_BEGIN = "11000";
    /// @dev Error: 登録済みRegion Id
    string constant REGION_IS_EXIST = "11001:region is exist";
    /// @dev Error: 値が不正
    string constant REGION_INVALID_VAL = "11002:region invalid val";
    /// @dev Error: FinancialAccountコントラクトからの呼び出しではない
    string constant NOT_FINANCIAL_ACCOUNT = "11003:not financial account";
    /// @dev Error: FinancialTokenコントラクトからの呼び出しではない
    string constant NOT_FINANCIAL_TOKEN = "11004:not financial token";
    /// @dev Error: limitAmountsが要素数設定されていない
    string constant LIMIT_AMOUNT_INVALID_COUNT = "11005:limitAmount invalid count";
    /// @dev Error: itemFlgsが要素数設定されていない
    string constant ITEM_FLG_INVALID_COUNT = "11006:itemFlgs invalid count";
    /// @dev Error: 付加領域からの呼び出しではない
    string constant NOT_INDUSTRY_REGION = "11007:not industry region";
    /// @dev Error: 登録済みRegion Id
    string constant NOT_STREAM_ADDRESS = "11010:not stream";
    /// @dev Error: DAILYの上限を超えている。
    string constant EXCEEDED_DAILY_LIMIT = "11014:exceeded daily limit";
    /// @dev Error: MINTの上限を超えている。
    string constant EXCEEDED_MINT_LIMIT = "11015:exceeded mint limit";
    /// @dev Error: BURNの上限を超えている。
    string constant EXCEEDED_BURN_LIMIT = "11016:exceeded burn limit";
    /// @dev Error: TRANSFERの上限を超えている。
    string constant EXCEEDED_TRANSFER_LIMIT = "11017:exceeded transfer limit";
    /// @dev Error: CHARGEの上限を超えている。
    string constant EXCEEDED_CHARGE_LIMIT = "11018:exceeded charge limit";
    /// @dev Error: FinancialValidatorコントラクトからの呼び出しではない
    string constant NOT_FINANCIAL_VALIDATOR = "11009:not financial validator";
    /// @dev Error: fromAccount, toAccount, amountの配列の要素数不一致
    string constant FROM_TO_AMOUNT_NOT_SAME_COUNT = "11020:from to amount are not same count";
    /// @dev Error: DISCHARGEの上限を超えている。
    string constant EXCEEDED_DISCHARGE_LIMIT = "11021:exceeded discharge limit";
    /// @dev Error: MINT DAILYの上限を超えている。
    string constant EXCEEDED_DAILY_MINT_LIMIT = "11022:exceeded daily mint limit";
    /// @dev Error: BURN DAILYの上限を超えている。
    string constant EXCEEDED_DAILY_BURN_LIMIT = "11023:exceeded daily burn limit";
    /// @dev Error: CHARGE DAILYの上限を超えている。
    string constant EXCEEDED_DAILY_CHARGE_LIMIT = "11024:exceeded daily charge limit";
    /// @dev Error: DISCHARGE DAILYの上限を超えている。
    string constant EXCEEDED_DAILY_DISCHARGE_LIMIT = "11025:exceeded daily discharge limit";
    /// @dev Error: TRANSFER DAILYの上限を超えている。
    string constant EXCEEDED_DAILY_TRANSFER_LIMIT = "11026:exceeded daily transfer limit";

    // 12000～12999: RenewableEnergyToken
    //
    // string constant RenewableEnergyToken_BEGIN = "12000";
    /// @dev Error: 一覧検索時のlimitとして最大取得件数を超える値が指定された
    string constant RETOKEN_TOO_LARGE_LIMIT = "12100:retoken too large limit";
    /// @dev Error: 一覧検索時のoffsetとして範囲外を指定された
    string constant RETOKEN_OFFSET_OUT_OF_INDEX = "12101:retoken out of index (offset)";
    /// @dev Error: Token登録限界数
    string constant RETOKEN_EXCEED_REGISTER_LIMIT = "12102:exceed token register limit";
    /// @dev Error: Tokenが存在しない
    string constant RETOKEN_NOT_EXIST = "12103:retoken not exist";
    /// @dev Error: Tokenを所有していない
    string constant RETOKEN_NOT_OWNER = "12104:retoken not owner";
    /// @dev Error: TokenのステータスがActiveでない
    string constant RETOKEN_NOT_ACTIVE = "12105:retoken not active";
    /// @dev Error: Tokenがロックされている
    string constant RETOKEN_IS_LOCKED = "12106:retoken is locked";
    /// @dev Error: FromAccouontIdとToAccountIdが同一
    string constant RETOKEN_FROM_TO_ARE_SAME = "12108:retoken from to are same";
    /// @dev Error: 値が不正
    string constant RETOKEN_INVALID_VAL = "12109:retoken invalid val";
    /// @dev Error: すでにTokenが登録済み
    string constant RETOKEN_ID_EXIST = "12109:retoken id exist";
    /// @dev Error: 不正なmiscValue1
    string constant RETOKEN_INVALID_MISC1 = "12110:retoken invalid misc1";
    /// @dev Error: 不正なmiscValue2
    string constant RETOKEN_INVALID_MISC2 = "12111:retoken invalid misc2";
    // string constant RenewableEnergyToken_END = "12999";

    // 20000~20999: 汎用的なバリデーションエラー
    /// @dev Error: organizationId(issuerId/validatorId)が無効な値
    string constant INVALID_ORGANIZATION_ID = "20001:organization id is invalid";
    /// @dev Error: accountIdが無効な値
    string constant INVALID_ACCOUNT_ID = "20002:account id is invalid";
    /// @dev Error: コントラクトアドレスが未登録
    string constant CONTRACT_ADDRESS_NOT_EXIST = "20003:contract address not exist";
    /// @dev Error: 呼び出し元のコントラクトが誤り
    string constant INVALID_CALLER_ADDRESS = "20004:caller is different";
    // solhint-enable state-visibility
}
