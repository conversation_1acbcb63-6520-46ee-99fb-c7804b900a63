// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "./Struct.sol";

/**
 * @dev Issuerインターフェース
 */
interface IIssuer {
    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    /**
     * @dev
     * 発行者を追加する。
     * EOAはaddIssuerRole()で登録する。
     * ```
     * emit event: AddIssuer()
     * ```
     *
     * signature用hash: `keccak256(abi.encode(issuerId, name, deadline))`
     *
     *      <li>signatureがAdmin登録されたEOAのものである
     *      <li>deadlineが期限内
     *      <li>issuerIdが未登録
     *      <li>issuerIdが0以外
     * @param issuerId 発行者ID
     * @param bankCode 金融機関コード
     * @param name 発行者名
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addIssuer(
        bytes32 issuerId,
        uint16 bankCode,
        string memory name,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev
     * 発行者権限を紐づくアドレスに追加する。
     * ```
     * emit event: AddIssuerRole()
     * ```
     *
     * signature用hash: `keccak256(abi.encode(issuerId, issuerEoa, deadline))`
     *
     *      <li>signatureがAdmin登録されたEOAのものである
     *      <li>deadlineが期限内
     *      <li>issuerIdが登録済み
     *      <li>issuerEoaが0以外
     * @param issuerId 発行者ID
     * @param issuerEoa 権限を付与するEOA
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addIssuerRole(
        bytes32 issuerId,
        address issuerEoa,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev IssuerIDにAccountIDを紐付ける
     * @param issuerId  発行者ID
     * @param accountId AccountID
     * @param traceId トレースID
     */
    function addAccountId(
        bytes32 issuerId,
        bytes32 accountId,
        bytes32 traceId
    ) external;

    /**
     * @dev
     * アカウントのRole追加する。
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param accountEoa 本人確認
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addAccountRole(
        bytes32 issuerId,
        bytes32 accountId,
        address accountEoa,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev アカウントの状態を更新する
     * _
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param accountStatus アカウントステータス
     * @param reasonCode 解約理由
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function setAccountStatus(
        bytes32 issuerId,
        bytes32 accountId,
        bytes32 accountStatus,
        bytes32 reasonCode,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev
     * 発行者を更新する。
     * nameを指定しない(name=0x00を指定した)場合はその項目の値を更新しない
     * ```
     * emit event: ModIssuer()
     * ```
     * signature用hash: `keccak256(abi.encode(issuerId, name, deadline))`
     *      <li>signatureがAdmin登録されたEOAのものである
     *      <li>deadlineが期限内
     *      <li>issuerIdが登録済み
     * @param issuerId 発行者ID
     * @param name 発行者名
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function modIssuer(
        bytes32 issuerId,
        string memory name,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev アカウントを強制償却させる
     * AccountコントラクトのforceBurnを呼び出す。
     *
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param traceId traceId
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function forceBurn(
        bytes32 issuerId,
        bytes32 accountId,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev アカウントを部分的に強制償却させる
     * AccountコントラクトのpartialForceBurnを呼び出す。
     *
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param burnedAmount 償却する金額
     * @param burnedBalance 償却後に残す金額
     * @param traceId traceId
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function partialForceBurn(
        bytes32 issuerId,
        bytes32 accountId,
        uint256 burnedAmount,
        uint256 burnedBalance,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external;

    function setIssuerAll(
        IssuerAll memory issuer,
        uint256 deadline,
        bytes memory signature
    ) external;

    function addBizZoneToIssuer(
        bytes32 issuerId,
        uint16 zoneId,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external;

    function deleteBizZoneToIssuer(
        bytes32 issuerId,
        uint16 zoneId,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external;

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev (内部用)発行者IDが登録済であるか確認する。
     * @param issuerId 発行者ID
     * @return success true:発行者IDが登録済み false:発行者IDが登録済みでない
     * @return err エラー内容
     *      <li>ISSUER_INVALID_VAL:ID値不正
     *      <li>ISSUER_ID_NOT_EXIST:未登録
     */
    function hasIssuer(bytes32 issuerId) external view returns (bool success, string memory err);

    /**
     * @dev 発行者情報リストを取得する。
     * @param limit 取得件数
     * @param offset インデックス開始位置、0始まり
     * @return issuers 発行者リスト(IDとNAME)
     * @return totalCount 発行者情報の総件数
     * @return err bytes(err).length!=0の場合、エラー有り(その他の戻り値は無効)
     */
    function getIssuerList(uint256 limit, uint256 offset)
        external
        view
        returns (
            IssuerListData[] memory issuers,
            uint256 totalCount,
            string memory err
        );

    /**
     * @dev AccountのListを取得する
     */
    function getAccount(bytes32 issuerId, bytes32 accountId)
        external
        view
        returns (
            string memory accountName,
            uint256 balance,
            bytes32 accountStatus,
            bytes32 reasonCode,
            string memory err
        );

    /**
     * @dev AccountのListを取得する
     */
    function getAccountList(
        bytes32 issuerId,
        bytes32[] memory inAccountIds,
        uint256 limit,
        uint256 offset
    )
        external
        view
        returns (
            IssuerAccountsData[] memory accounts,
            uint256 totalCount,
            string memory err
        );

    /**
     * @dev IssuerがAccountと紐付いているか確認を行う。
     * @param issuerId    発行者ID
     * @param accountId   AccountID
     * @return success true:IssuerがAccountと紐づいている, false:IssuerがAccountと紐づいていない
     * @return err        エラー内容
     *      <li>ISSUER_INVALID_VAL:ID値不正
     *      <li>ISSUER_ID_NOT_EXIST:Issuerが未登録
     *      <li>ISSUER_DISABLED:無効(chkEnabled=true時)
     *      <li>ACCOUNT_ID_NOT_EXIST:Accountが未登録
     */
    function hasAccount(bytes32 issuerId, bytes32 accountId)
        external
        view
        returns (bool success, string memory err);

    /**
     * @dev 発行者情報を取得する。
     * @param issuerId 発行者ID
     * @return name 発行者名
     * @return bankCode 金融機関コード
     * @return err 空文字列では無い場合、エラー有り(その他の戻り値は無効)
     */
    function getIssuer(bytes32 issuerId)
        external
        view
        returns (
            string memory name,
            uint16 bankCode,
            string memory err
        );

    /**
     * @dev 発行者数を取得する。
     * @return count 登録されている発行者数(無効発行者を含む)
     */
    function getIssuerCount() external view returns (uint256 count);

    /**
     * @dev 発行者IDを取得する。
     * @param index 取得する発行者IDのインデックス。0始まり。
     * @return issuerId 発行者ID
     * @return err 空文字列では無い場合、エラー有り(その他の戻り値は無効)
     */
    function getIssuerId(uint256 index) external view returns (bytes32 issuerId, string memory err);

    /**
     * @dev (内部用)発行者の権限をチェックする。
     *      発行者IDの有効性チェックは行わない。
     * @param issuerId 発行者ID
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     * @return has true=権限あり
     * @return err 空文字列では無い場合、エラー有り(その他の戻り値は無効)
     */
    function checkRole(
        bytes32 issuerId,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external view returns (bool has, string memory err);

    /**
     * @dev Accountの限度額を更新する
     * @param issuerId 発行者ID
     * @param accountId 更新対象アカウントID
     * @param limitUpdates アカウント限度額の更新フラグ
     * @param limitValues アカウントの限度額値
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     */
    function modTokenLimit(
        bytes32 issuerId,
        bytes32 accountId,
        AccountLimitUpdates memory limitUpdates,
        AccountLimitValues memory limitValues,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev Accountの累積限度額初期化
     * @param issuerId 発行者ID
     * @param accountId 更新対象アカウントID
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     */
    function cumulativeReset(
        bytes32 issuerId,
        bytes32 accountId,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @param index index
     */
    function getIssuerAll(uint256 index) external view returns (IssuerAll memory issuer);

    ///////////////////////////////////
    // events
    ///////////////////////////////////

    /**
     * @dev Issuer追加時に出力されるEvent
     * @param issuerId 発行者ID
     * @param bankCode 金融機関コード
     * @param name 発行者名
     * @param traceId トレースID
     */
    event AddIssuer(
        bytes32 indexed issuerId,
        uint16 indexed bankCode,
        string name,
        bytes32 traceId
    );

    /**
     * @dev Issuerに対しRoleを付与した際のEvent
     * @param issuerId 発行者ID
     * @param issuerEoa EOA
     * @param traceId トレースID
     */
    event AddIssuerRole(bytes32 indexed issuerId, address issuerEoa, bytes32 traceId);

    /**
     * @dev Issuerを有効か無効にした際のEvent
     * @param issuerId 発行者ID
     * @param traceId トレースID
     */
    event IssuerEnabled(bytes32 indexed issuerId, bytes32 traceId);

    /**
     * @dev IssuerのStatusが変更された際のEvent
     * @param issuerId 発行者ID
     * @param name 発行者名
     * @param traceId トレースID
     */
    event ModIssuer(bytes32 indexed issuerId, string name, bytes32 traceId);

    /**
     * @dev Issuerによるアカウントの追加
     * @param issuerId 検証者ID
     * @param accountId アカウントID
     * @param traceId トレースID
     */
    event AddAccountByIssuer(bytes32 indexed issuerId, bytes32 accountId, bytes32 traceId);

    /**
     * @dev Accountの限度額を更新する
     * @param validatorId 検証者ID
     * @param accountId 更新対象アカウントID
     * @param limitUpdates アカウント限度額の更新フラグ
     * @param limitValues アカウントの限度額値
     * @param traceId トレースID
     */
    event ModTokenLimit(
        bytes32 indexed validatorId,
        bytes32 indexed accountId,
        AccountLimitUpdates limitUpdates,
        AccountLimitValues limitValues,
        bytes32 traceId
    );

    /**
     * @dev Accountの累積限度額初期化
     * @param issuerId 検証者ID
     * @param accountId 更新対象アカウントID
     * @param traceId トレースID
     * @param validatorId 検証者ID
     */
    event CumulativeReset(
        bytes32 issuerId,
        bytes32 accountId,
        bytes32 traceId,
        bytes32 validatorId
    );

    /**
     * @dev Biz ZoneとIssuerの紐付け。
     * @param issuerId 検証者ID
     * @param zoneId ゾーンID
     * @param traceId トレースID
     */
    event AddBizZoneToIssuer(bytes32 issuerId, uint16 zoneId, bytes32 traceId);

    /**
     * @dev Biz ZoneとIssuerの紐付け削除。
     * @param issuerId 検証者ID
     * @param zoneId ゾーンID
     * @param traceId トレースID
     */
    event DeleteBizZoneToIssuer(bytes32 issuerId, uint16 zoneId, bytes32 traceId);
}
