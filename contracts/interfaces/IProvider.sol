// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;
import "./Struct.sol";

/**
 * @dev Providerインターフェース
 */
interface IProvider {
    ///////////////////////////////////

    // send functions
    ///////////////////////////////////

    /**
     * @dev
     * プロバイダの追加。
     * 追加後はenabled状態となる。
     * まずプロバイダID,プロバイダ名のみを追加し、addProviderRole()で権限を持たせるEOAを設定する。
     * ```
     * emit event: AddProvider()
     * ```
     *
     * signature用hash: `keccak256(abi.encode(providerId, name, zoneId, deadline))`
     *
     *      <li>signatureがAdmin登録されたEOAのものである
     *      <li>deadlineが期限内
     *      <li>providerIdが未登録
     *      <li>providerIdが0以外
     * @param providerId プロバイダID
     * @param zoneId 領域ID (デジタル通貨区分)
     * @param zoneName 領域名 (デジタル通貨区分)
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addProvider(
        bytes32 providerId,
        uint16 zoneId,
        string memory zoneName,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev (Fin専用)Finで管理を行うZone情報の追加。Adminの権限が必要。
     * ```
     * emit event: AddBizZone()
     * ```
     *
     * signature用hash: `keccak256(abi.encode(zoneId, zoneName, deadline))`
     *
     *      <li>signatureがAdmin登録されたEOAのものである
     *      <li>deadlineが期限内
     *      <li>providerIdが未登録
     * @param zoneId 領域ID (デジタル通貨区分)
     * @param zoneName 領域名 (デジタル通貨区分)
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addBizZone(
        uint16 zoneId,
        string memory zoneName,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev プロバイダ権限アドレス追加。
     * ```
     * emit event: AddProviderRole()
     * ```
     *
     * signature用hash: `keccak256(abi.encode(providerId, providerEoa, deadline))`
     *
     *      <li>signatureがAdmin登録されたEOAのものである
     *      <li>deadlineが期限内
     *      <li>providerIdが登録済み & enabled=true
     *      <li>providerEoaが0以外
     * @param providerId プロバイダID
     * @param providerEoa 権限を付与するEOA
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addProviderRole(
        bytes32 providerId,
        address providerEoa,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev
     * プロバイダの更新。
     * name, zoneIdに空文字を指定した場合はその項目の値を更新しない
     * ```
     * emit event: ModProvider()
     * ```
     *
     * signature用hash: `keccak256(abi.encode(providerId, name, zoneId, deadline))`
     *
     *      <li>signatureがAdmin登録されたEOAのものである
     *      <li>deadlineが期限内
     *      <li>providerIdが登録済み
     * @param providerId プロバイダID
     * @param name プロバイダ名
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function modProvider(
        bytes32 providerId,
        bytes32 name,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * トークン登録
     */
    function addToken(
        bytes32 providerId,
        bytes32 tokenId,
        bytes32 name,
        bytes32 symbol,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * トークン更新
     */
    function modToken(
        bytes32 tokenId,
        bytes32 name,
        bytes32 symbol,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external;

    function addBizZoneToIssuer(bytes32 issuerId, uint16 zoneId) external;

    function deleteBizZoneToIssuer(bytes32 issuerId, uint16 zoneId) external;

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev (内部用)プロバイダID登録済み情報取得
     * @param providerId プロバイダID
     * @return success true:プロパイダIDが登録済み, false:プロバイダIDが登録済みでない
     * @return err
     *      <li>PROV_INVALID_VAL:ID値不正
     *      <li>PROV_ID_NOT_EXIST:未登録
     *      <li>PROV_DISABLED:無効(chkEnabled=true時)
     */
    function hasProvider(bytes32 providerId)
        external
        view
        returns (bool success, string memory err);

    /**
     * @dev プロバイダ情報を取得する。
     * @return providerId プロバイダID
     * @return zoneId 領域ID (デジタル通貨区分)
     * @return zoneName 領域名 (デジタル通貨区分)
     * @return err bytes(err).length!=0の場合、エラー有り(その他の戻り値は無効)
     */
    function getProvider()
        external
        view
        returns (
            bytes32 providerId,
            uint16 zoneId,
            string memory zoneName,
            string memory err
        );

    /**
     * @dev ZoneIDを取得する。
     * @return zoneId 領域ID (デジタル通貨区分)
     * @return zoneName 領域名 (デジタル通貨区分)
     * @return err bytes(err).length!=0の場合、エラー有り(その他の戻り値は無効)
     */
    function getZone()
        external
        view
        returns (
            uint16 zoneId,
            string memory zoneName,
            string memory err
        );

    /**
     * @dev zone名称の取得。
     *
     * @return zoneName zoneName
     */
    function getZoneName(uint16 zoneId) external view returns (string memory zoneName);

    /**
     * @dev TokenIdを取得する。
     * @return tokenId トークンID (デジタル通貨区分)
     * @return err bytes(err).length!=0の場合、エラー有り(その他の戻り値は無効)
     */
    function getTokenId() external view returns (bytes32 tokenId, string memory err);

    /**
     * @dev TokenIDを取得する
     */
    function getToken(bytes32 providerId)
        external
        view
        returns (
            bytes32 tokenId,
            bytes32 name,
            bytes32 symbol,
            uint256 totalSupply,
            bool enabled,
            string memory err
        );

    /**
     * @dev プロバイダ数数取得。
     * @return count 登録されているプロバイダ数(無効プロバイダを含む)
     */
    function getProviderCount() external view returns (uint256 count);

    /**
     * @dev
     * (内部用)プロバイダ権限チェック。
     * providerIdのenabledチェックは行わない。
     *
     * @param providerId プロバイダID
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     * @return has true=権限あり
     * @return err 空文字列では無い場合、エラー有り(その他の戻り値は無効)
     */
    function checkRole(
        bytes32 providerId,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external view returns (bool has, string memory err);

    function getAvailableIssuerIds(uint16 zoneId)
        external
        view
        returns (bytes32[] memory availableIssuerIds);

    function checkAvailableIssuerIds(uint16 zoneId, bytes32 accountId)
        external
        view
        returns (bool success, string memory err);

    function setProviderAll(
        ProviderAll memory provider,
        uint256 deadline,
        bytes memory signature
    ) external;

    function getProviderAll(bytes32 providerId) external view returns (ProviderAll memory provider);

    ///////////////////////////////////
    // events
    ///////////////////////////////////

    /**
     * @dev Providerが追加された際のEvent
     * @param providerId プロバイダID
     * @param zoneId 領域ID (デジタル通貨区分)
     * @param zoneName 領域名 (デジタル通貨区分)
     */
    event AddProvider(bytes32 indexed providerId, uint16 zoneId, string zoneName, bytes32 traceId);

    /**
     * @dev Zoneが追加された際のEvent(Fin専用)
     * @param zoneId 領域ID (デジタル通貨区分)
     * @param zoneName 領域名 (デジタル通貨区分)
     */
    event AddBizZone(uint16 zoneId, string zoneName);

    /**
     * @dev Providerに対しRoleを付与した際のEvent
     * @param providerId プロバイダID
     * @param providerEoa プロバイダEOA
     */
    event AddProviderRole(bytes32 indexed providerId, address providerEoa, bytes32 traceId);

    /**
     * @dev Providerを有効か無効にした際のEvent
     * @param providerId プロバイダID
     * @param enabled true=有効化, false=無効化
     */
    event ProviderEnabled(bytes32 indexed providerId, bool enabled, bytes32 traceId);

    /**
     * @dev ProviderのStatusが変更された際のEvent
     * @param providerId プロバイダID
     * @param name プロバイダ名
     */
    event ModProvider(bytes32 indexed providerId, bytes32 name, bytes32 traceId);

    /**
     * @dev Zoneの名称を変更する
     * @param providerId プロバイダID
     * @param zoneName ゾーン名
     * @param traceId トレースID
     */
    event ModZone(bytes32 providerId, string zoneName, bytes32 traceId);

    /**
     * @dev Providerに対し、TokenIDをセットした際のEvent
     * @param providerId プロバイダID
     * @param tokenId トークンID
     */
    event SetTokenIdByProvider(bytes32 indexed providerId, bytes32 tokenId, bytes32 traceId);

    /**
     * @dev ProviderによるToken追加
     * @param providerId プロバイダID
     * @param tokenId トークンID
     */
    event AddTokenByProvider(bytes32 indexed providerId, bytes32 tokenId, bytes32 traceId);
}
