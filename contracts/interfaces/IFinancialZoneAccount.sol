// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "./Struct.sol";

/**
 * @dev Accountコントラクト
 */
interface IFinancialZoneAccount {
    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    // Account限度額設定 TODO:CoreAPIマッピングとの整合性確認時に作成
    function addAccountLimit(
        bytes32 accountId,
        AccountLimitValues memory limitValues,
        bytes32 traceId
    ) external;

    // Account限度額更新
    function modAccountLimit(
        bytes32 accountId,
        AccountLimitUpdates memory limitUpdates,
        AccountLimitValues memory limitValues
    ) external returns (AccountLimitValues memory);

    // Account累積限度額初期化
    function cumulativeReset(bytes32 accountId) external;

    // cumulativeAmountリセット
    function syncCumulativeReset(bytes32 accountId, bytes32 traceId) external;

    // cumulativeAmount加算
    function addCumlativeAmount(
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external returns (uint256 cumulativeDate, uint256 cumulativeAmount);

    // cumulativeAmountを減算
    function subtractCumulativeAmount(
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external returns (uint256 cumulativeDate, uint256 cumulativeAmount);

    // mint額を限度額に反映させる
    function syncMint(
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external;

    // burn額を限度額に反映させる
    function syncBurn(
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external;

    // chargeされた額のamountを限度額に反映させる
    function syncCharge(
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external;

    // dischargeされた額のamountを限度額に反映させる
    function syncDischarge(
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external;

    // transferされた額のamountを限度額に反映させる
    function syncTransfer(
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external;

    // 指定されたAcountIdsに紐づくFinancialZoneAccounts情報を登録、もしくは上書きする
    function setFinAccountAll(
        FinancialZoneAccountsAll memory finAccount,
        uint256 deadline,
        bytes memory signature
    ) external;

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    // Account限度額取得
    function getAccountLimitData(bytes32 accountId)
        external
        view
        returns (FinancialZoneAccountData memory accountLimitData, string memory err);

    // 累積限度額チェック
    function checkTransactionLimits(
        bytes32 accountId,
        uint256 amount,
        bytes32 operationType
    ) external view returns (bool success, string memory err);

    // MintLimitチェック
    function checkMint(bytes32 accountId, uint256 amount)
        external
        view
        returns (bool success, string memory err);

    // BurnLimitチェック
    function checkBurn(bytes32 accountId, uint256 amount)
        external
        view
        returns (bool success, string memory err);

    // TransferLimitチェック
    function checkTransfer(bytes32 accountId, uint256 amount)
        external
        view
        returns (bool success, string memory err);

    // ChargeLimitチェック
    function checkCharge(bytes32 accountId, uint256 amount)
        external
        view
        returns (bool success, string memory err);

    // DischargeLimitチェック
    function checkDischarge(bytes32 accountId, uint256 amount)
        external
        view
        returns (bool success, string memory err);

    // UTCからJSTの0時へ変換
    function getJSTDay() external view returns (uint256);

    // TimestampをJSTの0時へ変換
    function convertJSTDay(uint256 timestamp) external view returns (uint256);

    // AccountIdに紐づくFinancialZoneAccounts情報を取得
    function getFinAccountAll(uint256 index)
        external
        view
        returns (FinancialZoneAccountsAll memory finAccount);

    // AccountLimitData追加
    event AddAccountLimit(bytes32 accountId, AccountLimitValues limitValues, bytes32 traceId);
    // cumulative amount初期化
    event SyncCumulativeReset(bytes32 accountId, bytes32 traceId);
    // cumulative amount加算
    event AddCumulativeAmount(
        bytes32 accountId,
        uint256 amount,
        uint256 cumulativeDate,
        uint256 cumulativeAmount,
        bytes32 traceId
    );
    // cumulative amount減算
    event SubtractCumulativeAmount(
        bytes32 accountId,
        uint256 amount,
        uint256 cumulativeDate,
        uint256 cumulativeAmount,
        bytes32 traceId
    );
    // mintによる限度額更新
    event SyncMint(
        bytes32 accountId,
        uint256 amount,
        uint256 cumulativeDate,
        uint256 cumulativeAmount,
        uint256 cumulativeMintAmount,
        bytes32 traceId
    );
    // burnによる限度額更新
    event SyncBurn(
        bytes32 accountId,
        uint256 amount,
        uint256 cumulativeDate,
        uint256 cumulativeAmount,
        uint256 cumulativeBurnAmount,
        bytes32 traceId
    );
    // chargeによる限度額更新
    event SyncCharge(
        bytes32 accountId,
        uint256 amount,
        uint256 cumulativeDate,
        uint256 cumulativeAmount,
        uint256 cumulativeChargeAmount,
        bytes32 traceId
    );
    // dischargeによる限度額更新
    event SyncDischarge(
        bytes32 accountId,
        uint256 amount,
        uint256 cumulativeDate,
        uint256 cumulativeAmount,
        uint256 cumulativeDischargeAmount,
        bytes32 traceId
    );
    // transferによる限度額更新
    event SyncTransfer(
        bytes32 accountId,
        uint256 amount,
        uint256 cumulativeDate,
        uint256 cumulativeAmount,
        uint256 cumulativeTransferAmount,
        bytes32 traceId
    );
}
