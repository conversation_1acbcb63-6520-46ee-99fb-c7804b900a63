// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "@openzeppelin/contracts-upgradeable/utils/math/SafeMathUpgradeable.sol";

import "../interfaces/IContractManager.sol";
import "../interfaces/IToken.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

library TokenLib {
    /* @dev FinZoneのID */
    uint16 private constant _FINANCIAL_ZONE = 3000;

    /** @dev 口座の開設状況が初期化済みであることを表す固定値 **/
    uint256 private constant _STATE_CODE_INITIALIZED = 0;
    /** @dev バリデーション用のステータス値(解約済) */
    bytes32 private constant _STATUS_TERMINATED = "terminated";

    using SafeMathUpgradeable for uint256;

    function checkAccountTermination(address contractManagerAddr, bytes32 accountId) public view {
        IContractManager contractManager = IContractManager(contractManagerAddr);
        bool terminated;
        string memory err;

        (terminated, err) = contractManager.account().isTerminated(accountId);
        require(bytes(err).length == 0, err);
        require(!terminated, Error.ACCOUNT_TERMINATED);
    }

    /**
     * @dev Token情報を取得する
     *
     * @param tokenMapping トークンデータを保存するマッピング
     * @param key マッピングのキーとなるトークンID
     * @return name トークンIDに紐づくトークンの名
     * @return symbol トークンIDに紐づくトークンのsymbol
     * @return totalSupply トークンIDに紐づくトークンの総供給量
     * @return enabled トークンIDに紐づくture:有効,false:無効
     * @return err エラーメッセージ
     */
    function getToken(mapping(bytes32 => TokenData) storage tokenMapping, bytes32 key)
        external
        view
        returns (
            bytes32 name,
            bytes32 symbol,
            uint256 totalSupply,
            bool enabled,
            string memory err
        )
    {
        return (
            tokenMapping[key].name,
            tokenMapping[key].symbol,
            tokenMapping[key].totalSupply,
            tokenMapping[key].enabled,
            ""
        );
    }

    /**
     * @dev ビジネスゾーンの全残高情報を取得する
     *
     * @param accountId accountId
     * @return zoneIds ビジネスゾーンID
     * @return zoneNames ゾーン名
     * @return balances 残高
     * @return accountNames アカウント名
     * @return accountStatus アカウントステータス
     * @return totalBalance 合計残高
     */
    function getBalanceList(
        address contractManagerAddr,
        bytes32 accountId,
        ZoneData[] memory zones
    )
        external
        view
        returns (
            uint16[] memory zoneIds,
            string[] memory zoneNames,
            uint256[] memory balances,
            string[] memory accountNames,
            bytes32[] memory accountStatus,
            uint256 totalBalance
        )
    {
        IContractManager contractManager = IContractManager(contractManagerAddr);
        zoneIds = new uint16[](zones.length);
        zoneNames = new string[](zones.length);
        balances = new uint256[](zones.length);
        accountNames = new string[](zones.length);
        accountStatus = new bytes32[](zones.length);

        for (uint256 lp = 0; lp < zones.length; lp++) {
            uint16 zoneId = zones[lp].zoneId;

            BusinessZoneAccountData memory data = contractManager
                .businessZoneAccount()
                .getBusinessZoneAccount(zoneId, accountId);
            if (data.accountStatus != _STATUS_TERMINATED) {
                zoneIds[lp] = zoneId;
                zoneNames[lp] = contractManager.provider().getZoneName(zoneId);
                balances[lp] = data.balance;
                accountNames[lp] = data.accountName;
                accountStatus[lp] = data.accountStatus;
                totalBalance += data.balance;
            }
        }

        return (zoneIds, zoneNames, balances, accountNames, accountStatus, totalBalance);
    }

    /**
     * @dev トークンデータを追加する
     *
     * @param tokenMapping トークンデータを保存するマッピング
     * @param tokenId  マッピングのキーとなる追加対象のトークンID
     * @param storedTokenId _tokenIdとして宣言されているトークンID
     * @param name トークンの名前
     * @param symbol トークンのsymbol
     */
    function addToken(
        mapping(bytes32 => TokenData) storage tokenMapping,
        bytes32 tokenId,
        bytes32 storedTokenId,
        bytes32 name,
        bytes32 symbol
    ) external {
        // token登録チェック
        {
            require(tokenId != 0x00, Error.TOKEN_INVALID_VAL);
            require(storedTokenId == 0x00, Error.TOKEN_ID_EXIST);
        }
        bool enabled = true;
        tokenMapping[tokenId].name = name;
        tokenMapping[tokenId].symbol = symbol;
        tokenMapping[tokenId].enabled = enabled;
    }

    /**
     * @dev TotalSupplyを増額する
     *
     * @param tokenMapping トークンデータを保存するマッピング
     * @param key マッピングのキーとなるトークンID
     * @param amount Mintする数量
     */
    function addTotalSupply(
        mapping(bytes32 => TokenData) storage tokenMapping,
        bytes32 key,
        uint256 amount
    ) external {
        // OVERFLOW対策
        // TODO: 言語仕様としてチェックされるので不要
        require(
            tokenMapping[key].totalSupply + amount >= tokenMapping[key].totalSupply,
            Error.TOKEN_OVERFLOW
        );
        tokenMapping[key].totalSupply = tokenMapping[key].totalSupply.add(amount);
    }

    /**
     * @dev トークンデータを変更する
     *
     * @param tokenMapping トークンデータを保存するマッピング
     * @param key マッピングのキーとなるトークンID
     * @param tokenId トークンId
     * @param name トークンの名前
     * @param symbol トークンのsymbol
     */
    function modToken(
        mapping(bytes32 => TokenData) storage tokenMapping,
        bytes32 key,
        bytes32 tokenId,
        bytes32 name,
        bytes32 symbol
    ) external {
        // token登録チェック
        {
            require(key != 0x00, Error.TOKEN_INVALID_VAL);
            require(tokenId == key, Error.NOT_TOKEN_ID);
        }
        if (name != 0x00) {
            tokenMapping[key].name = name;
        }
        if (symbol != 0x00) {
            tokenMapping[key].symbol = symbol;
        }
    }

    /**
     * @dev Tokenのステータスを変更する。
     *
     * @param tokenMapping トークンデータを保存するマッピング
     * @param key マッピングのキーとなるトークンID
     * @param tokenId トークンId
     * @param enabled Tokenの有効性.true:有効,false:無効
     */
    function setTokenEnabled(
        mapping(bytes32 => TokenData) storage tokenMapping,
        bytes32 key,
        bytes32 tokenId,
        bool enabled
    ) external {
        // token登録チェック
        {
            require(tokenId == key, Error.TOKEN_ID_NOT_EXIST);
        }
        tokenMapping[key].enabled = enabled;
    }

    /**
     * @dev TotalSupply減額
     *
     * @param tokenMapping トークンデータを保存するマッピング
     * @param key マッピングのキーとなるトークンID
     * @param amount Burnする数量
     */
    function subTotalSupply(
        mapping(bytes32 => TokenData) storage tokenMapping,
        bytes32 key,
        uint256 amount
    ) external {
        // UNDERFLOW対策
        // TODO: 言語仕様としてチェックされるので不要
        require(tokenMapping[key].totalSupply >= amount, Error.TOKEN_UNDERFLOW);
        tokenMapping[key].totalSupply = tokenMapping[key].totalSupply.sub(amount);
    }

    /**
     * @dev Token有効性確認。
     *
     * @param tokenMapping トークンデータを保存するマッピング
     * @param tokenId コントラクトが保存するトークンID
     * @param chkTokenId 比較対象のトークンID
     * @param chkEnabled true:有効性確認を行う,false:有効性確認を行わない
     * @return success true:Tokenが有効,false:Tokenが無効
     * @return err エラーメッセージ
     */
    function hasToken(
        mapping(bytes32 => TokenData) storage tokenMapping,
        bytes32 tokenId,
        bytes32 chkTokenId,
        bool chkEnabled
    ) internal view returns (bool success, string memory err) {
        {
            if (chkTokenId == 0x00) {
                return (false, Error.TOKEN_INVALID_VAL);
            }
            if (tokenId == chkTokenId) {
                if (chkEnabled) {
                    return
                        (tokenMapping[chkTokenId].enabled)
                            ? (true, "")
                            : (false, Error.TOKEN_DISABLED);
                } else {
                    return (true, "");
                }
            }
            return (false, Error.TOKEN_ID_NOT_EXIST);
        }
    }

    /**
     * @dev 発行
     */
    function mint(
        address contractManagerAddr,
        mapping(bytes32 => TokenData) storage tokenData,
        bytes32 tokenId,
        bytes32 issuerId,
        bytes32 accountId,
        uint256 amount
    )
        external
        returns (
            uint16 zoneId,
            bytes32 validatorId,
            string memory accountName,
            uint256 balance
        )
    {
        IContractManager contractManager = IContractManager(contractManagerAddr);
        //Account、IssuerのID入力値チェック
        {
            require(issuerId != 0x00, Error.INVALID_ORGANIZATION_ID);
            require(accountId != 0x00, Error.INVALID_ACCOUNT_ID);
        }
        // zoneIdの取得
        (zoneId, , ) = contractManager.provider().getZone();

        tokenData[tokenId].totalSupply = tokenData[tokenId].totalSupply.add(amount);

        //Account増額
        balance = contractManager.account().mint(accountId, amount);

        //Accountに紐づくvalidatorIdを取得(bcmonitoringのフィルタリング用)
        (validatorId, ) = contractManager.account().getValidatorIdByAccountId(accountId);

        //Account名義返却のため、アカウント情報を取得
        (AccountDataWithoutZoneId memory accountData, ) = contractManager.account().getAccount(
            accountId
        );

        return (zoneId, validatorId, accountData.accountName, balance);
    }

    /**
     * @dev 償却
     */
    function burn(
        address contractManagerAddr,
        mapping(bytes32 => TokenData) storage tokenData,
        bytes32 tokenId,
        bytes32 issuerId,
        bytes32 accountId,
        uint256 amount
    )
        external
        returns (
            uint16 zoneId,
            bytes32 validatorId,
            string memory accountName,
            uint256 balance
        )
    {
        IContractManager contractManager = IContractManager(contractManagerAddr);
        // 入力される値と解約確認を行う(stack too deep回避のため解約確認以前をburn()側で実施)
        {
            require(issuerId != 0x00, Error.INVALID_ORGANIZATION_ID);
            require(accountId != 0x00, Error.INVALID_ACCOUNT_ID);
        }

        // zoneIdの取得
        (zoneId, , ) = contractManager.provider().getZone();

        //Account減額
        balance = contractManager.account().burn(accountId, amount);

        //TotalSupply減額
        tokenData[tokenId].totalSupply = tokenData[tokenId].totalSupply.sub(amount);

        //Accountに紐づくvalidatorIdを取得(bcmonitoringのフィルタリング用)
        (validatorId, ) = contractManager.account().getValidatorIdByAccountId(accountId);

        //Account名義返却のため、アカウント情報を取得
        (AccountDataWithoutZoneId memory accountData, ) = contractManager.account().getAccount(
            accountId
        );

        return (zoneId, validatorId, accountData.accountName, balance);
    }

    /**
     * @dev 償却取り消し
     */
    function burnCancel(
        address contractManagerAddr,
        mapping(bytes32 => TokenData) storage tokenData,
        BurnCancelData memory burnCancelData
    )
        external
        returns (
            uint16 zoneId,
            bytes32 validatorId,
            uint256 balance
        )
    {
        IContractManager contractManager = IContractManager(contractManagerAddr);
        string memory err;
        bool success;
        //Account、IssuerのID入力値チェック
        {
            require(burnCancelData.issuerId != 0x00, Error.TOKEN_INVALID_VAL); // TODO: エラーメッセージが不適切なので修正する
            require(burnCancelData.accountId != 0x00, Error.TOKEN_INVALID_VAL); // TODO: エラーメッセージが不適切なので修正する
        }
        {
            // 解約確認を行う
            checkAccountTermination(address(contractManager), burnCancelData.accountId);
        }
        //Issuer権限確認
        {
            bytes32 hash = keccak256(
                abi.encode(
                    burnCancelData.issuerId,
                    burnCancelData.accountId,
                    burnCancelData.amount,
                    burnCancelData.blockTimestamp,
                    burnCancelData.deadline
                )
            );
            (success, err) = contractManager.issuer().checkRole(
                burnCancelData.issuerId,
                hash,
                burnCancelData.deadline,
                burnCancelData.signature
            );
            require(bytes(err).length == 0, err);
            require(success, Error.ISSUER_NOT_ROLE);
        }
        //AccountID存在確認
        {
            (success, err) = contractManager.issuer().hasAccount(
                burnCancelData.issuerId,
                burnCancelData.accountId
            );
            require(success, err);
        }
        // zoneIdの取得
        (zoneId, , err) = contractManager.provider().getZone();
        require(bytes(err).length == 0, err);

        //TotalSupply増額
        require(
            tokenData[burnCancelData.tokenId].totalSupply + burnCancelData.amount >=
                tokenData[burnCancelData.tokenId].totalSupply,
            Error.TOKEN_OVERFLOW
        );
        tokenData[burnCancelData.tokenId].totalSupply = tokenData[burnCancelData.tokenId]
            .totalSupply
            .add(burnCancelData.amount);

        // 指定された数量のBurnをCancelする
        balance = contractManager.account().mint(burnCancelData.accountId, burnCancelData.amount);

        //Accountに紐づくvalidatorIdを取得(bcmonitoringのフィルタリング用)
        (validatorId, ) = contractManager.account().getValidatorIdByAccountId(
            burnCancelData.accountId
        );

        return (zoneId, validatorId, balance);
    }

    /**
     * @dev transferの実行
     */
    function transfer(address contractManagerAddr, TransferData memory data)
        external
        returns (TransferData memory)
    {
        IContractManager contractManager = IContractManager(contractManagerAddr);
        string memory err;
        {
            // sendAccountIdとfromAccountIdが異なる場合にAllowanceのチェックを行う。IBCから呼ばれる場合は通らない。
            if (data.sendAccountId != data.fromAccountId) {
                // Allowanceの設定額を確認する
                uint256 allowance;
                (allowance, , err) = contractManager.account().getAllowance(
                    data.fromAccountId,
                    data.sendAccountId
                );
                require(data.amount <= allowance, Error.ALLOWANCE_NOT_ENOUGH);
                // Allowanceの減額を行う
                contractManager.account().calcAllowance(
                    data.fromAccountId,
                    data.sendAccountId,
                    data.amount
                );
            }
        }
        // Event伝播用のaccount名義情報を取得し、上書きする
        AccountDataWithoutZoneId memory accountData;

        // 取引種別がchargeの場合
        if (data.transferType == Constant._CHARGE) {
            (accountData, ) = contractManager.account().getAccount(data.fromAccountId);
            data.fromAccountName = accountData.accountName;

            // fromAccountIdからフィルタリング用のvalidatorIdを取得
            (data.fromValidatorId, ) = contractManager.account().getValidatorIdByAccountId(
                data.fromAccountId
            );
        }
        // 取引種別がdischargeの場合
        else if (data.transferType == Constant._DISCHARGE) {
            (accountData, ) = contractManager.account().getAccount(data.toAccountId);
            data.toAccountName = accountData.accountName;

            // toAccountIdからフィルタリング用のvalidatorIdを取得
            (data.toValidatorId, ) = contractManager.account().getValidatorIdByAccountId(
                data.toAccountId
            );
        }
        // その他の取引種別の場合
        else {
            (accountData, ) = contractManager.account().getAccount(data.fromAccountId);
            data.fromAccountName = accountData.accountName;
            (accountData, ) = contractManager.account().getAccount(data.toAccountId);
            data.toAccountName = accountData.accountName;

            // fromAccountIdからフィルタリング用のvalidatorIdを取得
            (data.fromValidatorId, ) = contractManager.account().getValidatorIdByAccountId(
                data.fromAccountId
            );
            (data.toValidatorId, ) = contractManager.account().getValidatorIdByAccountId(
                data.toAccountId
            );
        }

        {
            // 送金を行う
            (data.fromAccountBalance, data.toAccountBalance) = contractManager
                .account()
                .calcBalance(data.fromAccountId, data.toAccountId, data.amount);

            // 取引種別がcharge, dischargeであれば、それぞれBizZone残高を取得する
            if (data.transferType == Constant._CHARGE || data.transferType == Constant._DISCHARGE) {
                BusinessZoneAccountData memory bizData = contractManager
                    .businessZoneAccount()
                    .getBusinessZoneAccount(
                        data.bizZoneId,
                        data.transferType == Constant._CHARGE
                            ? data.fromAccountId
                            : data.toAccountId
                    );
                data.businessZoneBalance = bizData.balance;
            }

            (data.zoneId, , err) = contractManager.provider().getZone();
            require(bytes(err).length == 0, err);

            //Accountに紐づくvalidatorIdを取得(bcmonitoringのフィルタリング用)
            (data.fromValidatorId, ) = contractManager.account().getValidatorIdByAccountId(
                data.fromAccountId
            );

            (data.toValidatorId, ) = contractManager.account().getValidatorIdByAccountId(
                data.toAccountId
            );

            return data;
        }
    }
}
