// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import {Height} from "./yuiContracts/proto/Client.sol";
import {Packet} from "./yuiContracts/proto/Channel.sol";
import {IBCAppBase} from "./yuiContracts/apps/commons/IBCAppBase.sol";
import {IIBCModule} from "./yuiContracts/core/26-router/IIBCModule.sol";
import {IIBCHandler} from "./yuiContracts/core/25-handler/IIBCHandler.sol";

// TODO: Actionsのエラー回避のため一時的にIToken.solの実ファイルをimportしている。contract-sandboxnのdevelopブランチのIToken.solが最新化された場合は元に戻す
import "./interfaces/IIBCToken.sol";
import "./interfaces/IAccount.sol";
import "./interfaces/IAccessCtrl.sol";
import "./interfaces/IBalanceSyncBridge.sol";
import "./interfaces/Error.sol";
import "./interfaces/Struct.sol";

contract BalanceSyncBridge is Initializable, IBCAppBase, IBalanceSyncBridge {
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    IIBCHandler private _ibcHandler;
    IIBCToken private _ibcToken;
    IAccount private _account;
    IAccessCtrl private _accessCtrl;

    uint16 internal finZoneId;

    string public balanceSyncSourcePort;
    string public balanceSyncSourceChannel;
    string public balanceSyncSourceVersion;
    // TODO: 現状のIFでは、ブリッジ関数が送信先Zoneの情報を受け取っていないため利用不可
    // mapping (uint16 => string) private _zoneIdToChannel;

    /** @dev 外部署名の検証に使用する固定値 **/
    bytes32 private constant _STRING_BALANCE_SYNC = "balanceSync";

    ///////////////////////////////////
    // modifier
    ///////////////////////////////////

    modifier adminOnly(uint256 deadline, bytes memory signature) {
        bytes32 hash = keccak256(abi.encode(_STRING_BALANCE_SYNC, deadline));

        (bool has, string memory err) = _accessCtrl.checkAdminRole(hash, deadline, signature);
        require(bytes(err).length == 0, err);
        require(has, "not admin role");
        _;
    }

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。
     *
     * @param ibcHandler_ ibcHandler
     * @param ibcTokenAddr ibcToken
     * @param accountAddr account
     * @param accessCtrlAddr accessCtrl
     */
    function initialize(
        IIBCHandler ibcHandler_,
        address ibcTokenAddr,
        address accountAddr,
        address accessCtrlAddr
    ) public initializer {
        require(address(ibcHandler_) != address(0), Error.IBC_INVALID_VAL);
        require(ibcTokenAddr != address(0), Error.IBC_INVALID_VAL);
        require(accountAddr != address(0), Error.IBC_INVALID_VAL);
        require(accessCtrlAddr != address(0), Error.IBC_INVALID_VAL);
        _ibcHandler = ibcHandler_;
        _ibcToken = IIBCToken(ibcTokenAddr);
        _account = IAccount(accountAddr);
        _accessCtrl = IAccessCtrl(accessCtrlAddr);

        finZoneId = 3000;
        balanceSyncSourcePort = "balance-sync";
        balanceSyncSourceChannel = "channel-1";
        balanceSyncSourceVersion = "balance-sync-1";
    }

    /**
     * @dev コントラクトバージョン取得。
     *
     * @return version コントラクトバージョン
     */
    function version() external pure virtual returns (string memory) {
        return "v1";
    }

    function ibcAddress() public view virtual override returns (address) {
        return address(_ibcHandler);
    }

    function setAddress(
        IIBCToken ibcTokenAddr,
        IAccount accountAddr,
        IAccessCtrl accessCtrlAddr,
        uint256 deadline,
        bytes memory signature
    ) public adminOnly(deadline, signature) {
        _ibcToken = ibcTokenAddr;
        _account = accountAddr;
        _accessCtrl = accessCtrlAddr;
    }

    function setChannel(
        uint16 zoneId,
        string memory channel,
        uint256 deadline,
        bytes memory signature
    ) public adminOnly(deadline, signature) {
        // TODO: 現状のIFでは、ブリッジ関数が送信先Zoneの情報を受け取っていないためマッピングは利用不可なので、変数側を利用する
        // _zoneIdToChannel[zoneId] = channel;
        balanceSyncSourceChannel = channel;
    }

    /**
     * @dev FinZoneかどうかを確認する
     *
     * @param zoneId zoneId
     */
    function _isFinancialZone(uint16 zoneId) private view returns (bool) {
        return zoneId == finZoneId;
    }

    function getConfig() external view returns (Config memory config) {
        config = Config({
            port: balanceSyncSourcePort,
            channel: balanceSyncSourceChannel,
            version: balanceSyncSourceVersion
        });

        return config;
    }

    // TODO: チャネル情報とZone情報の管理・変更がある場合変更できる機能が必要
    // Admin権限で実装すること
    // setConfig()
    // setFinZoneId()

    /**
     * @dev zone内のtransferを実行する。bizzone内で実行された場合はfinzoneへ残高同期のpacketを送信する
     *
     * @param fromAccountId 送り元のアカウントID
     * @param toAccountId 送り先のアカウントID
     * @param fromZoneId packet送信元のzoneID
     * @param amount amount
     * @param timeoutHeight timeout までの Block高
     * @param traceId traceId
     */
    function syncTransfer(
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint16 fromZoneId,
        uint256 amount,
        uint64 timeoutHeight,
        bytes32 traceId
    ) external override returns (uint256) {
        AccountDataWithoutZoneId memory fromAccountData;
        (fromAccountData, ) = _account.getAccount(fromAccountId);

        AccountDataWithoutZoneId memory toAccountData;
        (toAccountData, ) = _account.getAccount(toAccountId);

        // TODO 別箇所で指定
        SyncBuisinessZoneBlanaceParams memory packetData = SyncBuisinessZoneBlanaceParams(
            fromAccountId,
            fromAccountData.accountName,
            toAccountId,
            toAccountData.accountName,
            fromZoneId,
            amount,
            traceId
        );

        bytes memory packet = abi.encode(packetData);

        return
            _ibcHandler.sendPacket(
                balanceSyncSourcePort,
                balanceSyncSourceChannel,
                Height.Data({revision_number: 0, revision_height: timeoutHeight}),
                0,
                packet
            );
    }

    /**
     * @dev relayerからpacketを受け取り、Financial Zoneで管理するBusiness Zoneのステータスを更新する
     *
     * @param packet packet
     */
    function onRecvPacket(Packet.Data calldata packet, address)
        external
        override
        returns (bytes memory acknowledgement)
    {
        require(packet.data.length > 0, Error.IBC_INVALID_VAL);

        // stuck too deep回避のため、structを利用する
        SyncBuisinessZoneBlanaceParams memory syncBuisinessZoneBlanaceParams = abi.decode(
            packet.data,
            (SyncBuisinessZoneBlanaceParams)
        );

        // 受け取ったpacketからfinancial zone管理のbusiness zoneのデータを更新する
        _ibcToken.syncBusinessZoneBalance(syncBuisinessZoneBlanaceParams);

        return bytes("success");
    }

    function onChanOpenInit(IIBCModule.MsgOnChanOpenInit calldata msg_)
        external
        virtual
        override
        onlyIBC
        returns (string memory)
    {
        require(
            bytes(msg_.version).length == 0 ||
                keccak256(bytes(msg_.version)) == keccak256(bytes(balanceSyncSourceVersion)),
            "version mismatch"
        );
        return balanceSyncSourceVersion;
    }

    function onChanOpenTry(IIBCModule.MsgOnChanOpenTry calldata msg_)
        external
        virtual
        override
        onlyIBC
        returns (string memory)
    {
        require(
            keccak256(bytes(msg_.counterpartyVersion)) ==
                keccak256(bytes(balanceSyncSourceVersion)),
            "version mismatch"
        );
        return balanceSyncSourceVersion;
    }

    /**
     * @dev リカバリーのためADMIN権限を利用してpacket receiveを再実行する
     *
     * @param packet packet
     */
    function recoverPacket(
        Packet.Data calldata packet,
        address,
        uint256 deadline,
        bytes memory signature
    ) external adminOnly(deadline, signature) {
        this.onRecvPacket(packet, address(0));
    }
}
