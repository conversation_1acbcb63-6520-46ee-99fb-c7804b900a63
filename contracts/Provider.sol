// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

import "./interfaces/IContractManager.sol";
import "./interfaces/Error.sol";
import "./remigration/RemigrationLib.sol";
import "./libraries/ProviderLib.sol";
import {ProviderData} from "./interfaces/Struct.sol";

/**
 * @dev Providerコントラクト
 */
contract Provider is Initializable, IProvider {
    ///////////////////////////////////
    // libraries
    ///////////////////////////////////

    using ProviderLib for *;

    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev ContractManagerアドレス */
    IContractManager private _contractManager;
    /** @dev プロバイダID */
    bytes32 private _providerId;
    /** @dev ゾーンIDリスト */
    uint16[] private _zoneIds;
    /** @dev プロバイダデータ */
    mapping(bytes32 => ProviderData) private _providerData;
    /** @dev ゾーンデータ */
    mapping(uint16 => ZoneData) private _zoneData;
    /** @dev getProviderAllのsignature検証用 **/
    string private constant _GET_PROVIDER_ALL_SIGNATURE = "getProviderAll";
    /* @dev setProviderAllのsignature検証用 */
    string private constant _SET_PROVIDER_ALL_SIGNATURE = "setProviderAll";
    /// @dev Providerロール計算用(calcRole()prefix用文字列(Provider権限))
    bytes32 public constant ROLE_PREFIX_PROV = keccak256("PROV_ROLE");

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。
     *
     * @param contractManager ContractManagerアドレス
     */
    function initialize(IContractManager contractManager) public initializer {
        _contractManager = contractManager;
    }

    /**
     * @dev コントラクトバージョン取得。
     *
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    ///////////////////////////////////
    // verify sender functions
    ///////////////////////////////////

    /**
     * @dev Admin権限を持つかチェックする。署名からEOAを復元し権限を持つかチェックする(内部関数)。
     *
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     */
    function _adminOnly(
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) internal view {
        (bool has, string memory err) = _contractManager.accessCtrl().checkAdminRole(
            hash,
            deadline,
            signature
        );
        require(bytes(err).length == 0, err);
        require(has, Error.PROV_NOT_ADMIN_ROLE);
    }

    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    /**
     * @dev Providerの追加。Adminの権限が必要。
     *
     * ```
     * emit event: AddProvider()
     * ```
     *
     * @param providerId providerID
     * @param zoneId zoneId
     * @param zoneName zoneName
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addProvider(
        bytes32 providerId,
        uint16 zoneId,
        string memory zoneName,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // Admin権限を持つかチェック
        {
            bytes32 hash = keccak256(abi.encode(providerId, zoneId, deadline));
            _adminOnly(hash, deadline, signature);
        }

        // provider未登録チェック
        require(providerId != 0x00, Error.PROV_INVALID_VAL);
        require(_providerId == 0x00, Error.PROV_ID_EXIST);

        _providerId = providerId;
        _zoneIds.push(zoneId);

        ProviderLib.addProvider(
            _providerData,
            _zoneData,
            providerId,
            _contractManager.accessCtrl().calcRole(ROLE_PREFIX_PROV, providerId),
            zoneId,
            zoneName
        );

        emit AddProvider(providerId, zoneId, zoneName, traceId);
    }

    /**
     * @dev (Fin専用)Finで管理を行うZone情報の追加。Adminの権限が必要。
     *
     * ```
     * emit event: AddBizZone()
     * ```
     *
     * @param zoneId zoneId
     * @param zoneName zoneName
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addBizZone(
        uint16 zoneId,
        string memory zoneName,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // Admin権限を持つかチェック
        {
            bytes32 hash = keccak256(abi.encode(zoneId, zoneName, deadline));
            _adminOnly(hash, deadline, signature);
        }

        // provider登録チェック
        require(_providerId != 0x00, Error.PROV_ID_NOT_EXIST);

        _zoneIds.push(zoneId);
        _zoneData[zoneId].zoneId = zoneId;
        _zoneData[zoneId].zoneName = zoneName;

        emit AddBizZone(zoneId, zoneName);
    }

    /**
     * @dev Provider権限の追加。Adminの権限が必要。
     *
     * ```
     * emit event: AddProviderRole()
     * ```
     *
     * @param providerId providerID
     * @param providerEoa providerEoa
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addProviderRole(
        bytes32 providerId,
        address providerEoa,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // Admin権限を持つかチェック
        {
            bytes32 hash = keccak256(abi.encode(providerId, providerEoa, deadline));
            _adminOnly(hash, deadline, signature);
        }

        // providerId有効性チェック
        {
            (bool success, string memory errTmp) = _hasProvider(providerId);
            require(success, errTmp);
        }

        require(providerEoa != address(0), Error.PROV_INVALID_VAL);

        _contractManager.accessCtrl().addRoleByProv(
            providerId,
            _providerData[providerId].role,
            providerEoa
        );

        emit AddProviderRole(providerId, providerEoa, traceId);
    }

    /**
     * @dev Provider名の更新。Adminの権限が必要。
     *
     * ```
     * emit event: ModProvider()
     * ```
     *
     * @param providerId providerID
     * @param name 更新後のproviderの名前
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function modProvider(
        bytes32 providerId,
        bytes32 name,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // Admin権限を持つかチェック
        {
            bytes32 hash = keccak256(abi.encode(providerId, name, deadline));
            _adminOnly(hash, deadline, signature);
        }

        // providerId有効性チェック
        {
            (bool success, string memory errTmp) = _hasProvider(providerId);
            require(success, errTmp);
        }

        _providerData.modProviderName(providerId, name);

        emit ModProvider(providerId, name, traceId);
    }

    /**
     * @dev Zone名の更新。Adminの権限が必要。
     *
     * ```
     * emit event: ModProvider()
     * ```
     *
     * @param providerId providerID
     * @param zoneName 更新後のzone名
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function modZone(
        bytes32 providerId,
        string memory zoneName,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external {
        // Admin権限を持つかチェック
        {
            bytes32 hash = keccak256(abi.encode(providerId, deadline));
            _adminOnly(hash, deadline, signature);
        }

        // providerId有効性チェック
        {
            (bool success, string memory errTmp) = _hasProvider(providerId);
            require(success, errTmp);
        }

        ProviderLib.modZoneName(_providerData, _zoneData, providerId, zoneName);

        emit ModZone(providerId, zoneName, traceId);
    }

    /**
     * @dev Tokenの追加。
     *
     * ```
     * emit event: AddTokenByProvider()
     * ```
     *
     * @param providerId providerID
     * @param tokenId tokenId
     * @param name トークン名
     * @param symbol symbol
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addToken(
        bytes32 providerId,
        bytes32 tokenId,
        bytes32 name,
        bytes32 symbol,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        //ProviderId有効性チェック
        {
            require(providerId == _providerId, Error.NOT_PROVIDER_ID);
            bytes32 hash = keccak256(abi.encode(providerId, tokenId, name, symbol, deadline));
            //権限チェック
            (bool has, string memory err) = _contractManager.accessCtrl().checkRole(
                _providerData[providerId].role,
                hash,
                deadline,
                signature
            );
            require(bytes(err).length == 0, err);
            require(has, Error.PROV_NOT_ROLE);
        }
        _contractManager.token().addToken(tokenId, name, symbol, traceId);

        emit AddTokenByProvider(providerId, tokenId, traceId);
    }

    /**
     * @dev Tokenのステータス変更。name, symbolのみ変更を許可し、空を許容しない。
     *
     * @param tokenId tokenId
     * @param name トークン名
     * @param symbol symbol
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function modToken(
        bytes32 tokenId,
        bytes32 name,
        bytes32 symbol,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // token登録チェック
        {
            (bool success, string memory errTmp) = _hasToken(tokenId, true);
            require(success, errTmp);
        }
        //権限チェック
        {
            bytes32 hash = keccak256(abi.encode(tokenId, name, symbol, deadline));
            (bool has, string memory err) = _contractManager.accessCtrl().checkRole(
                _providerData[_providerId].role,
                hash,
                deadline,
                signature
            );
            require(bytes(err).length == 0, err);
            require(has, Error.PROV_NOT_ROLE);
        }
        _contractManager.token().modToken(tokenId, name, symbol, traceId);
    }

    /**
     * @dev Biz ZoneとIssuerの紐付け。
     *
     * @param issuerId issuerId
     * @param zoneId zoneId
     */
    function addBizZoneToIssuer(bytes32 issuerId, uint16 zoneId) external override {
        // provider登録チェック
        require(_providerId != 0x00, Error.PROV_ID_NOT_EXIST);

        // zone登録チェック
        bool has = false;
        for (uint256 i = 0; i < _zoneIds.length; i++) {
            if (_zoneIds[i] == zoneId) {
                has = true;
            }
        }
        require(has, Error.ZONE_NOT_EXIST);

        // issuerId重複チェック
        bytes32[] memory issuerIds = this.getAvailableIssuerIds(zoneId);
        for (uint256 i = 0; i < issuerIds.length; i++) {
            require(issuerIds[i] != issuerId, Error.ISSUER_ID_EXIST);
        }

        _zoneData[zoneId].availableIssuerIds.push(issuerId);
    }

    /**
     * @dev Biz ZoneとIssuerの紐付け削除。
     *
     * @param issuerId issuerId
     * @param zoneId zoneId
     */
    function deleteBizZoneToIssuer(bytes32 issuerId, uint16 zoneId) external override {
        // provider登録チェック
        require(_providerId != 0x00, Error.PROV_ID_NOT_EXIST);

        // zone登録チェック
        bool has = false;
        for (uint256 i = 0; i < _zoneIds.length; i++) {
            if (_zoneIds[i] == zoneId) {
                has = true;
            }
        }
        require(has, Error.ZONE_NOT_EXIST);

        uint256 length = _zoneData[zoneId].availableIssuerIds.length;
        for (uint256 i = 0; i < length; i++) {
            if (_zoneData[zoneId].availableIssuerIds[i] == issuerId) {
                _zoneData[zoneId].availableIssuerIds[i] = _zoneData[zoneId].availableIssuerIds[
                    length - 1
                ];
                _zoneData[zoneId].availableIssuerIds.pop();
                break;
            }
        }
    }

    /**
     * @dev プロバイダ全情報登録
     * @param provider 全プロバイダの情報
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function setProviderAll(
        ProviderAll memory provider,
        uint256 deadline,
        bytes memory signature
    ) external {
        // Admin権限を持つかチェック
        {
            bytes32 hash = keccak256(abi.encode(_SET_PROVIDER_ALL_SIGNATURE, deadline));
            _adminOnly(hash, deadline, signature);
        }

        _providerId = provider.providerId;
        RemigrationLib.setProviderAll(
            _providerData[provider.providerId],
            _zoneIds,
            _zoneData,
            address(_contractManager),
            provider
        );
    }

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev Providerの存在確認。
     *
     * @param providerId providerID
     * @return success true:Providerが存在する,false:Providerが存在しない
     * @return err エラーメッセージ
     */
    function hasProvider(bytes32 providerId)
        external
        view
        override
        returns (bool success, string memory err)
    {
        return _hasProvider(providerId);
    }

    /**
     * @dev Providerの存在確認(内部関数)。
     *
     * @param providerId providerID
     * @return success true:Providerが存在する,false:Providerが存在しない
     * @return err エラーメッセージ
     */
    function _hasProvider(bytes32 providerId)
        internal
        view
        returns (bool success, string memory err)
    {
        return _providerId.hasProvider(providerId);
    }

    /**
     * @dev Tokenの存在確認。
     *
     * @param tokenId tokenId
     * @param providerId providerId
     * @param chkEnabled true:有効性確認を行う,false:有効性確認を行わない
     * @return success true:Tokenが存在し、有効,false:Tokenが存在しない、あるいは無効
     * @return err エラーメッセージ
     */
    function hasToken(
        bytes32 tokenId,
        bytes32 providerId,
        bool chkEnabled
    ) external view returns (bool success, string memory err) {
        //ProviderIDの有効性
        {
            if (providerId == 0x00) {
                return (false, Error.PROV_INVALID_VAL);
            } else if (_providerId != providerId) {
                return (false, Error.PROV_ID_NOT_EXIST);
            }
        }
        return _hasToken(tokenId, chkEnabled);
    }

    /**
     * @dev Tokenの存在確認(内部関数)。
     *
     * @param tokenId tokenId
     * @param chkEnabled true:有効性確認を行う,false:有効性確認を行わない
     * @return success true:Tokenが存在し、有効,false:Tokenが存在しない、あるいは無効
     * @return err エラーメッセージ
     */
    function _hasToken(bytes32 tokenId, bool chkEnabled)
        internal
        view
        returns (bool success, string memory err)
    {
        //TokenIDが未入力の場合エラー
        if (tokenId == 0x00) {
            return (false, Error.PROV_INVALID_VAL);
        }
        //Tokenの有効性チェック
        (success, err) = _contractManager.token().hasToken(tokenId, chkEnabled);
        if (success) {
            return (true, "");
        } else {
            return (false, err);
        }
    }

    /**
     * @dev Provider情報の取得。
     *
     * @return providerId providerId
     * @return zoneId zoneId
     * @return zoneName zoneName
     * @return err エラーメッセージ
     */
    function getProvider()
        external
        view
        override
        returns (
            bytes32 providerId,
            uint16 zoneId,
            string memory zoneName,
            string memory err
        )
    {
        if (_providerId == 0x00) {
            return (0x00, 0, "", Error.PROV_NOT_EXIST);
        }

        zoneId = _providerData[_providerId].zoneId;
        zoneName = _zoneData[zoneId].zoneName;

        return (_providerId, zoneId, zoneName, "");
    }

    /**
     * @dev zoneIDの取得。
     *
     * @return zoneId zoneID
     * @return zoneName zoneName
     * @return err エラーメッセージ
     */
    function getZone()
        external
        view
        override
        returns (
            uint16 zoneId,
            string memory zoneName,
            string memory err
        )
    {
        return ProviderLib.getZone(_providerData, _zoneData, _providerId);
    }

    /**
     * @dev zone名称の取得。
     *
     * @return zoneName zoneName
     */
    function getZoneName(uint16 zoneId) external view override returns (string memory zoneName) {
        return _zoneData[zoneId].zoneName;
    }

    /**
     * @dev TokenIDの取得。
     *
     * @return tokenId tokenID
     * @return err エラーメッセージ
     */
    function getTokenId() external view override returns (bytes32 tokenId, string memory err) {
        (tokenId, , , , , err) = _contractManager.token().getToken();
        return (tokenId, err);
    }

    /**
     * @dev Token情報の取得。
     *
     * @param providerId providerID
     * @return tokenId tokenId
     * @return name トークン名
     * @return symbol symbol
     * @return totalSupply 合計供給量
     * @return enabled true:Tokenが有効, false:Tokenが無効
     * @return err エラーメッセージ
     */
    function getToken(bytes32 providerId)
        external
        view
        override
        returns (
            bytes32 tokenId,
            bytes32 name,
            bytes32 symbol,
            uint256 totalSupply,
            bool enabled,
            string memory err
        )
    {
        // providerId有効性チェック
        {
            (bool success, string memory errTmp) = _hasProvider(providerId);
            if (!success) {
                // TODO: Refactor時に_EMPTY_xxxに修正
                return (0x00, 0x00, 0x00, 0, false, errTmp);
            }
        }
        return _contractManager.token().getToken();
    }

    /**
     * @dev Provider数の取得。
     *
     * @return count providerの数
     */
    function getProviderCount() external view override returns (uint256 count) {
        return _providerId == 0x00 ? 0 : 1;
    }

    /**
     * @dev 権限の確認。
     *
     * @param providerId providerID
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     * @return has true:ロールを所持,false:ロールを不所持
     * @return err エラーメッセージ
     */
    function checkRole(
        bytes32 providerId,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external view override returns (bool has, string memory err) {
        if (providerId == 0x00) {
            return (false, Error.PROV_INVALID_VAL);
        }
        // 権限チェック
        (has, err) = _contractManager.accessCtrl().checkRole(
            _providerData[providerId].role,
            hash,
            deadline,
            signature
        );
    }

    /**
     * @dev 認可イシュアを取得。
     *
     * @param zoneId zoneId
     * @return availableIssuerIds 認可イシュアリスト
     */
    function getAvailableIssuerIds(uint16 zoneId)
        external
        view
        override
        returns (bytes32[] memory availableIssuerIds)
    {
        return _zoneData[zoneId].availableIssuerIds;
    }

    /**
     * @dev 認可イシュアを確認。
     *
     * @param zoneId zoneId
     * @param accountId accountId
     * @return success 成功したかどうか
     * @return err エラーメッセージ
     */
    function checkAvailableIssuerIds(uint16 zoneId, bytes32 accountId)
        external
        view
        override
        returns (bool success, string memory err)
    {
        bytes32[] memory issuerIds = this.getAvailableIssuerIds(zoneId);
        if (issuerIds.length == 0) {
            return (false, Error.ISSUER_ID_NOT_EXIST);
        }
        for (uint256 i = 0; i < issuerIds.length; i++) {
            (success, err) = _contractManager.issuer().hasAccount(issuerIds[i], accountId);
            if (success) {
                return (true, "");
            }
        }
        return (false, err);
    }

    /**
     * @dev プロバイダ全情報取得
     *      既に登録されているプロバイダの全情報を取得する
     */
    function getProviderAll(bytes32 providerId)
        external
        view
        returns (ProviderAll memory provider)
    {
        provider = RemigrationLib.getProviderAll(
            _providerData[providerId],
            _zoneIds,
            _zoneData,
            address(_contractManager),
            _providerId
        );

        return (provider);
    }

    ///////////////////////////////////
    // for upgrade contracts
    // deprecated
    ///////////////////////////////////

    // uint256[50] private __gap;
}
