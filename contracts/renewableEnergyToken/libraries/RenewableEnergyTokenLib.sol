// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "@openzeppelin/contracts-upgradeable/utils/math/SafeMathUpgradeable.sol";

import "../../interfaces/RenewableEnergyTokenStruct.sol";
import "../../interfaces/Error.sol";
import "../../interfaces/IContractManager.sol";
import "./StringUtils.sol";

library RenewableEnergyTokenLib {
    uint256 private constant _MAX_LIMIT = 100;
    /** @dev 未登録の場合にて返す空の件数 **/
    uint256 private constant _EMPTY_LENGTH = 0;
    /** @dev 外部署名の検証に使用する固定値 **/
    bytes32 private constant _STRING_TRANSFER = "transfer";
    /** @dev ソート制御用の固定値(降順) */
    bytes32 private constant _DESC_SORT = keccak256(bytes("desc"));
    /** @dev ソート制御用の固定値(昇順) */
    bytes32 private constant _ASC_SORT = keccak256(bytes("asc"));

    /**
     * @dev renewableEnergyTokenDataMappingのマッピング内から、指定されたaccountIdがownerAccountId、mintAccountIdとなっているトークンのリストを取得する
     *
     * @param renewableEnergyTokenData renewableEnergyTokenDataMapping
     * @param tokenIds 全トークンIDのリスト
     * @param accountId 取得対象のアカウントID
     * @param offset 取得開始位置
     * @param limit 取得件数
     * @return tokenDataList 取得したトークンのリスト
     * @return totalCount 取得対象のトークン数
     * @return err エラーメッセージ
     */
    function getTokenList(
        mapping(bytes32 => RenewableEnergyTokenData) storage renewableEnergyTokenData,
        bytes32[] storage tokenIds,
        bytes32 accountId,
        uint256 offset,
        uint256 limit
    )
        external
        view
        returns (
            RenewableEnergyTokenListData[] memory tokenDataList,
            uint256,
            string memory
        )
    {
        // 動的配列をreturnすることが言語使用上できないため、最初に返却対象の配列のサイズを取得する = tokenIdsを総なめして、accountIdがownerAccountIdまたはmintAccountIdとなっているトークンの数をカウントする
        uint256 tokenCount = 0;
        for (uint256 i = 0; i < tokenIds.length; i++) {
            if (
                renewableEnergyTokenData[tokenIds[i]].ownerAccountId == accountId ||
                renewableEnergyTokenData[tokenIds[i]].mintAccountId == accountId
            ) {
                tokenCount++;
            }
        }

        // キーの長さがオフセットより小さい、またはリミットが0の場合は、空の配列を返す
        if (limit == 0 || tokenCount == 0) {
            return (tokenDataList, _EMPTY_LENGTH, "");
        }
        if (limit > _MAX_LIMIT) {
            return (tokenDataList, _EMPTY_LENGTH, Error.RETOKEN_TOO_LARGE_LIMIT);
        }
        if (offset >= tokenCount) {
            return (tokenDataList, tokenCount, Error.RETOKEN_OFFSET_OUT_OF_INDEX);
        }

        uint256 size = (tokenCount >= offset + limit) ? limit : tokenCount - offset;

        // カウント数に基づいて、tokenIdListのサイズを確保し、再度tokenIdsを総なめして、accountIdがownerAccountIdまたはmintAccountIdとなっているトークンのIDをtokenIdListに格納する
        bytes32[] memory tokenIdList = new bytes32[](tokenCount);
        uint256 tokenIndex = 0;
        for (uint256 i = 0; i < tokenIds.length; i++) {
            if (
                renewableEnergyTokenData[tokenIds[i]].ownerAccountId == accountId ||
                renewableEnergyTokenData[tokenIds[i]].mintAccountId == accountId
            ) {
                tokenIdList[tokenIndex] = tokenIds[i];
                tokenIndex++;
            }
        }

        // tokenIdListリストから、offsetとlimitに基づいて、tokenListを作成する
        tokenDataList = new RenewableEnergyTokenListData[](size);
        uint256 index = 0;
        for (uint256 i = offset; i < offset + size; i++) {
            bytes32 tokenId = tokenIdList[i];
            tokenDataList[index].tokenId = tokenId;
            tokenDataList[index].tokenStatus = renewableEnergyTokenData[tokenId].tokenStatus;
            tokenDataList[index].metadataId = renewableEnergyTokenData[tokenId].metadataId;
            tokenDataList[index].metadataHash = renewableEnergyTokenData[tokenId].metadataHash;
            tokenDataList[index].mintAccountId = renewableEnergyTokenData[tokenId].mintAccountId;
            tokenDataList[index].ownerAccountId = renewableEnergyTokenData[tokenId].ownerAccountId;
            tokenDataList[index].previousAccountId = renewableEnergyTokenData[tokenId]
                .previousAccountId;
            tokenDataList[index].isLocked = renewableEnergyTokenData[tokenId].isLocked;
            index++;
        }

        return (tokenDataList, tokenCount, "");
    }

    /**
     * @dev トークンIDの詳細情報を取得する
     *
     * @param renewableEnergyTokenDataMapping 再生可能エネルギートークンのデータを保存するマッピング
     * @param key キーとなるトークンID
     */
    function getToken(
        mapping(bytes32 => RenewableEnergyTokenData) storage renewableEnergyTokenDataMapping,
        bytes32 key
    )
        external
        view
        returns (RenewableEnergyTokenData memory renewableEnergyTokenData, string memory err)
    {
        if (renewableEnergyTokenDataMapping[key].tokenStatus == TokenStatus.Empty) {
            return (renewableEnergyTokenData, Error.RETOKEN_NOT_EXIST);
        }

        return (renewableEnergyTokenDataMapping[key], err);
    }

    /**
     * @dev トークンが存在するかどうかを確認する関数
     *
     * @param data 再生可能エネルギートークンのデータを保存するマッピング
     * @param tokenId 確認するトークンのID
     * @return bool トークンが存在する場合はtrue、存在しない場合はfalseを返す
     */
    function exists(mapping(bytes32 => RenewableEnergyTokenData) storage data, bytes32 tokenId)
        public
        view
        returns (bool)
    {
        return data[tokenId].tokenStatus != TokenStatus.Empty;
    }

    function addTokenId(bytes32[] storage tokenIds, bytes32 tokenId) external {
        tokenIds.push(tokenId);
    }

    /**
     * @dev トークンを追加する
     *
     * @param renewableEnergyTokenDataMapping 再生可能エネルギートークンのデータを保存するマッピング
     * @param key キーとなるトークンID
     * @param metadataId メタデータID
     * @param metadataHash メタデータハッシュ
     * @param mintAccountId 発行するアカウントID
     * @param ownerAccountId 所有者のアカウントID
     * @param isLocked トークンのロック状態
     */
    function addToken(
        mapping(bytes32 => RenewableEnergyTokenData) storage renewableEnergyTokenDataMapping,
        bytes32 key,
        bytes32 metadataId,
        bytes32 metadataHash,
        bytes32 mintAccountId,
        bytes32 ownerAccountId,
        bool isLocked
    ) external {
        renewableEnergyTokenDataMapping[key].tokenStatus = TokenStatus.Active;
        renewableEnergyTokenDataMapping[key].metadataId = metadataId;
        renewableEnergyTokenDataMapping[key].metadataHash = metadataHash;
        renewableEnergyTokenDataMapping[key].mintAccountId = mintAccountId;
        renewableEnergyTokenDataMapping[key].ownerAccountId = ownerAccountId;
        renewableEnergyTokenDataMapping[key].previousAccountId = mintAccountId;
        renewableEnergyTokenDataMapping[key].isLocked = isLocked;
    }

    /**
     * @dev AccountIdに紐づくトークンIDを追加する
     *
     * @param tokenIdsByAccountIdMapping 追加するトークンIDを保存するマッピング
     * @param accountId キーとなるアカウントID
     * @param tokenId 追加するトークンID
     */
    function addTokenIdToAccountId(
        mapping(bytes32 => bytes32[]) storage tokenIdsByAccountIdMapping,
        bytes32 accountId,
        bytes32 tokenId
    ) external {
        tokenIdsByAccountIdMapping[accountId].push(tokenId);
    }

    /**
     * @dev トークンを移転する
     *
     * @param renewableEnergyTokenDataMapping 再生可能エネルギートークンのデータを保存するマッピング
     * @param tokenId キーとなるトークンID
     * @param fromAccountId 移転もとのアカウントID
     * @param toAccountId 移転先のアカウントID
     */
    function transferToken(
        mapping(bytes32 => RenewableEnergyTokenData) storage renewableEnergyTokenDataMapping,
        mapping(bytes32 => bytes32[]) storage tokenIdsByAccountIdMapping,
        bytes32 tokenId,
        bytes32 fromAccountId,
        bytes32 toAccountId
    ) external {
        // NFTがアクティブでない、またはロック状態である場合はエラー
        RenewableEnergyTokenData memory data = renewableEnergyTokenDataMapping[tokenId];
        require(data.tokenStatus == TokenStatus.Active, Error.RETOKEN_NOT_ACTIVE);
        require(!data.isLocked, Error.RETOKEN_IS_LOCKED);
        require(data.ownerAccountId == fromAccountId, Error.RETOKEN_NOT_OWNER);

        // 移転元アカウントの該当トークン所有情報を削除する
        for (uint256 i = 0; i < tokenIdsByAccountIdMapping[fromAccountId].length; i++) {
            if (tokenIdsByAccountIdMapping[fromAccountId][i] == tokenId) {
                tokenIdsByAccountIdMapping[fromAccountId][i] = tokenIdsByAccountIdMapping[
                    fromAccountId
                ][tokenIdsByAccountIdMapping[fromAccountId].length - 1];
                tokenIdsByAccountIdMapping[fromAccountId].pop();
                break;
            }
        }
        renewableEnergyTokenDataMapping[tokenId].ownerAccountId = toAccountId;
        renewableEnergyTokenDataMapping[tokenId].previousAccountId = fromAccountId;
        tokenIdsByAccountIdMapping[toAccountId].push(tokenId);
    }

    /**
     * @dev トークンを移転する
     *
     * @param renewableEnergyTokenDataMapping 再生可能エネルギートークンのデータを保存するマッピング
     * @param tokenIds キーとなるトークンID
     * @param fromAccountId 移転もとのアカウントID
     * @param toAccountId 移転先のアカウントID
     */
    function transferBatchTokens(
        mapping(bytes32 => RenewableEnergyTokenData) storage renewableEnergyTokenDataMapping,
        mapping(bytes32 => bytes32[]) storage tokenIdsByAccountIdMapping,
        string[] memory tokenIds,
        bytes32 fromAccountId,
        bytes32 toAccountId
    ) external {
        for (uint256 i; i < tokenIds.length; i++) {
            bytes32 tokenId = StringUtils.stringToBytes32(tokenIds[i]);
            // NFTがアクティブでない、またはロック状態である場合はエラー
            RenewableEnergyTokenData memory data = renewableEnergyTokenDataMapping[tokenId];
            require(data.tokenStatus == TokenStatus.Active, Error.RETOKEN_NOT_EXIST);
            require(!data.isLocked, Error.RETOKEN_IS_LOCKED);
            require(data.ownerAccountId == fromAccountId, Error.RETOKEN_NOT_OWNER);

            // 移転元アカウントの該当トークン所有情報を削除する
            for (uint256 j = 0; j < tokenIdsByAccountIdMapping[fromAccountId].length; j++) {
                if (tokenIdsByAccountIdMapping[fromAccountId][j] == tokenId) {
                    tokenIdsByAccountIdMapping[fromAccountId][j] = tokenIdsByAccountIdMapping[
                        fromAccountId
                    ][tokenIdsByAccountIdMapping[fromAccountId].length - 1];
                    tokenIdsByAccountIdMapping[fromAccountId].pop();
                    break;
                }
            }
            renewableEnergyTokenDataMapping[tokenId].ownerAccountId = toAccountId;
            renewableEnergyTokenDataMapping[tokenId].previousAccountId = fromAccountId;
            tokenIdsByAccountIdMapping[toAccountId].push(tokenId);
        }
    }

    /**
     * @dev トークンが移転可能かどうかチェックする
     *
     * @param contractManagerAddr コントラクトマネージャーアドレス
     * @param renewableEnergyTokenData 再生可能エネルギートークンのデータを保存するマッピング
     * @param sendAccountId 送信者のアカウントID
     * @param fromAccountId 移転元のアカウントID
     * @param toAccountId 移転先のアカウントID
     * @param miscValue1 miscValue1
     * @param miscValue2Arrays miscVlue2
     * @return success true:OK,false:NG
     * @return err エラーメッセージ
     */
    function checkTransaction(
        address contractManagerAddr,
        mapping(bytes32 => RenewableEnergyTokenData) storage renewableEnergyTokenData,
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        bytes32 miscValue1,
        string[] memory miscValue2Arrays
    ) external view returns (bool success, string memory err) {
        IContractManager contractManager = IContractManager(contractManagerAddr);
        // miscValue1の値がrenewableでない場合はエラー
        {
            if (miscValue1 != bytes32("renewable")) {
                return (false, Error.RETOKEN_INVALID_MISC1);
            }
        }
        // miscValue2に含まれる要素数が100を超える場合はエラー
        {
            if (miscValue2Arrays.length >= 100) {
                return (false, Error.RETOKEN_INVALID_MISC2);
            }
        }
        // sendAccountIdのステータス確認
        {
            // sendAccount有効性確認
            (success, err) = contractManager.account().isActivated(sendAccountId);
            if (!success) {
                return (success, err);
            }
        }
        // fromAccountIdのステータス確認
        {
            // fromAccount有効性確認
            (success, err) = contractManager.account().isActivated(fromAccountId);
            if (!success) {
                return (success, err);
            }
        }
        // toAccountIdのステータス確認
        {
            // toAccount有効性確認
            (success, err) = contractManager.account().isActivated(toAccountId);
            if (!success) {
                return (success, err);
            }
        }
        // fromAccountIdとtoAccountIdが同一の場合はエラー
        {
            if (fromAccountId == toAccountId) {
                return (false, Error.RETOKEN_FROM_TO_ARE_SAME);
            }
        }
        // miscValue2で指定された各要素が32bytesであり、全トークンが存在し、fromAccountIdが所有しているか確認
        {
            for (uint256 i; i < miscValue2Arrays.length; i++) {
                if (bytes(miscValue2Arrays[i]).length > 32) {
                    return (false, Error.RETOKEN_INVALID_MISC2);
                }
                bytes32 tokenId = StringUtils.stringToBytes32(miscValue2Arrays[i]);
                // if (tokenId.length != 32) {
                //     return (false, Error.RETOKEN_INVALID_MISC2);
                // }
                if (renewableEnergyTokenData[tokenId].tokenStatus != TokenStatus.Active) {
                    return (false, Error.RETOKEN_NOT_EXIST);
                }
                if (renewableEnergyTokenData[tokenId].isLocked) {
                    return (false, Error.RETOKEN_IS_LOCKED);
                }
                if (renewableEnergyTokenData[tokenId].ownerAccountId != fromAccountId) {
                    return (false, Error.RETOKEN_NOT_OWNER);
                }
            }
        }
        return (true, "");
    }

    /**
     * @dev 指定されたtokenIdに紐づくToken情報を登録、もしくは上書きする
     *
     * @param renewableEnergyTokenDataMapping Tokenデータを保存するマッピング
     * @param tokenIdsArray TokenIDを保存するArray
     * @param token Token情報
     */
    function setRenewableEnergyTokenAll(
        mapping(bytes32 => RenewableEnergyTokenData) storage renewableEnergyTokenDataMapping,
        bytes32[] storage tokenIdsArray,
        RenewableEnergyTokenAll memory token
    ) external {
        bytes32 tokenId = token.tokenId;
        tokenIdsArray.push(tokenId);
        renewableEnergyTokenDataMapping[tokenId].tokenStatus = token
            .renewableEnergyTokenData
            .tokenStatus;
        renewableEnergyTokenDataMapping[tokenId].metadataId = token
            .renewableEnergyTokenData
            .metadataId;
        renewableEnergyTokenDataMapping[tokenId].metadataHash = token
            .renewableEnergyTokenData
            .metadataHash;
        renewableEnergyTokenDataMapping[tokenId].mintAccountId = token
            .renewableEnergyTokenData
            .mintAccountId;
        renewableEnergyTokenDataMapping[tokenId].ownerAccountId = token
            .renewableEnergyTokenData
            .ownerAccountId;
        renewableEnergyTokenDataMapping[tokenId].previousAccountId = token
            .renewableEnergyTokenData
            .previousAccountId;
        renewableEnergyTokenDataMapping[tokenId].isLocked = token.renewableEnergyTokenData.isLocked;
    }

    /**
     * @dev 指定されたTokenIdに紐づくToken情報を登録、もしくは上書きする
     *      バックアップリスト作業時のみ実行
     *
     * @param renewableEnergyTokenDataMapping Tokenデータを保存するマッピング
     * @param tokenIdsArray TokenIDを保存するArray
     * @param index 取得tokensのindex
     */
    function getRenewableEnergyTokenAll(
        mapping(bytes32 => RenewableEnergyTokenData) storage renewableEnergyTokenDataMapping,
        bytes32[] storage tokenIdsArray,
        uint256 index
    ) external view returns (RenewableEnergyTokenAll memory renewableEnergyToken) {
        bytes32 tokenId = tokenIdsArray[index];
        RenewableEnergyTokenAll memory token;
        token.tokenId = tokenId;
        token.renewableEnergyTokenData.tokenStatus = renewableEnergyTokenDataMapping[tokenId]
            .tokenStatus;
        token.renewableEnergyTokenData.metadataId = renewableEnergyTokenDataMapping[tokenId]
            .metadataId;
        token.renewableEnergyTokenData.metadataHash = renewableEnergyTokenDataMapping[tokenId]
            .metadataHash;
        token.renewableEnergyTokenData.mintAccountId = renewableEnergyTokenDataMapping[tokenId]
            .mintAccountId;
        token.renewableEnergyTokenData.ownerAccountId = renewableEnergyTokenDataMapping[tokenId]
            .ownerAccountId;
        token.renewableEnergyTokenData.previousAccountId = renewableEnergyTokenDataMapping[tokenId]
            .previousAccountId;
        token.renewableEnergyTokenData.isLocked = renewableEnergyTokenDataMapping[tokenId].isLocked;
        renewableEnergyToken = token;

        return (renewableEnergyToken);
    }

    /**
     * @dev 指定されたaccountIdに紐づくTokenIdを取得
     * @param tokenIdsByAccountIdMapping accountIdに紐づくTokenIdマッピング
     * @param accountId accountId
     */
    function getTokenIdsByAccountIdAll(
        mapping(bytes32 => bytes32[]) storage tokenIdsByAccountIdMapping,
        bytes32 accountId
    ) external view returns (TokenIdsByAccountIdAll memory tokenIdsByAccountId) {
        tokenIdsByAccountId.accountId = accountId;
        tokenIdsByAccountId.tokenIds = tokenIdsByAccountIdMapping[accountId];

        return (tokenIdsByAccountId);
    }

    /**
     * @dev 指定されたaccountIdに紐づくTokenIdを登録、もしくは上書きする
     * @param tokenIdsByAccountIdMapping accountIdに紐づくTokenIdのマッピング
     * @param tokenIdByAccountId accountIdに紐づくTokenId
     */
    function setTokenIdsByAccountIdAll(
        mapping(bytes32 => bytes32[]) storage tokenIdsByAccountIdMapping,
        TokenIdsByAccountIdAll memory tokenIdByAccountId
    ) external {
        bytes32 accountId = tokenIdByAccountId.accountId;
        tokenIdsByAccountIdMapping[accountId] = tokenIdByAccountId.tokenIds;
    }
}
