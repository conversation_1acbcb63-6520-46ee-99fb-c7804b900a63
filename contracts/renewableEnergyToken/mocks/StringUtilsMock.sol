// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../libraries/StringUtils.sol";

contract StringUtilsMock {
    using StringUtils for string;

    function stringToBytes32(string memory source) public pure returns (bytes32) {
        return source.stringToBytes32();
    }

    function slice(string memory source, string memory delimiter)
        public
        pure
        returns (string[] memory)
    {
        return source.slice(delimiter);
    }

    function substring(
        string memory str,
        uint256 startIndex,
        uint256 endIndex
    ) public pure returns (string memory) {
        return StringUtils.substring(str, startIndex, endIndex);
    }
}
