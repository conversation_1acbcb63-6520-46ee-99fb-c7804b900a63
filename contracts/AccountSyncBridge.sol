// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

import {Height} from "./yuiContracts/proto/Client.sol";
import {Packet} from "./yuiContracts/proto/Channel.sol";
import {IBCAppBase} from "./yuiContracts/apps/commons/IBCAppBase.sol";
import {IIBCModule} from "./yuiContracts/core/26-router/IIBCModule.sol";
import {IIBCHandler} from "./yuiContracts/core/25-handler/IIBCHandler.sol";

import "./interfaces/IValidator.sol";
import "./interfaces/Error.sol";
import "./interfaces/Struct.sol";
import "./interfaces/IAccessCtrl.sol";
import "./interfaces/IBusinessZoneAccount.sol";
import "./interfaces/IIBCToken.sol";
import "./interfaces/IProvider.sol";

contract AccountSyncBridge is Initializable, IBCAppBase {
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    IIBCHandler private _ibcHandler;
    IValidator private _validator;
    IAccessCtrl private _accessCtrl;
    IBusinessZoneAccount private _businessZoneAccount;
    IIBCToken private _ibcToken;
    IProvider private _provider;

    uint16 internal finZoneId;

    string public accountSyncSourcePort;
    string public accountSyncSourceChannel;
    string public accountSyncSourceVersion;
    // TODO: 現状のIFでは、ブリッジ関数が送信先Zoneの情報を受け取っていないため利用不可
    // mapping (uint16 => string) private _zoneIdToChannel;

    /* @dev AccountStatus判定用の定数(口座申し込み) */
    bytes32 private constant _STATUS_APPLIYNG = "applying";
    /* @dev AccountStatus判定用の定数(口座解約申し込み) */
    bytes32 private constant _STATUS_TERMINATING = "terminating";
    /** @dev 外部署名の検証に使用する固定値 **/
    bytes32 private constant _STRING_ACCOUNT_SYNC = "accountSync";

    ///////////////////////////////////
    // modifier
    ///////////////////////////////////

    modifier adminOnly(uint256 deadline, bytes memory signature) {
        bytes32 hash = keccak256(abi.encode(_STRING_ACCOUNT_SYNC, deadline));

        (bool has, string memory err) = _accessCtrl.checkAdminRole(hash, deadline, signature);
        require(bytes(err).length == 0, err);
        require(has, "not admin role");
        _;
    }

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。
     *
     * @param ibcHandler_ ibcHandler
     * @param providerAddr providerAddr
     * @param validatorAddr validator
     * @param accessCtrlAddr accessCtrl
     * @param businessZoneAccountAddr businessZoneAccount
     * @param ibcTokenAddr ibcToken
     */
    function initialize(
        IIBCHandler ibcHandler_,
        address providerAddr,
        address validatorAddr,
        address accessCtrlAddr,
        address businessZoneAccountAddr,
        address ibcTokenAddr
    ) public initializer {
        require(address(ibcHandler_) != address(0), Error.IBC_INVALID_VAL);
        require(providerAddr != address(0), Error.IBC_INVALID_VAL);
        require(validatorAddr != address(0), Error.IBC_INVALID_VAL);
        require(accessCtrlAddr != address(0), Error.IBC_INVALID_VAL);
        require(businessZoneAccountAddr != address(0), Error.IBC_INVALID_VAL);
        require(ibcTokenAddr != address(0), Error.IBC_INVALID_VAL);
        _ibcHandler = ibcHandler_;
        _provider = IProvider(providerAddr);
        _validator = IValidator(validatorAddr);
        _accessCtrl = IAccessCtrl(accessCtrlAddr);
        _businessZoneAccount = IBusinessZoneAccount(businessZoneAccountAddr);
        _ibcToken = IIBCToken(ibcTokenAddr);

        finZoneId = 3000;
        accountSyncSourcePort = "account-sync";
        accountSyncSourceChannel = "channel-0";
        accountSyncSourceVersion = "account-sync-1";
    }

    /**
     * @dev コントラクトバージョン取得。
     *
     * @return version コントラクトバージョン
     */
    function version() external pure virtual returns (string memory) {
        return "v1";
    }

    function ibcAddress() public view virtual override returns (address) {
        return address(_ibcHandler);
    }

    function setAddress(
        IProvider providerAddr,
        IValidator validatorAddr,
        IAccessCtrl accessCtrlAddr,
        IBusinessZoneAccount businessZoneAccountAddr,
        IIBCToken ibcTokenAddr,
        uint256 deadline,
        bytes memory signature
    ) public adminOnly(deadline, signature) {
        _provider = providerAddr;
        _validator = validatorAddr;
        _accessCtrl = accessCtrlAddr;
        _businessZoneAccount = businessZoneAccountAddr;
        _ibcToken = ibcTokenAddr;
    }

    function setChannel(
        uint16 zoneId,
        string memory channel,
        uint256 deadline,
        bytes memory signature
    ) public adminOnly(deadline, signature) {
        // TODO: 現状のIFでは、ブリッジ関数が送信先Zoneの情報を受け取っていないためマッピングは利用不可なので、変数側を利用する
        // _zoneIdToChannel[zoneId] = channel;
        accountSyncSourceChannel = channel;
    }

    /**
     * @dev FinZoneかどうかを確認する
     *
     * @param zoneId zoneId
     */
    function _isFinancialZone(uint16 zoneId) private view returns (bool) {
        return zoneId == finZoneId;
    }

    function getConfig() external view returns (Config memory config) {
        config = Config({
            port: accountSyncSourcePort,
            channel: accountSyncSourceChannel,
            version: accountSyncSourceVersion
        });

        return config;
    }

    function getPort() external view returns (string memory port) {
        return accountSyncSourcePort;
    }

    // TODO: チャネル情報とZone情報の管理・変更がある場合変更できる機能が必要
    // Admin権限で実装すること
    // setConfig()
    // setFinZoneId()

    // TODO: exchange the parameters timeoutHeight and deadline
    function syncAccount(
        bytes32 validatorId,
        bytes32 accountId,
        string memory accountName,
        uint16 fromZoneId,
        string memory zoneName, // TODO: syncAccount時のzoneNameは不要となったため削除する(Coreと平仄を合わせること)
        bytes32 accountStatus,
        bytes32 reasonCode,
        uint256 approvalAmount,
        bytes32 traceId,
        uint64 timeoutHeight
    ) external returns (uint256) {
        // 実行ゾーンがfinancial zoneであればpacket送信は行わない
        require(!_isFinancialZone(fromZoneId), Error.FIN_ZONE_OP_NOT_ALLOWED);

        require(
            accountStatus == _STATUS_APPLIYNG || accountStatus == _STATUS_TERMINATING,
            Error.IBC_INVALID_VAL
        );

        // 開設申込時にアカウントが存在する場合、アカウントの残高を初期化する
        // 上記以外の場合、Zone内でアカウントの申し込みを行う(Business Zone専用)
        {
            (bool success, ) = _validator.hasAccount(validatorId, accountId);
            if (accountStatus == _STATUS_APPLIYNG && success) {
                _ibcToken.initAccountBalance(accountId);
            } else {
                _validator.syncAccount(
                    validatorId,
                    accountId,
                    accountName,
                    fromZoneId,
                    zoneName,
                    accountStatus,
                    reasonCode,
                    approvalAmount,
                    traceId
                );
            }
        }

        SyncBusinessZoneAccountParams memory packetData = SyncBusinessZoneAccountParams({
            accountId: accountId,
            accountName: accountName,
            fromZoneId: fromZoneId,
            fromZoneName: zoneName,
            accountStatus: accountStatus,
            traceId: traceId
        });

        // 実行ゾーンがfinancial zone以外であれば、financial zone向けにpacketを送信する
        bytes memory packet = abi.encode(packetData);

        return
            _ibcHandler.sendPacket(
                accountSyncSourcePort,
                accountSyncSourceChannel,
                Height.Data({revision_number: 0, revision_height: timeoutHeight}),
                0,
                packet
            );
    }

    /**
     * @dev relayerからpacketを受け取り、Financial Zoneで管理するBusiness Zoneのステータスを更新する
     *
     * @param packet packet
     */
    function onRecvPacket(Packet.Data calldata packet, address)
        external
        override
        returns (bytes memory)
    {
        require(packet.data.length > 0, Error.IBC_INVALID_VAL);

        // stuck too deep回避のため、structを利用する
        SyncBusinessZoneAccountParams memory packetData;

        packetData = abi.decode(packet.data, (SyncBusinessZoneAccountParams));

        // 送り元のzoneIdがfinancial zoneでないことを確認する
        require(!_isFinancialZone(packetData.fromZoneId), Error.NOT_ALLOWED_FROM_FIN_ZONE);

        // 認可イシュアを確認
        (bool success, string memory err) = _provider.checkAvailableIssuerIds(
            packetData.fromZoneId,
            packetData.accountId
        );
        require(success, err);

        // 受け取ったpacketからfinancial zone管理のbusiness zoneのデータを更新する
        _businessZoneAccount.syncBusinessZoneStatus(
            packetData.fromZoneId,
            packetData.fromZoneName,
            packetData.accountId,
            packetData.accountName,
            packetData.accountStatus,
            packetData.traceId
        );
        return bytes("success");
    }

    /**
     * @dev packet受け取り後の処理
     *
     * @param packet packet
     */
    function onAcknowledgementPacket(
        Packet.Data calldata packet,
        bytes calldata,
        address
    ) external virtual override onlyIBC {
        // stuck too deep回避のため、structを利用する
        SyncBusinessZoneAccountParams memory packetData;

        packetData = abi.decode(packet.data, (SyncBusinessZoneAccountParams));

        // TODO: Ackが成功したことをどのように検知するか検討が必要
    }

    function onChanOpenInit(IIBCModule.MsgOnChanOpenInit calldata msg_)
        external
        view
        override
        onlyIBC
        returns (string memory)
    {
        require(
            bytes(msg_.version).length == 0 ||
                keccak256(bytes(msg_.version)) == keccak256(bytes(accountSyncSourceVersion)),
            "version mismatch"
        );
        return accountSyncSourceVersion;
    }

    function onChanOpenTry(IIBCModule.MsgOnChanOpenTry calldata msg_)
        external
        view
        override
        onlyIBC
        returns (string memory)
    {
        require(
            keccak256(bytes(msg_.counterpartyVersion)) ==
                keccak256(bytes(accountSyncSourceVersion)),
            "version mismatch"
        );
        return accountSyncSourceVersion;
    }

    /**
     * @dev リカバリーのためADMIN権限を利用してpacket receiveを再実行する
     *
     * @param packet packet
     */
    function recoverPacket(
        Packet.Data calldata packet,
        address,
        uint256 deadline,
        bytes memory signature
    ) external adminOnly(deadline, signature) {
        this.onRecvPacket(packet, address(0));
    }
}
