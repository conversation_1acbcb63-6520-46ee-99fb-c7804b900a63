// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "../interfaces/IProvider.sol";
import "../interfaces/Struct.sol";

contract ProviderMock is IProvider {
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    ///////////////////////////////////
    // verify sender functions
    ///////////////////////////////////

    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    function addProvider(
        bytes32,
        uint16,
        string memory,
        bytes32,
        uint256,
        bytes memory
    ) external pure override {
        revert("unused override only");
    }

    function addBizZone(
        uint16,
        string memory,
        uint256,
        bytes memory
    ) external pure override {
        revert("unused override only");
    }

    function addProviderRole(
        bytes32,
        address,
        bytes32,
        uint256,
        bytes memory
    ) external pure override {
        revert("unused override only");
    }

    function modProvider(
        bytes32,
        bytes32,
        bytes32,
        uint256,
        bytes memory
    ) external pure override {
        revert("unused override only");
    }

    function addToken(
        bytes32,
        bytes32,
        bytes32,
        bytes32,
        bytes32,
        uint256,
        bytes memory
    ) external pure override {
        revert("unused override only");
    }

    function modToken(
        bytes32,
        bytes32,
        bytes32,
        bytes32,
        uint256,
        bytes memory
    ) external pure override {
        revert("unused override only");
    }

    function addBizZoneToIssuer(bytes32, uint16) external pure override {
        revert("unused override only");
    }

    function deleteBizZoneToIssuer(bytes32, uint16) external pure override {
        revert("unused override only");
    }

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    function hasProvider(bytes32) external pure override returns (bool, string memory) {
        revert("unused override only");
    }

    function getProvider()
        external
        pure
        override
        returns (
            bytes32,
            uint16,
            string memory,
            string memory
        )
    {
        revert("unused override only");
    }

    function getZone()
        external
        pure
        override
        returns (
            uint16,
            string memory,
            string memory
        )
    {
        revert("unused override only");
    }

    function getZoneName(uint16) external pure override returns (string memory) {
        revert("unused override only");
    }

    function getTokenId() external pure override returns (bytes32, string memory) {
        revert("unused override only");
    }

    function getToken(bytes32)
        external
        pure
        override
        returns (
            bytes32,
            bytes32,
            bytes32,
            uint256,
            bool,
            string memory
        )
    {
        revert("unused override only");
    }

    function getProviderCount() external pure override returns (uint256) {
        revert("unused override only");
    }

    function checkRole(
        bytes32,
        bytes32,
        uint256,
        bytes memory
    ) external pure override returns (bool, string memory) {
        revert("unused override only");
    }

    function getAvailableIssuerIds(uint16) external pure override returns (bytes32[] memory) {
        revert("unused override only");
    }

    function checkAvailableIssuerIds(uint16, bytes32)
        external
        pure
        override
        returns (bool, string memory)
    {
        return (true, "");
    }

    function setProviderAll(
        ProviderAll memory,
        uint256,
        bytes memory
    ) external pure override {
        revert("unused override only");
    }

    function getProviderAll(bytes32) external view returns (ProviderAll memory) {
        revert("unused override only");
    }
}
