// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "../interfaces/IAccount.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

contract AccountMock is IAccount {
    bytes32 private constant _STATUS_ACTIVE = "active";

    ///////////////////////////////////
    // Mock functions
    ///////////////////////////////////

    // IDより対象Accountのステータスを出力する
    function getAccount(bytes32)
        external
        pure
        override
        returns (AccountDataWithoutZoneId memory accountData, string memory err)
    {
        accountData = AccountDataWithoutZoneId({
            accountName: "accountName",
            accountStatus: _STATUS_ACTIVE,
            balance: 0,
            reasonCode: 0,
            appliedAt: 0,
            registeredAt: 0,
            terminatingAt: 0,
            terminatedAt: 0
        });
        return (accountData, "");
    }

    // 共通領域 Account登録
    function addAccount(
        bytes32,
        string memory,
        bytes32
    ) external pure override {
        revert("unused override only");
    }

    //AccountのRole追加
    function addAccountRole(
        bytes32,
        address,
        bytes32
    ) external pure override {
        revert("unused override only");
    }

    // @dev アカウント名変更
    function modAccount(
        bytes32,
        string memory,
        bytes32
    ) external pure override {
        revert("unused override only");
    }

    //Accountの有効化/凍結
    function setAccountStatus(
        bytes32,
        bytes32,
        bytes32,
        bytes32
    ) external pure override {
        revert("unused override only");
    }

    //Accountの解約
    function setTerminated(
        bytes32,
        bytes32,
        bytes32
    ) external pure override {
        revert("unused override only");
    }

    // 連携済みzone情報の追加
    function addZone(
        bytes32,
        uint16,
        bytes32
    ) external pure override {
        revert("unused override only");
    }

    //Mint
    // 呼び出し元のTokenで残高をemitするため、残高のreturnを行う
    function mint(bytes32, uint256) external pure override returns (uint256) {
        revert("unused override only");
    }

    //Burn
    // 呼び出し元のTokenで残高をemitするため、残高のreturnを行う
    function burn(bytes32, uint256) external pure override returns (uint256) {
        revert("unused override only");
    }

    //transferFrom
    function calcBalance(
        bytes32,
        bytes32,
        uint256
    ) external pure override returns (uint256, uint256) {
        revert("unused override only");
    }

    // Allowanceの減額を行う
    function calcAllowance(
        bytes32,
        bytes32,
        uint256
    ) external pure override {
        revert("unused override only");
    }

    //editBalance
    function editBalance(
        bytes32,
        uint256,
        uint256
    ) external pure override returns (uint256) {
        revert("unused override only");
    }

    //Approve
    function approve(
        bytes32,
        bytes32,
        uint256
    ) external pure override {
        revert("unused override only");
    }

    // AccountのBalanceを強制償却
    function forceBurn(bytes32, bytes32) external pure override {
        revert("unused override only");
    }

    function partialForceBurn(
        bytes32 accountId,
        uint256 burnedAmount,
        uint256 burnedBalance,
        bytes32 traceId
    ) external pure override {
        revert("unused override only");
    }

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    //AccountID存在確認
    function hasAccount(bytes32) external pure override returns (bool, string memory) {
        revert("unused override only");
    }

    //Accountの有効性確認
    function isActivated(bytes32) external pure override returns (bool, string memory) {
        revert("unused override only");
    }

    //IndexよりAccountIDを出力する
    function getAccountId(uint256) external pure override returns (bytes32, string memory) {
        revert("unused override only");
    }

    //IDより対象アカウントの全データを主力する
    function getAccountAll(bytes32)
        external
        pure
        override
        returns (AccountDataAll memory, string memory)
    {
        revert("unused override only");
    }

    // 移転先のアカウント情報を取得する
    function getDestinationAccount(bytes32)
        external
        pure
        override
        returns (string memory, string memory)
    {
        revert("unused override only");
    }

    //IDより対象Accountに紐づけられているValiadtorIDを取得する
    function getValidatorIdByAccountId(bytes32)
        external
        pure
        override
        returns (bytes32, string memory)
    {
        revert("unused override only");
    }

    // IDより対象Accountの連携済みゾーン情報を出力する
    function getZoneByAccountId(bytes32) external pure override returns (ZoneData[] memory) {
        revert("unused override only");
    }

    // アカウントの限度額情報を取得する
    function getAccountLimit(bytes32, bytes32)
        external
        pure
        override
        returns (FinancialZoneAccountData memory, string memory)
    {
        revert("unused override only");
    }

    //総Account数
    function getAccountCount() external pure override returns (uint256) {
        revert("unused override only");
    }

    //Accountの解約確認
    function isTerminated(bytes32) external pure override returns (bool, string memory) {
        revert("unused override only");
    }

    function getAllowance(bytes32, bytes32)
        external
        pure
        override
        returns (
            uint256,
            uint256,
            string memory
        )
    {
        revert("unused override only");
    }

    function getAllowanceList(
        bytes32,
        uint256,
        uint256
    )
        external
        pure
        override
        returns (
            AccountApprovalAll[] memory,
            uint256,
            string memory
        )
    {
        revert("unused override only");
    }

    // AccountのBalance取得
    function balanceOf(bytes32) external pure override returns (uint256, string memory) {
        revert("unused override only");
    }

    // Accountの凍結状態を確認
    function isFrozen(bytes32) external pure override returns (bool, string memory) {
        revert("unused override only");
    }

    function getAccountsAll(uint256) external pure override returns (AccountsAll memory) {
        revert("unused override only");
    }

    function setAccountAll(
        AccountsAll memory,
        uint256,
        bytes memory
    ) external pure override {
        revert("unused override only");
    }

    function emitAfterBalance(
        bytes32,
        bytes32,
        bytes32
    ) external pure override {
        revert("unused override only");
    }
}
